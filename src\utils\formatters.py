#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统数据格式化工具模块

此模块提供各种数据格式化功能，包括：
1. 数值格式化
2. 时间格式化
3. 交易数据格式化
4. AI响应格式化
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
import json


class NumberFormatter:
    """数值格式化器"""
    
    @staticmethod
    def format_price(price: Union[int, float], precision: int = 4) -> str:
        """格式化价格
        
        Args:
            price: 价格值
            precision: 小数位数
        
        Returns:
            str: 格式化后的价格字符串
        """
        if price is None:
            return "N/A"
        
        # 使用Decimal进行精确计算
        decimal_price = Decimal(str(price))
        format_str = f"{{:.{precision}f}}"
        return format_str.format(float(decimal_price))
    
    @staticmethod
    def format_amount(amount: Union[int, float], precision: int = 6) -> str:
        """格式化数量
        
        Args:
            amount: 数量值
            precision: 小数位数
        
        Returns:
            str: 格式化后的数量字符串
        """
        if amount is None:
            return "N/A"
        
        decimal_amount = Decimal(str(amount))
        format_str = f"{{:.{precision}f}}"
        formatted = format_str.format(float(decimal_amount))
        
        # 移除尾随零
        if '.' in formatted:
            formatted = formatted.rstrip('0').rstrip('.')
        
        return formatted
    
    @staticmethod
    def format_percentage(value: Union[int, float], precision: int = 2) -> str:
        """格式化百分比
        
        Args:
            value: 百分比值
            precision: 小数位数
        
        Returns:
            str: 格式化后的百分比字符串
        """
        if value is None:
            return "N/A"
        
        format_str = f"{{:+.{precision}f}}%"
        return format_str.format(value)
    
    @staticmethod
    def format_pnl(pnl: Union[int, float], currency: str = "USDT", precision: int = 2) -> str:
        """格式化盈亏
        
        Args:
            pnl: 盈亏值
            currency: 币种
            precision: 小数位数
        
        Returns:
            str: 格式化后的盈亏字符串
        """
        if pnl is None:
            return "N/A"
        
        format_str = f"{{:+.{precision}f}} {currency}"
        return format_str.format(pnl)
    
    @staticmethod
    def format_large_number(number: Union[int, float], precision: int = 2) -> str:
        """格式化大数值（使用K、M、B等单位）
        
        Args:
            number: 数值
            precision: 小数位数
        
        Returns:
            str: 格式化后的数值字符串
        """
        if number is None:
            return "N/A"
        
        abs_number = abs(number)
        sign = "-" if number < 0 else ""
        
        if abs_number >= 1_000_000_000:
            formatted = f"{sign}{abs_number / 1_000_000_000:.{precision}f}B"
        elif abs_number >= 1_000_000:
            formatted = f"{sign}{abs_number / 1_000_000:.{precision}f}M"
        elif abs_number >= 1_000:
            formatted = f"{sign}{abs_number / 1_000:.{precision}f}K"
        else:
            formatted = f"{sign}{abs_number:.{precision}f}"
        
        return formatted


class TimeFormatter:
    """时间格式化器"""
    
    @staticmethod
    def format_timestamp(timestamp: Union[int, float], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化时间戳
        
        Args:
            timestamp: 时间戳（毫秒或秒）
            format_str: 格式字符串
        
        Returns:
            str: 格式化后的时间字符串
        """
        if timestamp is None:
            return "N/A"
        
        # 判断是毫秒还是秒
        if timestamp > 1e10:  # 毫秒
            dt = datetime.fromtimestamp(timestamp / 1000)
        else:  # 秒
            dt = datetime.fromtimestamp(timestamp)
        
        return dt.strftime(format_str)
    
    @staticmethod
    def format_duration(seconds: Union[int, float]) -> str:
        """格式化持续时间
        
        Args:
            seconds: 秒数
        
        Returns:
            str: 格式化后的持续时间字符串
        """
        if seconds is None:
            return "N/A"
        
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        elif seconds < 86400:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
        else:
            days = seconds / 86400
            return f"{days:.1f}天"
    
    @staticmethod
    def get_relative_time(timestamp: Union[int, float]) -> str:
        """获取相对时间
        
        Args:
            timestamp: 时间戳（毫秒或秒）
        
        Returns:
            str: 相对时间字符串
        """
        if timestamp is None:
            return "N/A"
        
        # 判断是毫秒还是秒
        if timestamp > 1e10:  # 毫秒
            dt = datetime.fromtimestamp(timestamp / 1000)
        else:  # 秒
            dt = datetime.fromtimestamp(timestamp)
        
        now = datetime.now()
        diff = now - dt
        
        if diff.total_seconds() < 60:
            return "刚刚"
        elif diff.total_seconds() < 3600:
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}分钟前"
        elif diff.total_seconds() < 86400:
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}小时前"
        else:
            days = int(diff.total_seconds() / 86400)
            return f"{days}天前"


class TradingFormatter:
    """交易数据格式化器"""
    
    @staticmethod
    def format_symbol(symbol: str) -> str:
        """格式化交易对符号
        
        Args:
            symbol: 交易对符号
        
        Returns:
            str: 格式化后的交易对符号
        """
        if not symbol:
            return "N/A"
        
        # 移除多余的冒号部分（如 BTC/USDT:USDT -> BTC/USDT）
        if ':' in symbol:
            return symbol.split(':')[0]
        return symbol
    
    @staticmethod
    def format_side(side: str) -> str:
        """格式化交易方向
        
        Args:
            side: 交易方向
        
        Returns:
            str: 格式化后的交易方向
        """
        side_map = {
            "long": "做多",
            "short": "做空",
            "buy": "买入",
            "sell": "卖出"
        }
        return side_map.get(side.lower(), side)
    
    @staticmethod
    def format_order_status(status: str) -> str:
        """格式化订单状态
        
        Args:
            status: 订单状态
        
        Returns:
            str: 格式化后的订单状态
        """
        status_map = {
            "pending": "待处理",
            "open": "已开仓",
            "closed": "已平仓",
            "canceled": "已取消",
            "rejected": "已拒绝"
        }
        return status_map.get(status.lower(), status)
    
    @staticmethod
    def format_position_summary(position_data: Dict[str, Any]) -> str:
        """格式化持仓摘要
        
        Args:
            position_data: 持仓数据字典
        
        Returns:
            str: 格式化后的持仓摘要
        """
        symbol = TradingFormatter.format_symbol(position_data.get("symbol", ""))
        side = TradingFormatter.format_side(position_data.get("side", ""))
        amount = NumberFormatter.format_amount(position_data.get("amount", 0))
        pnl = NumberFormatter.format_pnl(position_data.get("unrealized_pnl", 0))
        pnl_pct = NumberFormatter.format_percentage(position_data.get("unrealized_pnl_percentage", 0))
        
        return f"{symbol} {side} {amount} | {pnl} ({pnl_pct})"


class AIFormatter:
    """AI响应格式化器"""
    
    @staticmethod
    def format_ai_action(action: str) -> str:
        """格式化AI动作
        
        Args:
            action: AI动作
        
        Returns:
            str: 格式化后的AI动作
        """
        action_map = {
            "open_long": "开多仓",
            "open_short": "开空仓",
            "close_position": "平仓",
            "hold": "持有",
            "no_action": "无操作",
            "adjust_stop_loss": "调整止损",
            "take_profit": "止盈"
        }
        return action_map.get(action.lower(), action)
    
    @staticmethod
    def format_confidence_level(confidence: Union[int, float]) -> str:
        """格式化置信度级别
        
        Args:
            confidence: 置信度值
        
        Returns:
            str: 格式化后的置信度级别
        """
        if confidence is None:
            return "N/A"
        
        if confidence >= 90:
            return f"{confidence}% (极高)"
        elif confidence >= 80:
            return f"{confidence}% (高)"
        elif confidence >= 70:
            return f"{confidence}% (中等)"
        elif confidence >= 60:
            return f"{confidence}% (较低)"
        else:
            return f"{confidence}% (低)"
    
    @staticmethod
    def format_ai_decision_summary(decision_data: Dict[str, Any]) -> str:
        """格式化AI决策摘要
        
        Args:
            decision_data: AI决策数据字典
        
        Returns:
            str: 格式化后的AI决策摘要
        """
        action = AIFormatter.format_ai_action(decision_data.get("action", ""))
        confidence = AIFormatter.format_confidence_level(decision_data.get("confidence", 0))
        symbol = TradingFormatter.format_symbol(decision_data.get("symbol", ""))
        
        return f"{symbol}: {action} (置信度: {confidence})"


class JSONFormatter:
    """JSON格式化器"""
    
    @staticmethod
    def format_json(data: Any, indent: int = 2, ensure_ascii: bool = False) -> str:
        """格式化JSON数据
        
        Args:
            data: 要格式化的数据
            indent: 缩进空格数
            ensure_ascii: 是否确保ASCII编码
        
        Returns:
            str: 格式化后的JSON字符串
        """
        try:
            return json.dumps(data, indent=indent, ensure_ascii=ensure_ascii, default=str)
        except Exception as e:
            return f"JSON格式化失败: {e}"
    
    @staticmethod
    def format_compact_json(data: Any) -> str:
        """格式化紧凑JSON数据
        
        Args:
            data: 要格式化的数据
        
        Returns:
            str: 紧凑的JSON字符串
        """
        try:
            return json.dumps(data, separators=(',', ':'), ensure_ascii=False, default=str)
        except Exception as e:
            return f"JSON格式化失败: {e}"


# =============================================================================
# 便捷函数
# =============================================================================

def format_trading_summary(data: Dict[str, Any]) -> Dict[str, str]:
    """格式化交易摘要数据
    
    Args:
        data: 交易数据字典
    
    Returns:
        Dict[str, str]: 格式化后的交易摘要
    """
    return {
        "symbol": TradingFormatter.format_symbol(data.get("symbol", "")),
        "side": TradingFormatter.format_side(data.get("side", "")),
        "amount": NumberFormatter.format_amount(data.get("amount", 0)),
        "price": NumberFormatter.format_price(data.get("price", 0)),
        "pnl": NumberFormatter.format_pnl(data.get("pnl", 0)),
        "pnl_percentage": NumberFormatter.format_percentage(data.get("pnl_percentage", 0)),
        "timestamp": TimeFormatter.format_timestamp(data.get("timestamp", 0))
    }


def format_system_status(data: Dict[str, Any]) -> Dict[str, str]:
    """格式化系统状态数据
    
    Args:
        data: 系统状态数据字典
    
    Returns:
        Dict[str, str]: 格式化后的系统状态
    """
    return {
        "status": data.get("status", "unknown"),
        "uptime": TimeFormatter.format_duration(data.get("uptime", 0)),
        "total_balance": NumberFormatter.format_price(data.get("total_balance", 0)),
        "available_balance": NumberFormatter.format_price(data.get("available_balance", 0)),
        "total_pnl": NumberFormatter.format_pnl(data.get("total_pnl", 0)),
        "active_positions": str(data.get("active_positions", 0)),
        "last_update": TimeFormatter.get_relative_time(data.get("last_update", 0))
    }
