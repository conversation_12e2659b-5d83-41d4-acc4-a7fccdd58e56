#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步市场数据引擎测试

专门测试异步功能。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.market_data_engine import MarketDataEngine
from src.data.models import ExchangeConfig


async def test_async_market_data():
    """测试异步市场数据功能"""
    print("=== 异步市场数据引擎测试 ===")
    
    # 创建配置
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建交易所客户端
    exchange_client = ExchangeClient(config)
    
    try:
        # 连接交易所
        print("1. 连接交易所...")
        exchange_client.connect()
        print("✅ 交易所连接成功")
        
        # 创建市场数据引擎
        print("\n2. 创建市场数据引擎...")
        symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT"]
        engine = MarketDataEngine(exchange_client, symbols)
        print(f"✅ 市场数据引擎创建成功，监控 {len(symbols)} 个交易对")
        
        # 测试异步获取多时间周期数据
        print("\n3. 测试异步获取多时间周期数据...")
        symbol = "BTC/USDT:USDT"
        
        start_time = asyncio.get_event_loop().time()
        timeframe_data = await engine.fetch_multi_timeframe_data(symbol)
        end_time = asyncio.get_event_loop().time()
        
        print(f"异步获取耗时: {end_time - start_time:.2f}秒")
        print(f"获取到 {len(timeframe_data)} 个时间周期的数据:")
        
        for timeframe, ohlcv_list in timeframe_data.items():
            print(f"  {timeframe}: {len(ohlcv_list)} 条K线数据")
            if ohlcv_list:
                latest = ohlcv_list[-1]
                print(f"    最新: {latest.datetime} - 收盘价: {latest.close}")
        
        print("✅ 异步多时间周期数据获取成功")
        
        # 测试并发获取多个交易对数据
        print("\n4. 测试并发获取多个交易对数据...")
        
        start_time = asyncio.get_event_loop().time()
        
        # 创建并发任务
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(engine.fetch_multi_timeframe_data(symbol))
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        
        end_time = asyncio.get_event_loop().time()
        
        print(f"并发获取耗时: {end_time - start_time:.2f}秒")
        print(f"成功获取 {len(results)} 个交易对的数据")
        
        for i, (symbol, result) in enumerate(zip(symbols, results)):
            print(f"  {symbol}: {len(result)} 个时间周期")
            for timeframe, ohlcv_list in result.items():
                print(f"    {timeframe}: {len(ohlcv_list)} 条数据")
        
        print("✅ 并发数据获取成功")
        
        # 测试短时间轮询（只运行一轮）
        print("\n5. 测试短时间轮询...")
        
        # 启动轮询
        await engine.start_data_polling(interval=3)
        print("轮询已启动")
        
        # 等待一轮轮询完成
        await asyncio.sleep(8)  # 等待足够时间让轮询运行
        
        # 检查数据状态
        status = engine.get_data_status()
        print("轮询后数据状态:")
        for symbol_name, symbol_status in status.items():
            print(f"  {symbol_name}: 最后更新时间 {symbol_status['last_update']}")
            for tf, tf_status in symbol_status['timeframes'].items():
                print(f"    {tf}: {tf_status['count']} 条数据")
        
        # 停止轮询
        await engine.stop_data_polling()
        print("轮询已停止")
        
        print("✅ 轮询测试完成")
        
        # 测试异步资源清理
        print("\n6. 测试异步资源清理...")
        
        # 确保所有异步任务都已完成
        pending_tasks = [task for task in asyncio.all_tasks() if not task.done()]
        if pending_tasks:
            print(f"等待 {len(pending_tasks)} 个待完成任务...")
            await asyncio.gather(*pending_tasks, return_exceptions=True)
        
        print("✅ 异步资源清理完成")
        
        print("\n🎉 所有异步测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        try:
            if engine.is_running:
                await engine.stop_data_polling()
        except:
            pass
        
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


async def main():
    """主函数"""
    success = await test_async_market_data()
    if success:
        print("\n✅ 异步市场数据引擎测试全部通过！")
    else:
        print("\n❌ 异步市场数据引擎测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    # 运行异步测试
    asyncio.run(main())
