# DeepSeek加密货币永续合约全自动量化系统依赖包

# 核心交易库
ccxt>=4.0.0                    # 统一交易所API接口库
pandas>=2.0.0                  # 数据处理和分析
numpy>=1.24.0                  # 数值计算

# 技术分析库
TA-Lib>=0.4.25                 # 技术分析指标计算库

# AI和HTTP客户端
httpx>=0.25.0                  # 异步HTTP客户端，用于DeepSeek API调用
openai>=1.0.0                  # OpenAI兼容的API客户端

# Web框架和API
fastapi>=0.104.0               # 现代Web框架
uvicorn>=0.24.0                # ASGI服务器
jinja2>=3.1.0                  # 模板引擎
python-multipart>=0.0.6       # 文件上传支持

# 数据库和存储
# sqlite3                      # SQLite数据库（Python内置，无需安装）
cryptography>=41.0.0           # 加密解密库

# 配置和环境
python-dotenv>=1.0.0           # 环境变量管理
pydantic>=2.0.0                # 数据验证和设置管理
pydantic-settings>=2.0.0       # Pydantic设置管理

# 日志和监控
loguru>=0.7.0                  # 现代日志库

# 异步和并发
# asyncio                      # 异步编程（Python内置，无需安装）
aiofiles>=23.0.0               # 异步文件操作

# 测试框架
pytest>=7.4.0                 # 测试框架
pytest-asyncio>=0.21.0        # 异步测试支持
pytest-cov>=4.1.0             # 测试覆盖率

# 开发工具
black>=23.0.0                 # 代码格式化
flake8>=6.0.0                 # 代码检查
mypy>=1.5.0                   # 类型检查

# 数据类和类型提示
dataclasses                    # 数据类（Python内置）
typing-extensions>=4.7.0      # 类型提示扩展

# 时间和日期处理
python-dateutil>=2.8.0        # 日期时间工具

# JSON处理
orjson>=3.9.0                 # 高性能JSON库
