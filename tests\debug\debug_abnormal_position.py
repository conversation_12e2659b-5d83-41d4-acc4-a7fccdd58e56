#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常持仓详细调试工具

深入分析异常的BTC/USDT持仓数据。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


def debug_abnormal_position():
    """调试异常持仓"""
    print("=== 异常持仓详细调试工具 ===")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    
    try:
        # 连接交易所
        exchange_client.connect()
        
        # 获取持仓信息
        print("\n1. 获取详细持仓信息...")
        positions = exchange_client.fetch_positions()
        
        # 找到异常的BTC/USDT持仓
        btc_usdt_position = None
        for pos in positions:
            if pos.symbol == "BTC/USDT":
                btc_usdt_position = pos
                break
        
        if not btc_usdt_position:
            print("❌ 未找到BTC/USDT持仓")
            return False
        
        print(f"\n2. 异常持仓详细分析: {btc_usdt_position.symbol}")
        print(f"=== 基本信息 ===")
        print(f"交易对: {btc_usdt_position.symbol}")
        print(f"方向: {btc_usdt_position.side.value}")
        print(f"数量: {btc_usdt_position.amount}")
        print(f"开仓价格: {btc_usdt_position.entry_price}")
        print(f"当前价格: {btc_usdt_position.current_price}")
        print(f"杠杆: {btc_usdt_position.leverage}x")
        
        print(f"\n=== 盈亏信息 ===")
        print(f"未实现盈亏: {btc_usdt_position.unrealized_pnl}")
        print(f"未实现盈亏%: {btc_usdt_position.unrealized_pnl_percentage:.4f}%")
        
        print(f"\n=== 保证金信息 ===")
        print(f"已用保证金: {btc_usdt_position.margin_used}")
        
        print(f"\n=== 敞口计算分析 ===")
        amount = btc_usdt_position.amount
        price = btc_usdt_position.current_price
        margin = btc_usdt_position.margin_used
        leverage = btc_usdt_position.leverage
        
        # 不同的敞口计算方法
        exposure_method1 = abs(amount) * price
        exposure_method2 = margin * leverage
        
        print(f"方法1 (数量×价格): {exposure_method1:,.2f} USDT")
        print(f"方法2 (保证金×杠杆): {exposure_method2:,.2f} USDT")
        print(f"差异: {abs(exposure_method1 - exposure_method2):,.2f} USDT")
        print(f"差异倍数: {exposure_method1 / exposure_method2:.1f}x")
        
        print(f"\n=== 合理性检查 ===")
        
        # 检查保证金是否合理
        if leverage > 0:
            implied_position_value = margin * leverage
            implied_amount = implied_position_value / price
            print(f"基于保证金推算的合理持仓数量: {implied_amount:.6f} BTC")
            print(f"实际持仓数量: {amount} BTC")
            print(f"数量差异倍数: {abs(amount) / implied_amount:.1f}x")
        
        # 检查是否是单位问题
        print(f"\n=== 单位转换分析 ===")
        
        # 可能的单位转换
        possible_units = [
            ("Satoshi", 1e-8),
            ("mBTC", 1e-3),
            ("μBTC", 1e-6),
            ("BTC", 1.0),
            ("合约张数", None)
        ]
        
        for unit_name, multiplier in possible_units:
            if multiplier:
                converted_amount = amount * multiplier
                converted_exposure = converted_amount * price
                print(f"如果数量单位是{unit_name}: {converted_amount:.8f} BTC, 敞口: {converted_exposure:,.2f} USDT")
                
                # 检查是否与保证金×杠杆接近
                if abs(converted_exposure - exposure_method2) / exposure_method2 < 0.01:  # 1%误差内
                    print(f"  ✅ 与保证金×杠杆方法接近！可能的正确单位")
        
        # 获取市场信息验证
        print(f"\n3. 获取市场信息验证...")
        markets = exchange_client.fetch_markets()
        
        btc_usdt_market = None
        for market in markets:
            if market.get('symbol') == 'BTC/USDT':
                btc_usdt_market = market
                break
        
        if btc_usdt_market:
            print(f"找到市场信息:")
            print(f"  合约大小: {btc_usdt_market.get('contractSize')}")
            print(f"  合约价值: {btc_usdt_market.get('contractValue')}")
            
            info = btc_usdt_market.get('info', {})
            print(f"  合约面值: {info.get('ctVal')}")
            print(f"  合约乘数: {info.get('ctMult')}")
            print(f"  面值币种: {info.get('ctValCcy')}")
            print(f"  最小下单量: {info.get('minSz')}")
            print(f"  下单数量精度: {info.get('lotSz')}")
            
            # 使用合约规格重新计算
            contract_size = btc_usdt_market.get('contractSize', 1)
            if contract_size:
                corrected_exposure = abs(amount) * contract_size * price
                print(f"\n使用合约规格计算:")
                print(f"  修正后敞口: {corrected_exposure:,.2f} USDT")
                print(f"  与保证金×杠杆比较: {abs(corrected_exposure - exposure_method2):,.2f} USDT")
        
        # 建议解决方案
        print(f"\n4. 建议解决方案:")
        print(f"基于分析，建议:")
        
        if exposure_method2 < 50000:  # 保证金×杠杆小于5万，比较合理
            print(f"1. ✅ 使用保证金×杠杆方法: {exposure_method2:,.2f} USDT")
            print(f"2. ❌ 忽略异常的数量×价格方法: {exposure_method1:,.2f} USDT")
            print(f"3. 🔧 可能需要清理这个异常持仓数据")
        else:
            print(f"1. ⚠️  所有方法都显示异常，建议手动检查")
        
        # 检查是否可以平仓
        print(f"\n5. 检查是否可以平仓...")
        try:
            # 注意：这里只是检查，不实际执行
            print(f"持仓信息:")
            print(f"  可平仓数量: {abs(amount)}")
            print(f"  建议: 如果这是异常数据，可以尝试平仓清理")
        except Exception as e:
            print(f"检查平仓失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print(f"\n🧹 连接已断开")


if __name__ == "__main__":
    success = debug_abnormal_position()
    if success:
        print(f"\n✅ 异常持仓调试完成！")
    else:
        print(f"\n❌ 异常持仓调试失败！")
        sys.exit(1)
