#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统数据模型定义模块

此模块定义了系统中使用的所有数据模型，包括：
1. 市场数据模型
2. 交易数据模型
3. AI决策模型
4. 配置数据模型
5. 系统状态模型
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from enum import Enum
import json


class TradingSide(Enum):
    """交易方向枚举"""
    LONG = "long"
    SHORT = "short"
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """订单类型枚举"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"
    OPEN = "open"
    CLOSED = "closed"
    CANCELED = "canceled"
    REJECTED = "rejected"


class AIActionType(Enum):
    """AI动作类型枚举"""
    OPEN_LONG = "open_long"
    OPEN_SHORT = "open_short"
    CLOSE_POSITION = "close_position"
    HOLD = "hold"
    NO_ACTION = "no_action"
    ADJUST_STOP_LOSS = "adjust_stop_loss"
    TAKE_PROFIT = "take_profit"


class SystemStatus(Enum):
    """系统状态枚举"""
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


# =============================================================================
# 市场数据模型
# =============================================================================

@dataclass
class OHLCVData:
    """OHLCV数据模型"""
    timestamp: int                     # 时间戳（毫秒）
    open: float                        # 开盘价
    high: float                        # 最高价
    low: float                         # 最低价
    close: float                       # 收盘价
    volume: float                      # 成交量
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "timestamp": self.timestamp,
            "open": self.open,
            "high": self.high,
            "low": self.low,
            "close": self.close,
            "volume": self.volume
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OHLCVData':
        """从字典创建实例"""
        return cls(
            timestamp=data["timestamp"],
            open=data["open"],
            high=data["high"],
            low=data["low"],
            close=data["close"],
            volume=data["volume"]
        )
    
    @property
    def datetime(self) -> datetime:
        """获取时间戳对应的datetime对象"""
        return datetime.fromtimestamp(self.timestamp / 1000)


@dataclass
class TechnicalIndicators:
    """技术指标数据模型"""
    symbol: str                        # 交易对
    timeframe: str                     # 时间周期
    trend_indicators: Dict[str, float] = field(default_factory=dict)      # 趋势指标
    oscillator_indicators: Dict[str, float] = field(default_factory=dict) # 震荡指标
    volatility_indicators: Dict[str, float] = field(default_factory=dict) # 波动率指标
    volume_indicators: Dict[str, float] = field(default_factory=dict)     # 成交量指标
    support_resistance: Dict[str, float] = field(default_factory=dict)    # 支撑阻力
    calculated_at: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "trend_indicators": self.trend_indicators,
            "oscillator_indicators": self.oscillator_indicators,
            "volatility_indicators": self.volatility_indicators,
            "volume_indicators": self.volume_indicators,
            "support_resistance": self.support_resistance,
            "calculated_at": self.calculated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TechnicalIndicators':
        """从字典创建实例"""
        return cls(
            symbol=data["symbol"],
            timeframe=data["timeframe"],
            trend_indicators=data.get("trend_indicators", {}),
            oscillator_indicators=data.get("oscillator_indicators", {}),
            volatility_indicators=data.get("volatility_indicators", {}),
            volume_indicators=data.get("volume_indicators", {}),
            support_resistance=data.get("support_resistance", {}),
            calculated_at=data.get("calculated_at", int(datetime.now().timestamp() * 1000))
        )


@dataclass
class MarketData:
    """市场数据模型"""
    symbol: str                        # 交易对
    timeframes: Dict[str, List[OHLCVData]] = field(default_factory=dict)  # 多时间周期数据
    last_update: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))
    
    def add_ohlcv_data(self, timeframe: str, ohlcv_list: List[OHLCVData]):
        """添加OHLCV数据"""
        self.timeframes[timeframe] = ohlcv_list
        self.last_update = int(datetime.now().timestamp() * 1000)
    
    def get_latest_price(self, timeframe: str = "1m") -> Optional[float]:
        """获取最新价格"""
        if timeframe in self.timeframes and self.timeframes[timeframe]:
            return self.timeframes[timeframe][-1].close
        return None
    
    def get_data_count(self, timeframe: str) -> int:
        """获取指定时间周期的数据数量"""
        return len(self.timeframes.get(timeframe, []))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "symbol": self.symbol,
            "timeframes": {
                tf: [ohlcv.to_dict() for ohlcv in data_list]
                for tf, data_list in self.timeframes.items()
            },
            "last_update": self.last_update
        }


# =============================================================================
# 交易数据模型
# =============================================================================

@dataclass
class Position:
    """持仓数据模型"""
    symbol: str                        # 交易对
    side: TradingSide                  # 持仓方向
    amount: float                      # 持仓数量
    entry_price: float                 # 开仓价格
    current_price: float               # 当前价格
    leverage: int                      # 杠杆倍数
    unrealized_pnl: float              # 未实现盈亏
    unrealized_pnl_percentage: float   # 未实现盈亏百分比
    margin_used: float                 # 已用保证金
    created_at: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))
    updated_at: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))

    def update_current_price(self, new_price: float):
        """更新当前价格并重新计算盈亏"""
        self.current_price = new_price
        self.updated_at = int(datetime.now().timestamp() * 1000)

        # 计算未实现盈亏
        if self.side in [TradingSide.LONG, TradingSide.BUY]:
            price_diff = new_price - self.entry_price
        else:
            price_diff = self.entry_price - new_price

        self.unrealized_pnl = price_diff * self.amount * self.leverage
        self.unrealized_pnl_percentage = (price_diff / self.entry_price) * 100 * self.leverage

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "symbol": self.symbol,
            "side": self.side.value,
            "amount": self.amount,
            "entry_price": self.entry_price,
            "current_price": self.current_price,
            "leverage": self.leverage,
            "unrealized_pnl": self.unrealized_pnl,
            "unrealized_pnl_percentage": self.unrealized_pnl_percentage,
            "margin_used": self.margin_used,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Position':
        """从字典创建实例"""
        return cls(
            symbol=data["symbol"],
            side=TradingSide(data["side"]),
            amount=data["amount"],
            entry_price=data["entry_price"],
            current_price=data["current_price"],
            leverage=data["leverage"],
            unrealized_pnl=data["unrealized_pnl"],
            unrealized_pnl_percentage=data["unrealized_pnl_percentage"],
            margin_used=data["margin_used"],
            created_at=data.get("created_at", int(datetime.now().timestamp() * 1000)),
            updated_at=data.get("updated_at", int(datetime.now().timestamp() * 1000))
        )


@dataclass
class ProfitDrawdown:
    """利润回撤数据模型"""
    max_profit_rate: float             # 历史最高收益率
    current_profit_rate: float         # 当前收益率
    drawdown_amount: float             # 回撤幅度
    drawdown_percentage: float         # 回撤比例

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "max_profit_rate": self.max_profit_rate,
            "current_profit_rate": self.current_profit_rate,
            "drawdown_amount": self.drawdown_amount,
            "drawdown_percentage": self.drawdown_percentage
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProfitDrawdown':
        """从字典创建实例"""
        return cls(
            max_profit_rate=data["max_profit_rate"],
            current_profit_rate=data["current_profit_rate"],
            drawdown_amount=data["drawdown_amount"],
            drawdown_percentage=data["drawdown_percentage"]
        )


@dataclass
class Order:
    """订单数据模型"""
    id: str                            # 订单ID
    symbol: str                        # 交易对
    side: TradingSide                  # 交易方向
    amount: float                      # 交易数量
    price: Optional[float]             # 价格（限价单）
    order_type: OrderType              # 订单类型
    status: OrderStatus                # 订单状态
    leverage: int = 1                  # 杠杆倍数
    filled_amount: float = 0.0         # 已成交数量
    average_price: Optional[float] = None  # 平均成交价格
    created_at: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))
    updated_at: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "side": self.side.value,
            "amount": self.amount,
            "price": self.price,
            "order_type": self.order_type.value,
            "status": self.status.value,
            "leverage": self.leverage,
            "filled_amount": self.filled_amount,
            "average_price": self.average_price,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Order':
        """从字典创建实例"""
        return cls(
            id=data["id"],
            symbol=data["symbol"],
            side=TradingSide(data["side"]),
            amount=data["amount"],
            price=data.get("price"),
            order_type=OrderType(data["order_type"]),
            status=OrderStatus(data["status"]),
            leverage=data.get("leverage", 1),
            filled_amount=data.get("filled_amount", 0.0),
            average_price=data.get("average_price"),
            created_at=data.get("created_at", int(datetime.now().timestamp() * 1000)),
            updated_at=data.get("updated_at", int(datetime.now().timestamp() * 1000))
        )


# =============================================================================
# AI决策数据模型
# =============================================================================

@dataclass
class AIDecision:
    """AI决策数据模型"""
    action: AIActionType               # AI动作
    confidence: int                    # 置信度（0-100）
    reasoning: str                     # 推理说明
    timestamp: int                     # 决策时间戳
    symbol: str                        # 交易对
    engine_type: str                   # 引擎类型（opening/position）
    suggested_leverage: Optional[int] = None  # 建议杠杆倍数
    risk_level: Optional[str] = None   # 风险级别
    additional_data: Dict[str, Any] = field(default_factory=dict)  # 额外数据

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "action": self.action.value,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "timestamp": self.timestamp,
            "symbol": self.symbol,
            "engine_type": self.engine_type,
            "suggested_leverage": self.suggested_leverage,
            "risk_level": self.risk_level,
            "additional_data": self.additional_data
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AIDecision':
        """从字典创建实例"""
        return cls(
            action=AIActionType(data["action"]),
            confidence=data["confidence"],
            reasoning=data["reasoning"],
            timestamp=data["timestamp"],
            symbol=data["symbol"],
            engine_type=data["engine_type"],
            suggested_leverage=data.get("suggested_leverage"),
            risk_level=data.get("risk_level"),
            additional_data=data.get("additional_data", {})
        )


# =============================================================================
# 配置数据模型
# =============================================================================

@dataclass
class ExchangeConfig:
    """交易所配置数据模型"""
    exchange_name: str                 # 交易所名称
    api_key: str                       # API密钥
    secret_key: str                    # 密钥
    passphrase: Optional[str] = None   # 密码短语（OKX需要）
    trading_password: Optional[str] = None  # 保留字段以兼容现有数据，但不再使用
    sandbox_mode: bool = True          # 是否为沙盒模式

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "exchange_name": self.exchange_name,
            "api_key": self.api_key,
            "secret_key": self.secret_key,
            "passphrase": self.passphrase,
            "trading_password": self.trading_password,
            "sandbox_mode": self.sandbox_mode
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExchangeConfig':
        """从字典创建实例"""
        return cls(
            exchange_name=data["exchange_name"],
            api_key=data["api_key"],
            secret_key=data["secret_key"],
            passphrase=data.get("passphrase"),
            trading_password=data.get("trading_password"),
            sandbox_mode=data.get("sandbox_mode", True)
        )


@dataclass
class AIConfig:
    """AI引擎配置数据模型"""
    deepseek_api_key: str              # DeepSeek API密钥
    analysis_interval: int = 300       # 分析间隔（秒）
    lookback_period: int = 24          # 数据回看周期（小时）
    enable_opening_engine: bool = True # 启用AI开仓引擎
    enable_position_engine: bool = True # 启用AI持仓引擎

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "deepseek_api_key": self.deepseek_api_key,
            "analysis_interval": self.analysis_interval,
            "lookback_period": self.lookback_period,
            "enable_opening_engine": self.enable_opening_engine,
            "enable_position_engine": self.enable_position_engine
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AIConfig':
        """从字典创建实例"""
        return cls(
            deepseek_api_key=data["deepseek_api_key"],
            analysis_interval=data.get("analysis_interval", 300),
            lookback_period=data.get("lookback_period", 24),
            enable_opening_engine=data.get("enable_opening_engine", True),
            enable_position_engine=data.get("enable_position_engine", True)
        )


@dataclass
class TradingParameters:
    """交易参数数据模型"""
    max_leverage: int = 10                              # 最大杠杆倍数
    max_position_ratio: float = 0.5                     # 最大仓位比例
    opening_confidence_threshold: int = 70              # 开仓置信度阈值
    position_confidence_threshold: int = 60             # 持仓置信度阈值
    default_stop_loss_percentage: float = 0.05          # 默认止损百分比
    default_take_profit_percentage: float = 0.1         # 默认止盈百分比

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "max_leverage": self.max_leverage,
            "max_position_ratio": self.max_position_ratio,
            "opening_confidence_threshold": self.opening_confidence_threshold,
            "position_confidence_threshold": self.position_confidence_threshold,
            "default_stop_loss_percentage": self.default_stop_loss_percentage,
            "default_take_profit_percentage": self.default_take_profit_percentage
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradingParameters':
        """从字典创建实例"""
        return cls(
            max_leverage=data.get("max_leverage", 10),
            max_position_ratio=data.get("max_position_ratio", 0.5),
            opening_confidence_threshold=data.get("opening_confidence_threshold", 70),
            position_confidence_threshold=data.get("position_confidence_threshold", 60),
            default_stop_loss_percentage=data.get("default_stop_loss_percentage", 0.05),
            default_take_profit_percentage=data.get("default_take_profit_percentage", 0.1)
        )


@dataclass
class RiskParameters:
    """风险参数数据模型"""
    max_leverage: int                  # 最大杠杆倍数
    max_position_ratio: float          # 最大仓位比例
    min_balance_threshold: float       # 最小余额阈值
    opening_confidence_threshold: int  # 开仓置信度阈值
    position_confidence_threshold: int # 持仓置信度阈值

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "max_leverage": self.max_leverage,
            "max_position_ratio": self.max_position_ratio,
            "min_balance_threshold": self.min_balance_threshold,
            "opening_confidence_threshold": self.opening_confidence_threshold,
            "position_confidence_threshold": self.position_confidence_threshold
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RiskParameters':
        """从字典创建实例"""
        return cls(
            max_leverage=data["max_leverage"],
            max_position_ratio=data["max_position_ratio"],
            min_balance_threshold=data["min_balance_threshold"],
            opening_confidence_threshold=data["opening_confidence_threshold"],
            position_confidence_threshold=data["position_confidence_threshold"]
        )


# =============================================================================
# 系统状态数据模型
# =============================================================================

@dataclass
class SystemState:
    """系统状态数据模型"""
    status: SystemStatus               # 系统状态
    start_time: Optional[int] = None   # 启动时间
    last_update: int = field(default_factory=lambda: int(datetime.now().timestamp() * 1000))
    error_message: Optional[str] = None  # 错误信息
    active_symbols: List[str] = field(default_factory=list)  # 活跃交易对
    total_positions: int = 0           # 总持仓数
    total_balance: float = 0.0         # 总余额
    available_balance: float = 0.0     # 可用余额
    total_pnl: float = 0.0            # 总盈亏

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "status": self.status.value,
            "start_time": self.start_time,
            "last_update": self.last_update,
            "error_message": self.error_message,
            "active_symbols": self.active_symbols,
            "total_positions": self.total_positions,
            "total_balance": self.total_balance,
            "available_balance": self.available_balance,
            "total_pnl": self.total_pnl
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SystemState':
        """从字典创建实例"""
        return cls(
            status=SystemStatus(data["status"]),
            start_time=data.get("start_time"),
            last_update=data.get("last_update", int(datetime.now().timestamp() * 1000)),
            error_message=data.get("error_message"),
            active_symbols=data.get("active_symbols", []),
            total_positions=data.get("total_positions", 0),
            total_balance=data.get("total_balance", 0.0),
            available_balance=data.get("available_balance", 0.0),
            total_pnl=data.get("total_pnl", 0.0)
        )


@dataclass
class AccountBalance:
    """账户余额数据模型"""
    currency: str                      # 币种
    total: float                       # 总余额
    available: float                   # 可用余额
    used: float                        # 已用余额

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "currency": self.currency,
            "total": self.total,
            "available": self.available,
            "used": self.used
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccountBalance':
        """从字典创建实例"""
        return cls(
            currency=data["currency"],
            total=data["total"],
            available=data["available"],
            used=data["used"]
        )


@dataclass
class TradingStatistics:
    """交易统计数据模型"""
    total_trades: int = 0              # 总交易次数
    winning_trades: int = 0            # 盈利交易次数
    losing_trades: int = 0             # 亏损交易次数
    total_profit: float = 0.0          # 总盈利
    total_loss: float = 0.0            # 总亏损
    max_profit: float = 0.0            # 最大盈利
    max_loss: float = 0.0              # 最大亏损
    win_rate: float = 0.0              # 胜率
    profit_factor: float = 0.0         # 盈利因子

    def calculate_statistics(self):
        """计算统计数据"""
        if self.total_trades > 0:
            self.win_rate = self.winning_trades / self.total_trades * 100

        if self.total_loss > 0:
            self.profit_factor = abs(self.total_profit / self.total_loss)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "total_profit": self.total_profit,
            "total_loss": self.total_loss,
            "max_profit": self.max_profit,
            "max_loss": self.max_loss,
            "win_rate": self.win_rate,
            "profit_factor": self.profit_factor
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradingStatistics':
        """从字典创建实例"""
        return cls(
            total_trades=data.get("total_trades", 0),
            winning_trades=data.get("winning_trades", 0),
            losing_trades=data.get("losing_trades", 0),
            total_profit=data.get("total_profit", 0.0),
            total_loss=data.get("total_loss", 0.0),
            max_profit=data.get("max_profit", 0.0),
            max_loss=data.get("max_loss", 0.0),
            win_rate=data.get("win_rate", 0.0),
            profit_factor=data.get("profit_factor", 0.0)
        )


# =============================================================================
# 工具函数
# =============================================================================

def serialize_model(model: Any) -> str:
    """序列化数据模型为JSON字符串

    Args:
        model: 数据模型实例

    Returns:
        str: JSON字符串
    """
    if hasattr(model, 'to_dict'):
        return json.dumps(model.to_dict(), ensure_ascii=False, indent=2)
    else:
        raise ValueError(f"模型 {type(model)} 不支持序列化")


def deserialize_model(model_class: type, json_str: str) -> Any:
    """从JSON字符串反序列化数据模型

    Args:
        model_class: 数据模型类
        json_str: JSON字符串

    Returns:
        Any: 数据模型实例
    """
    try:
        data = json.loads(json_str)
        if hasattr(model_class, 'from_dict'):
            return model_class.from_dict(data)
        else:
            raise ValueError(f"模型类 {model_class} 不支持反序列化")
    except json.JSONDecodeError as e:
        raise ValueError(f"JSON解析失败: {e}")


def validate_model_data(model: Any) -> bool:
    """验证数据模型的数据完整性

    Args:
        model: 数据模型实例

    Returns:
        bool: 验证是否通过
    """
    try:
        # 尝试序列化和反序列化
        json_str = serialize_model(model)
        model_class = type(model)
        deserialize_model(model_class, json_str)
        return True
    except Exception:
        return False
