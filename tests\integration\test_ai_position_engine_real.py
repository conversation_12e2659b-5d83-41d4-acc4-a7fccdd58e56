#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI持仓引擎真实API测试

使用真实的DeepSeek API和持仓数据测试AI持仓管理功能。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.technical_analysis import TechnicalAnalysisEngine
from src.core.ai_position_engine import AIPositionEngine, ProfitDrawdown
from src.ai.deepseek_client import DeepSeekConfig
from src.data.models import ExchangeConfig, Position, TradingSide


async def test_ai_position_engine_real():
    """测试AI持仓引擎"""
    print("=== AI持仓引擎真实API测试 ===")
    print("⚠️  注意：这将使用真实的DeepSeek API进行AI分析")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建DeepSeek配置
    deepseek_config = DeepSeekConfig(
        api_key="***********************************",
        model="deepseek-chat",
        temperature=0.2,
        max_tokens=800
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    ta_engine = TechnicalAnalysisEngine()
    ai_engine = AIPositionEngine(deepseek_config)
    
    try:
        # 1. 测试DeepSeek连接
        print("\n1. 测试DeepSeek API连接...")
        connection_ok = ai_engine.test_connection()
        if connection_ok:
            print("✅ DeepSeek API连接成功")
        else:
            print("❌ DeepSeek API连接失败")
            return False
        
        # 2. 连接交易所并获取真实持仓
        print("\n2. 连接交易所并获取真实持仓...")
        exchange_client.connect()
        
        real_positions = exchange_client.fetch_positions()
        print(f"获取到 {len(real_positions)} 个真实持仓")
        
        if not real_positions:
            print("⚠️ 当前无持仓，创建模拟持仓进行测试")
            
            # 创建模拟持仓用于测试
            test_position = Position(
                symbol="BTC/USDT:USDT",
                side=TradingSide.LONG,
                amount=0.1,
                entry_price=50000.0,
                current_price=52000.0,
                leverage=5,
                unrealized_pnl=200.0,
                unrealized_pnl_percentage=4.0,
                margin_used=1000.0
            )
            test_positions = [test_position]
        else:
            test_positions = real_positions[:2]  # 只测试前2个持仓
        
        # 3. 对每个持仓进行AI分析
        for i, position in enumerate(test_positions):
            print(f"\n=== 分析持仓 {i+1}: {position.symbol} ===")
            print(f"持仓方向: {position.side.value}")
            print(f"持仓数量: {position.amount}")
            print(f"开仓价格: {position.entry_price}")
            print(f"当前价格: {position.current_price}")
            print(f"未实现盈亏: {position.unrealized_pnl} ({position.unrealized_pnl_percentage:.2f}%)")
            
            try:
                # 获取市场数据
                print(f"\n3.{i+1}.1 获取 {position.symbol} 市场数据...")
                market_data = {}
                timeframes = ["5m", "15m", "1h"]
                
                for timeframe in timeframes:
                    ohlcv_data = exchange_client.fetch_ohlcv_sync(position.symbol, timeframe, 300)
                    market_data[timeframe] = ohlcv_data
                    print(f"  {timeframe}: {len(ohlcv_data)} 条K线数据")
                
                # 计算技术指标
                print(f"\n3.{i+1}.2 计算技术指标...")
                technical_indicators = ta_engine.analyze_multi_timeframe(market_data)
                print(f"计算完成 {len(technical_indicators)} 个时间周期的技术指标")
                
                # 计算利润回撤
                print(f"\n3.{i+1}.3 计算利润回撤...")
                
                # 获取价格历史（用于计算最高收益率）
                price_history = [candle.close for candle in market_data["5m"][-50:]]  # 最近50个5分钟价格
                current_price = market_data["5m"][-1].close
                
                profit_drawdown = ai_engine.calculate_profit_drawdown(
                    position=position,
                    current_price=current_price,
                    price_history=price_history
                )
                
                print(f"利润回撤分析:")
                print(f"  历史最高收益率: {profit_drawdown.max_profit_rate:.4f} ({profit_drawdown.max_profit_rate*100:.2f}%)")
                print(f"  当前收益率: {profit_drawdown.current_profit_rate:.4f} ({profit_drawdown.current_profit_rate*100:.2f}%)")
                print(f"  利润回撤幅度: {profit_drawdown.drawdown_amount:.4f} ({profit_drawdown.drawdown_amount*100:.2f}%)")
                print(f"  利润回撤比例: {profit_drawdown.drawdown_percentage:.4f} ({profit_drawdown.drawdown_percentage*100:.2f}%)")
                
                # AI持仓分析
                print(f"\n3.{i+1}.4 AI持仓分析...")
                
                decision = await ai_engine.analyze_position_management(
                    position=position,
                    technical_data=technical_indicators,
                    profit_drawdown=profit_drawdown,
                    current_price=current_price
                )
                
                print(f"AI持仓分析结果:")
                print(f"  建议操作: {decision.action}")
                print(f"  置信度: {decision.confidence}%")
                print(f"  紧急程度: {decision.urgency}")
                print(f"  风险等级: {decision.risk_level}")
                print(f"  分析理由: {decision.reasoning[:200]}...")
                
                if decision.close_ratio:
                    print(f"  平仓比例: {decision.close_ratio:.2%}")
                if decision.new_stop_loss:
                    print(f"  新止损价: {decision.new_stop_loss}")
                if decision.new_take_profit:
                    print(f"  新止盈价: {decision.new_take_profit}")
                
                # 验证决策结构
                valid_actions = ["hold", "close_partial", "close_all", "adjust_stop_loss", "take_profit"]
                assert decision.action in valid_actions
                assert 0 <= decision.confidence <= 100
                assert decision.urgency in ["low", "normal", "high", "urgent"]
                assert decision.risk_level in ["low", "medium", "high"]
                assert len(decision.reasoning) > 10
                
                print(f"✅ {position.symbol} AI持仓分析成功")
                
                # 测试持仓管理决策
                print(f"\n3.{i+1}.5 测试持仓管理决策...")
                
                should_manage = ai_engine.should_manage_position(decision)
                print(f"是否应该执行操作: {should_manage}")
                
                if should_manage:
                    management_params = ai_engine.get_management_parameters(decision, position)
                    print(f"管理参数:")
                    for key, value in management_params.items():
                        print(f"  {key}: {value}")
                    
                    # 验证管理参数
                    assert "action" in management_params
                    assert "confidence" in management_params
                    assert "urgency" in management_params
                    assert "risk_level" in management_params
                
                print(f"✅ {position.symbol} 持仓管理决策完成")
                
            except Exception as e:
                print(f"❌ {position.symbol} 分析失败: {e}")
                continue
        
        # 4. 测试不同市场条件下的决策
        print(f"\n4. 测试不同市场条件下的决策...")
        
        # 创建不同盈亏状态的模拟持仓
        test_scenarios = [
            {
                "name": "盈利持仓",
                "position": Position(
                    symbol="ETH/USDT:USDT",
                    side=TradingSide.LONG,
                    amount=1.0,
                    entry_price=3000.0,
                    current_price=3300.0,
                    leverage=3,
                    unrealized_pnl=300.0,
                    unrealized_pnl_percentage=10.0,
                    margin_used=1000.0
                ),
                "profit_drawdown": ProfitDrawdown(
                    max_profit_rate=0.15,  # 历史最高15%收益
                    current_profit_rate=0.10,  # 当前10%收益
                    drawdown_amount=0.05,  # 回撤5%
                    drawdown_percentage=0.33  # 回撤比例33%
                )
            },
            {
                "name": "亏损持仓",
                "position": Position(
                    symbol="DOGE/USDT:USDT",
                    side=TradingSide.SHORT,
                    amount=1000.0,
                    entry_price=0.20,
                    current_price=0.22,
                    leverage=2,
                    unrealized_pnl=-100.0,
                    unrealized_pnl_percentage=-10.0,
                    margin_used=100.0
                ),
                "profit_drawdown": ProfitDrawdown(
                    max_profit_rate=0.05,  # 历史最高5%收益
                    current_profit_rate=-0.10,  # 当前-10%收益
                    drawdown_amount=0.15,  # 回撤15%
                    drawdown_percentage=3.0  # 回撤比例300%
                )
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n4.{scenario['name']} 场景测试...")
            
            try:
                # 获取市场数据（简化版）
                symbol = scenario["position"].symbol
                market_data = {}
                
                for timeframe in ["15m", "1h"]:
                    ohlcv_data = exchange_client.fetch_ohlcv_sync(symbol, timeframe, 300)
                    market_data[timeframe] = ohlcv_data
                
                # 计算技术指标
                technical_indicators = ta_engine.analyze_multi_timeframe(market_data)
                current_price = market_data["15m"][-1].close
                
                # AI分析
                decision = await ai_engine.analyze_position_management(
                    position=scenario["position"],
                    technical_data=technical_indicators,
                    profit_drawdown=scenario["profit_drawdown"],
                    current_price=current_price
                )
                
                print(f"  {scenario['name']} AI决策: {decision.action} (置信度: {decision.confidence}%)")
                print(f"  紧急程度: {decision.urgency}, 风险等级: {decision.risk_level}")
                
            except Exception as e:
                print(f"  {scenario['name']} 场景测试失败: {e}")
        
        print("✅ 不同市场条件测试完成")
        
        # 5. 性能测试
        print(f"\n5. 性能测试...")
        
        if test_positions:
            import time
            start_time = time.time()
            
            # 连续进行3次分析
            test_position = test_positions[0]
            
            # 获取数据（复用之前的数据）
            market_data = {}
            for timeframe in ["5m", "1h"]:
                ohlcv_data = exchange_client.fetch_ohlcv_sync(test_position.symbol, timeframe, 300)
                market_data[timeframe] = ohlcv_data
            
            technical_indicators = ta_engine.analyze_multi_timeframe(market_data)
            current_price = market_data["5m"][-1].close
            
            price_history = [candle.close for candle in market_data["5m"][-20:]]
            profit_drawdown = ai_engine.calculate_profit_drawdown(
                test_position, current_price, price_history
            )
            
            for i in range(3):
                decision = await ai_engine.analyze_position_management(
                    position=test_position,
                    technical_data=technical_indicators,
                    profit_drawdown=profit_drawdown,
                    current_price=current_price
                )
                print(f"  第{i+1}次分析: {decision.action} (置信度: {decision.confidence}%)")
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 3
            
            print(f"平均分析时间: {avg_time:.2f}秒")
        
        print("✅ 性能测试完成")
        
        print("\n🎉 所有AI持仓引擎测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ AI持仓引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = asyncio.run(test_ai_position_engine_real())
    if success:
        print("\n✅ AI持仓引擎测试全部通过！")
    else:
        print("\n❌ AI持仓引擎测试失败！")
        sys.exit(1)
