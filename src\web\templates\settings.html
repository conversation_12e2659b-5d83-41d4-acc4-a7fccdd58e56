{% extends "base.html" %}

{% block title %}系统设置 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<li style="display: flex; align-items: center; gap: 4px;">
    <span>/</span>
    <span>⚙️</span>
    <span>系统设置</span>
</li>
{% endblock %}

{% block extra_css %}
<style>
    /* 系统设置页面特定样式 */
    .settings-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .settings-tabs {
        display: flex;
        border-bottom: 1px solid var(--ant-color-border-secondary);
        margin-bottom: 24px;
        overflow-x: auto;
    }
    
    .settings-tab {
        padding: 12px 24px;
        border: none;
        background: none;
        color: var(--ant-color-text-secondary);
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        transition: all 0.3s;
        border-bottom: 2px solid transparent;
    }
    
    .settings-tab:hover {
        color: var(--ant-color-primary);
    }
    
    .settings-tab.active {
        color: var(--ant-color-primary);
        border-bottom-color: var(--ant-color-primary);
    }
    
    .settings-content {
        display: none;
    }
    
    .settings-content.active {
        display: block;
    }
    
    .settings-section {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--ant-box-shadow);
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--ant-color-text);
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .section-description {
        font-size: 14px;
        color: var(--ant-color-text-secondary);
        margin-bottom: 24px;
        line-height: 1.5;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
    }
    
    .form-item {
        margin-bottom: 16px;
    }
    
    .form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: var(--ant-color-text);
        margin-bottom: 8px;
    }
    
    .form-input,
    .form-select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid var(--ant-color-border);
        border-radius: var(--ant-border-radius);
        background: var(--ant-color-bg-container);
        color: var(--ant-color-text);
        font-size: 14px;
        transition: all 0.3s;
    }
    
    .form-input:focus,
    .form-select:focus {
        outline: none;
        border-color: var(--ant-color-primary);
        box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
    }
    
    .form-help {
        font-size: 12px;
        color: var(--ant-color-text-tertiary);
        margin-top: 4px;
    }
    
    .form-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-top: 24px;
        padding-top: 24px;
        border-top: 1px solid var(--ant-color-border-secondary);
    }
    
    .ant-btn {
        padding: 8px 16px;
        border-radius: var(--ant-border-radius);
        border: 1px solid var(--ant-color-border);
        background: var(--ant-color-bg-container);
        color: var(--ant-color-text);
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .ant-btn:hover {
        border-color: var(--ant-color-primary);
        color: var(--ant-color-primary);
    }
    
    .ant-btn-primary {
        background: var(--ant-color-primary);
        border-color: var(--ant-color-primary);
        color: white;
    }
    
    .ant-btn-primary:hover {
        background: #4096ff;
        border-color: #4096ff;
    }
    
    .ant-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: var(--ant-border-radius);
        font-size: 12px;
        font-weight: 500;
    }
    
    .status-indicator.success {
        background: rgba(82, 196, 26, 0.1);
        color: var(--ant-color-success);
    }
    
    .status-indicator.error {
        background: rgba(255, 77, 79, 0.1);
        color: var(--ant-color-error);
    }
    
    .status-indicator.warning {
        background: rgba(250, 173, 20, 0.1);
        color: var(--ant-color-warning);
    }
    
    .symbols-selector {
        border: 1px solid var(--ant-color-border);
        border-radius: var(--ant-border-radius);
        max-height: 300px;
        overflow-y: auto;
    }
    
    .symbols-search {
        padding: 12px;
        border-bottom: 1px solid var(--ant-color-border-secondary);
    }
    
    .symbols-list {
        padding: 8px;
    }
    
    .symbol-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .symbol-item:hover {
        background: var(--ant-color-fill);
    }
    
    .symbol-checkbox {
        margin: 0;
    }
    
    .symbol-name {
        font-weight: 500;
        color: var(--ant-color-text);
    }
    
    .symbol-info {
        font-size: 12px;
        color: var(--ant-color-text-secondary);
        margin-left: auto;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        border-radius: var(--ant-border-radius);
    }
    
    [data-theme="dark"] .loading-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid var(--ant-color-border);
        border-top-color: var(--ant-color-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 12px;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .settings-tabs {
            flex-wrap: wrap;
        }
        
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .ant-btn {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="settings-container">
    <div style="margin-bottom: 24px;">
        <h1 style="margin: 0 0 8px 0; font-size: 24px; font-weight: 600; color: var(--ant-color-text);">
            ⚙️ 系统设置
        </h1>
        <p style="margin: 0; color: var(--ant-color-text-secondary);">
            配置交易系统的各项参数和设置
        </p>
    </div>

    <!-- 设置标签页 -->
    <div class="settings-tabs">
        <button class="settings-tab active" data-tab="exchange">🏦 交易所配置</button>
        <button class="settings-tab" data-tab="trading">📊 交易参数</button>
        <button class="settings-tab" data-tab="risk">🛡️ 风控设置</button>
        <button class="settings-tab" data-tab="symbols">💱 交易对选择</button>
        <button class="settings-tab" data-tab="ai">🤖 AI配置</button>
    </div>

    <!-- 交易所配置 -->
    <div class="settings-content active" id="exchange-content">
        <div class="settings-section" style="position: relative;">
            <div class="section-title">
                <span>🏦</span>
                <span>交易所配置</span>
            </div>
            <div class="section-description">
                配置交易所API密钥和连接参数。支持多个主流交易所，可在模拟盘和实盘之间切换。
            </div>
            
            <form id="exchangeConfigForm">
                <div class="form-grid">
                    <div>
                        <div class="form-item">
                            <label class="form-label">选择交易所</label>
                            <select class="form-select" id="exchangeName" name="exchangeName">
                                <option value="">请选择交易所</option>
                                <option value="okx">欧易 OKX</option>
                                <option value="binance">币安 Binance</option>
                                <option value="huobi">火币 Huobi</option>
                                <option value="bybit">Bybit</option>
                            </select>
                            <div class="form-help">选择要连接的交易所</div>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">API Key</label>
                            <input type="password" class="form-input" id="apiKey" name="apiKey" placeholder="输入API Key">
                            <div class="form-help">从交易所获取的API密钥</div>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">Secret Key</label>
                            <input type="password" class="form-input" id="secretKey" name="secretKey" placeholder="输入Secret Key">
                            <div class="form-help">API密钥对应的私钥</div>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">Passphrase (如需要)</label>
                            <input type="password" class="form-input" id="passphrase" name="passphrase" placeholder="输入Passphrase">
                            <div class="form-help">部分交易所需要的额外密码</div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="form-item">
                            <label class="form-label">交易环境</label>
                            <select class="form-select" id="sandboxMode" name="sandboxMode">
                                <option value="true">模拟盘 (推荐)</option>
                                <option value="false">实盘 (谨慎使用)</option>
                            </select>
                            <div class="form-help">建议先在模拟盘测试</div>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">连接状态</label>
                            <div class="status-indicator" id="exchangeConnectionStatus">
                                <span>⏳</span>
                                <span>未连接</span>
                            </div>
                        </div>
                        
                        <div class="form-item">
                            <button type="button" class="ant-btn" id="testConnectionBtn">
                                <span>🔗</span>
                                <span>测试连接</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="ant-btn" id="resetExchangeBtn">
                        <span>🔄</span>
                        <span>重置</span>
                    </button>
                    <button type="submit" class="ant-btn ant-btn-primary">
                        <span>💾</span>
                        <span>保存配置</span>
                    </button>
                </div>
            </form>
            
            <!-- 加载遮罩 -->
            <div class="loading-overlay" id="exchangeLoading" style="display: none;">
                <div class="loading-spinner"></div>
                <span>保存配置中...</span>
            </div>
        </div>
    </div>

    <!-- 交易参数配置 -->
    <div class="settings-content" id="trading-content">
        <div class="settings-section">
            <div class="section-title">
                <span>📊</span>
                <span>交易参数配置</span>
            </div>
            <div class="section-description">
                设置交易系统的核心参数，包括杠杆倍数、仓位大小、AI决策阈值等。
            </div>

            <form id="tradingParamsForm">
                <div class="form-grid">
                    <div>
                        <div class="form-item">
                            <label class="form-label">最大杠杆倍数</label>
                            <input type="number" class="form-input" id="maxLeverage" name="maxLeverage" min="1" max="100" value="10">
                            <div class="form-help">系统允许的最大杠杆倍数 (1-100倍)</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">最大仓位比例 (%)</label>
                            <input type="number" class="form-input" id="maxPositionRatio" name="maxPositionRatio" min="1" max="100" value="50">
                            <div class="form-help">单笔交易最大占用资金比例 (1-100%)</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">开仓置信度阈值</label>
                            <input type="number" class="form-input" id="openingConfidenceThreshold" name="openingConfidenceThreshold" min="0" max="100" value="70">
                            <div class="form-help">AI开仓决策的最低置信度要求 (0-100)</div>
                        </div>
                    </div>

                    <div>
                        <div class="form-item">
                            <label class="form-label">持仓置信度阈值</label>
                            <input type="number" class="form-input" id="positionConfidenceThreshold" name="positionConfidenceThreshold" min="0" max="100" value="60">
                            <div class="form-help">AI持仓管理的最低置信度要求 (0-100)</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">默认止损百分比 (%)</label>
                            <input type="number" class="form-input" id="defaultStopLoss" name="defaultStopLoss" min="0.1" max="50" step="0.1" value="5">
                            <div class="form-help">默认止损幅度 (0.1-50%)</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">默认止盈百分比 (%)</label>
                            <input type="number" class="form-input" id="defaultTakeProfit" name="defaultTakeProfit" min="0.1" max="100" step="0.1" value="10">
                            <div class="form-help">默认止盈幅度 (0.1-100%)</div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="ant-btn" id="resetTradingBtn">
                        <span>🔄</span>
                        <span>重置</span>
                    </button>
                    <button type="submit" class="ant-btn ant-btn-primary">
                        <span>💾</span>
                        <span>保存参数</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 风控设置 -->
    <div class="settings-content" id="risk-content">
        <div class="settings-section">
            <div class="section-title">
                <span>🛡️</span>
                <span>风险控制设置</span>
            </div>
            <div class="section-description">
                配置风险管理参数，确保交易系统在安全范围内运行。
            </div>

            <form id="riskParamsForm">
                <div class="form-grid">
                    <div>
                        <div class="form-item">
                            <label class="form-label">最小余额阈值 (USDT)</label>
                            <input type="number" class="form-input" id="minBalanceThreshold" name="minBalanceThreshold" min="10" value="100">
                            <div class="form-help">账户余额低于此值时停止交易</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">最大回撤限制 (%)</label>
                            <input type="number" class="form-input" id="maxDrawdownLimit" name="maxDrawdownLimit" min="1" max="50" value="20">
                            <div class="form-help">达到此回撤幅度时暂停交易</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">单日最大亏损 (USDT)</label>
                            <input type="number" class="form-input" id="dailyLossLimit" name="dailyLossLimit" min="10" value="500">
                            <div class="form-help">单日亏损达到此值时停止交易</div>
                        </div>
                    </div>

                    <div>
                        <div class="form-item">
                            <label class="form-label">最大同时持仓数</label>
                            <input type="number" class="form-input" id="maxPositions" name="maxPositions" min="1" max="20" value="5">
                            <div class="form-help">系统同时持有的最大仓位数量</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">紧急停止条件</label>
                            <select class="form-select" id="emergencyStopCondition" name="emergencyStopCondition">
                                <option value="manual">仅手动触发</option>
                                <option value="loss_limit">达到亏损限制</option>
                                <option value="drawdown_limit">达到回撤限制</option>
                                <option value="both">任一条件触发</option>
                            </select>
                            <div class="form-help">自动触发紧急停止的条件</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">风控状态</label>
                            <div class="status-indicator success" id="riskManagementStatus">
                                <span>✅</span>
                                <span>正常运行</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="ant-btn" id="resetRiskBtn">
                        <span>🔄</span>
                        <span>重置</span>
                    </button>
                    <button type="submit" class="ant-btn ant-btn-primary">
                        <span>💾</span>
                        <span>保存设置</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 交易对选择 -->
    <div class="settings-content" id="symbols-content">
        <div class="settings-section">
            <div class="section-title">
                <span>💱</span>
                <span>交易对选择</span>
            </div>
            <div class="section-description">
                选择系统要监控和交易的加密货币永续合约交易对。
            </div>

            <div class="symbols-selector">
                <div class="symbols-search">
                    <input type="text" class="form-input" id="symbolsSearch" placeholder="搜索交易对...">
                </div>
                <div class="symbols-list" id="symbolsList">
                    <div style="text-align: center; padding: 40px; color: var(--ant-color-text-secondary);">
                        <div class="loading-spinner"></div>
                        <div>加载交易对列表...</div>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="ant-btn" id="selectAllSymbols">
                    <span>☑️</span>
                    <span>全选</span>
                </button>
                <button type="button" class="ant-btn" id="clearAllSymbols">
                    <span>❌</span>
                    <span>清空</span>
                </button>
                <button type="button" class="ant-btn ant-btn-primary" id="saveSymbols">
                    <span>💾</span>
                    <span>保存选择</span>
                </button>
            </div>
        </div>
    </div>

    <!-- AI配置 -->
    <div class="settings-content" id="ai-content">
        <div class="settings-section">
            <div class="section-title">
                <span>🤖</span>
                <span>AI引擎配置</span>
            </div>
            <div class="section-description">
                配置DeepSeek AI模型的参数和行为设置。
            </div>

            <form id="aiConfigForm">
                <div class="form-grid">
                    <div>
                        <div class="form-item">
                            <label class="form-label">DeepSeek API Key</label>
                            <input type="password" class="form-input" id="deepseekApiKey" name="deepseekApiKey" placeholder="输入DeepSeek API Key">
                            <div class="form-help">从DeepSeek获取的API密钥</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">AI模型版本</label>
                            <select class="form-select" id="aiModel" name="aiModel">
                                <option value="deepseek-chat">DeepSeek Chat</option>
                                <option value="deepseek-coder">DeepSeek Coder</option>
                            </select>
                            <div class="form-help">选择使用的AI模型版本</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">决策频率 (秒)</label>
                            <input type="number" class="form-input" id="decisionInterval" name="decisionInterval" min="30" max="3600" value="300">
                            <div class="form-help">AI分析和决策的时间间隔</div>
                        </div>
                    </div>

                    <div>
                        <div class="form-item">
                            <label class="form-label">温度参数</label>
                            <input type="number" class="form-input" id="temperature" name="temperature" min="0" max="2" step="0.1" value="0.7">
                            <div class="form-help">控制AI回答的随机性 (0-2)</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">最大Token数</label>
                            <input type="number" class="form-input" id="maxTokens" name="maxTokens" min="100" max="4000" value="1000">
                            <div class="form-help">AI响应的最大长度</div>
                        </div>

                        <div class="form-item">
                            <label class="form-label">AI引擎状态</label>
                            <div class="status-indicator success" id="aiEngineStatus">
                                <span>✅</span>
                                <span>正常运行</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="ant-btn" id="testAiBtn">
                        <span>🧪</span>
                        <span>测试AI连接</span>
                    </button>
                    <button type="button" class="ant-btn" id="resetAiBtn">
                        <span>🔄</span>
                        <span>重置</span>
                    </button>
                    <button type="submit" class="ant-btn ant-btn-primary">
                        <span>💾</span>
                        <span>保存配置</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 设置页面管理器
    class SettingsManager {
        constructor() {
            this.currentTab = 'exchange';
            this.availableSymbols = [];
            this.selectedSymbols = [];
            this.init();
        }

        init() {
            this.bindTabEvents();
            this.bindFormEvents();
            this.loadCurrentSettings();
        }

        bindTabEvents() {
            // 标签页切换
            document.querySelectorAll('.settings-tab').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const tabName = e.target.dataset.tab;
                    this.switchTab(tabName);
                });
            });
        }

        switchTab(tabName) {
            // 更新标签页状态
            document.querySelectorAll('.settings-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容区域
            document.querySelectorAll('.settings-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-content`).classList.add('active');

            this.currentTab = tabName;

            // 特殊处理
            if (tabName === 'symbols') {
                this.loadAvailableSymbols();
            }
        }

        bindFormEvents() {
            // 交易所配置表单
            document.getElementById('exchangeConfigForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveExchangeConfig();
            });

            // 测试连接按钮
            document.getElementById('testConnectionBtn').addEventListener('click', () => {
                this.testExchangeConnection();
            });

            // 交易参数表单
            document.getElementById('tradingParamsForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveTradingParams();
            });

            // 风控设置表单
            document.getElementById('riskParamsForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveRiskParams();
            });

            // AI配置表单
            document.getElementById('aiConfigForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveAiConfig();
            });

            // 测试AI连接
            document.getElementById('testAiBtn').addEventListener('click', () => {
                this.testAiConnection();
            });

            // 交易对选择
            document.getElementById('saveSymbols').addEventListener('click', () => {
                this.saveSelectedSymbols();
            });

            document.getElementById('selectAllSymbols').addEventListener('click', () => {
                this.selectAllSymbols();
            });

            document.getElementById('clearAllSymbols').addEventListener('click', () => {
                this.clearAllSymbols();
            });

            // 搜索交易对
            document.getElementById('symbolsSearch').addEventListener('input', (e) => {
                this.filterSymbols(e.target.value);
            });
        }

        async loadCurrentSettings() {
            try {
                // 加载所有配置
                const [exchangeConfig, tradingParams, riskParams, aiConfig, selectedSymbols] = await Promise.all([
                    this.fetchExchangeConfig(),
                    this.fetchTradingParams(),
                    this.fetchRiskParams(),
                    this.fetchAiConfig(),
                    this.fetchSelectedSymbols()
                ]);

                this.populateExchangeForm(exchangeConfig);
                this.populateTradingForm(tradingParams);
                this.populateRiskForm(riskParams);
                this.populateAiForm(aiConfig);
                this.selectedSymbols = selectedSymbols || [];

            } catch (error) {
                console.error('加载设置失败:', error);
                this.showError('加载设置失败，请刷新页面重试');
            }
        }

        async fetchExchangeConfig() {
            const response = await axios.get('/api/config/exchange');
            return response.data;
        }

        async fetchTradingParams() {
            const response = await axios.get('/api/config/trading');
            return response.data;
        }

        async fetchRiskParams() {
            const response = await axios.get('/api/config/risk');
            return response.data;
        }

        async fetchAiConfig() {
            const response = await axios.get('/api/config/ai');
            return response.data;
        }

        async fetchSelectedSymbols() {
            const response = await axios.get('/api/config/symbols');
            return response.data.symbols;
        }

        populateExchangeForm(config) {
            if (config) {
                document.getElementById('exchangeName').value = config.exchangeName || '';
                document.getElementById('apiKey').value = config.apiKey || '';
                document.getElementById('secretKey').value = config.secretKey || '';
                document.getElementById('passphrase').value = config.passphrase || '';
                document.getElementById('sandboxMode').value = config.sandboxMode ? 'true' : 'false';

                this.updateConnectionStatus(config.connectionStatus);
            }
        }

        populateTradingForm(params) {
            if (params) {
                document.getElementById('maxLeverage').value = params.maxLeverage || 10;
                document.getElementById('maxPositionRatio').value = params.maxPositionRatio || 50;
                document.getElementById('openingConfidenceThreshold').value = params.openingConfidenceThreshold || 70;
                document.getElementById('positionConfidenceThreshold').value = params.positionConfidenceThreshold || 60;
                document.getElementById('defaultStopLoss').value = params.defaultStopLoss || 5;
                document.getElementById('defaultTakeProfit').value = params.defaultTakeProfit || 10;
            }
        }

        populateRiskForm(params) {
            if (params) {
                document.getElementById('minBalanceThreshold').value = params.minBalanceThreshold || 100;
                document.getElementById('maxDrawdownLimit').value = params.maxDrawdownLimit || 20;
                document.getElementById('dailyLossLimit').value = params.dailyLossLimit || 500;
                document.getElementById('maxPositions').value = params.maxPositions || 5;
                document.getElementById('emergencyStopCondition').value = params.emergencyStopCondition || 'manual';
            }
        }

        populateAiForm(config) {
            if (config) {
                document.getElementById('deepseekApiKey').value = config.deepseekApiKey || '';
                document.getElementById('aiModel').value = config.aiModel || 'deepseek-chat';
                document.getElementById('decisionInterval').value = config.decisionInterval || 300;
                document.getElementById('temperature').value = config.temperature || 0.7;
                document.getElementById('maxTokens').value = config.maxTokens || 1000;
            }
        }

        async saveExchangeConfig() {
            const formData = new FormData(document.getElementById('exchangeConfigForm'));
            const config = Object.fromEntries(formData.entries());
            config.sandboxMode = config.sandboxMode === 'true';

            try {
                this.showLoading('exchangeLoading');
                await axios.post('/api/config/exchange', config);
                this.showSuccess('交易所配置保存成功');

                // 重新测试连接
                await this.testExchangeConnection();

            } catch (error) {
                console.error('保存交易所配置失败:', error);
                this.showError('保存失败: ' + (error.response?.data?.message || error.message));
            } finally {
                this.hideLoading('exchangeLoading');
            }
        }

        async testExchangeConnection() {
            try {
                const response = await axios.post('/api/config/exchange/test');
                const result = response.data;

                this.updateConnectionStatus(result.status, result.message);

                if (result.status === 'success') {
                    this.showSuccess('交易所连接测试成功');
                } else {
                    this.showError('连接测试失败: ' + result.message);
                }

            } catch (error) {
                console.error('测试连接失败:', error);
                this.updateConnectionStatus('error', '连接失败');
                this.showError('连接测试失败: ' + (error.response?.data?.message || error.message));
            }
        }

        updateConnectionStatus(status, message = '') {
            const statusElement = document.getElementById('exchangeConnectionStatus');

            statusElement.className = 'status-indicator';

            switch (status) {
                case 'success':
                case 'connected':
                    statusElement.classList.add('success');
                    statusElement.innerHTML = '<span>✅</span><span>已连接</span>';
                    break;
                case 'error':
                case 'failed':
                    statusElement.classList.add('error');
                    statusElement.innerHTML = '<span>❌</span><span>连接失败</span>';
                    break;
                case 'warning':
                    statusElement.classList.add('warning');
                    statusElement.innerHTML = '<span>⚠️</span><span>连接异常</span>';
                    break;
                default:
                    statusElement.innerHTML = '<span>⏳</span><span>未连接</span>';
            }

            if (message) {
                statusElement.title = message;
            }
        }

        async saveTradingParams() {
            const formData = new FormData(document.getElementById('tradingParamsForm'));
            const params = Object.fromEntries(formData.entries());

            // 转换数值类型
            Object.keys(params).forEach(key => {
                if (params[key] !== '') {
                    params[key] = parseFloat(params[key]);
                }
            });

            try {
                await axios.post('/api/config/trading', params);
                this.showSuccess('交易参数保存成功');
            } catch (error) {
                console.error('保存交易参数失败:', error);
                this.showError('保存失败: ' + (error.response?.data?.message || error.message));
            }
        }

        async saveRiskParams() {
            const formData = new FormData(document.getElementById('riskParamsForm'));
            const params = Object.fromEntries(formData.entries());

            // 转换数值类型
            Object.keys(params).forEach(key => {
                if (key !== 'emergencyStopCondition' && params[key] !== '') {
                    params[key] = parseFloat(params[key]);
                }
            });

            try {
                await axios.post('/api/config/risk', params);
                this.showSuccess('风控设置保存成功');
            } catch (error) {
                console.error('保存风控设置失败:', error);
                this.showError('保存失败: ' + (error.response?.data?.message || error.message));
            }
        }

        async saveAiConfig() {
            const formData = new FormData(document.getElementById('aiConfigForm'));
            const config = Object.fromEntries(formData.entries());

            // 转换数值类型
            config.decisionInterval = parseInt(config.decisionInterval);
            config.temperature = parseFloat(config.temperature);
            config.maxTokens = parseInt(config.maxTokens);

            try {
                await axios.post('/api/config/ai', config);
                this.showSuccess('AI配置保存成功');
            } catch (error) {
                console.error('保存AI配置失败:', error);
                this.showError('保存失败: ' + (error.response?.data?.message || error.message));
            }
        }

        async testAiConnection() {
            try {
                const response = await axios.post('/api/config/ai/test');
                const result = response.data;

                if (result.status === 'success') {
                    this.showSuccess('AI连接测试成功');
                    document.getElementById('aiEngineStatus').innerHTML = '<span>✅</span><span>正常运行</span>';
                } else {
                    this.showError('AI连接测试失败: ' + result.message);
                    document.getElementById('aiEngineStatus').innerHTML = '<span>❌</span><span>连接失败</span>';
                }

            } catch (error) {
                console.error('测试AI连接失败:', error);
                this.showError('AI连接测试失败: ' + (error.response?.data?.message || error.message));
                document.getElementById('aiEngineStatus').innerHTML = '<span>❌</span><span>连接失败</span>';
            }
        }

        showLoading(elementId) {
            document.getElementById(elementId).style.display = 'flex';
        }

        hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        showSuccess(message) {
            alert('✅ ' + message);
        }

        showError(message) {
            alert('❌ ' + message);
        }
    }

    // 页面加载完成后初始化设置管理器
    document.addEventListener('DOMContentLoaded', () => {
        window.settingsManager = new SettingsManager();
    });
</script>
{% endblock %}
