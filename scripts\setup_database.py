#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统数据库初始化脚本

此脚本用于初始化系统数据库，包括：
1. 创建数据库表结构
2. 插入默认配置数据
3. 验证数据库完整性
4. 生成加密密钥
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.database_manager import DatabaseManager
from src.data.encryption import generate_encryption_key, test_encryption
from src.data.models import TradingParameters, ExchangeConfig
from src.utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)


def generate_new_encryption_key() -> str:
    """生成新的加密密钥
    
    Returns:
        str: 32字符的加密密钥
    """
    try:
        key = generate_encryption_key()
        logger.info("新的加密密钥生成成功")
        return key
    except Exception as e:
        logger.error(f"生成加密密钥失败: {e}")
        raise


def test_database_encryption(encryption_key: str) -> bool:
    """测试数据库加密功能
    
    Args:
        encryption_key: 加密密钥
    
    Returns:
        bool: 测试是否通过
    """
    try:
        logger.info("开始测试数据库加密功能...")
        
        # 测试加密功能
        if not test_encryption(encryption_key):
            logger.error("加密功能测试失败")
            return False
        
        # 测试数据库加密存储
        db_manager = DatabaseManager("data/test_encryption.db", encryption_key)
        
        # 创建测试配置
        test_config = ExchangeConfig(
            exchange_name="test_exchange",
            api_key="test_api_key_12345",
            secret_key="test_secret_key_67890",
            passphrase="test_passphrase",
            sandbox_mode=True
        )
        
        # 保存和加载测试
        db_manager.init_database()
        db_manager.save_exchange_config(test_config)
        loaded_config = db_manager.load_exchange_config("test_exchange")
        
        # 验证数据一致性
        if (loaded_config.api_key != test_config.api_key or 
            loaded_config.secret_key != test_config.secret_key):
            logger.error("数据库加密存储测试失败")
            return False
        
        # 清理测试数据库
        test_db_path = Path("data/test_encryption.db")
        if test_db_path.exists():
            test_db_path.unlink()
        
        logger.info("数据库加密功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"数据库加密测试失败: {e}")
        return False


def setup_default_data(db_manager: DatabaseManager) -> bool:
    """设置默认数据
    
    Args:
        db_manager: 数据库管理器
    
    Returns:
        bool: 设置是否成功
    """
    try:
        logger.info("开始设置默认数据...")
        
        # 设置默认交易参数
        default_params = TradingParameters(
            max_leverage=10,
            max_position_ratio=0.5,
            opening_confidence_threshold=70,
            position_confidence_threshold=60,
            default_stop_loss_percentage=0.05,
            default_take_profit_percentage=0.1
        )
        
        db_manager.save_trading_parameters(default_params)
        logger.info("默认交易参数设置完成")
        
        # 设置默认交易对
        default_symbols = [
            "BTC/USDT:USDT",
            "ETH/USDT:USDT",
            "BNB/USDT:USDT",
            "ADA/USDT:USDT",
            "SOL/USDT:USDT"
        ]
        
        db_manager.save_selected_symbols(default_symbols)
        logger.info(f"默认交易对设置完成: {len(default_symbols)} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"设置默认数据失败: {e}")
        return False


def verify_database_integrity(db_manager: DatabaseManager) -> bool:
    """验证数据库完整性
    
    Args:
        db_manager: 数据库管理器
    
    Returns:
        bool: 验证是否通过
    """
    try:
        logger.info("开始验证数据库完整性...")
        
        # 获取数据库信息
        db_info = db_manager.get_database_info()
        
        # 检查必需的表是否存在
        required_tables = [
            "exchange_config",
            "trading_parameters", 
            "selected_symbols",
            "system_logs",
            "ai_decisions",
            "trading_records"
        ]
        
        missing_tables = []
        for table in required_tables:
            if table not in db_info["tables"]:
                missing_tables.append(table)
        
        if missing_tables:
            logger.error(f"缺少必需的数据库表: {missing_tables}")
            return False
        
        # 检查交易参数是否存在
        params = db_manager.load_trading_parameters()
        if not params:
            logger.error("交易参数未正确设置")
            return False
        
        # 检查交易对是否存在
        symbols = db_manager.load_selected_symbols()
        if not symbols:
            logger.warning("未设置默认交易对")
        
        logger.info("数据库完整性验证通过")
        logger.info(f"数据库信息: {db_info}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库完整性验证失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("=" * 60)
        print("DeepSeek量化交易系统数据库初始化")
        print("=" * 60)
        
        # 获取配置
        settings = get_settings()
        
        # 检查是否需要生成新的加密密钥
        encryption_key = settings.database_encryption_key
        if not encryption_key:
            print("\n未配置加密密钥，正在生成新密钥...")
            encryption_key = generate_new_encryption_key()
            print(f"新的加密密钥: {encryption_key}")
            print("请将此密钥保存到 .env 文件的 DATABASE_ENCRYPTION_KEY 配置项中")
            print("警告: 请妥善保管此密钥，丢失将无法解密已存储的数据！")
        
        # 测试加密功能
        print("\n正在测试加密功能...")
        if not test_database_encryption(encryption_key):
            print("❌ 加密功能测试失败")
            return False
        print("✅ 加密功能测试通过")
        
        # 初始化数据库
        print(f"\n正在初始化数据库: {settings.database_path}")
        db_manager = DatabaseManager(settings.database_path, encryption_key)
        
        # 创建数据库表结构
        print("正在创建数据库表结构...")
        db_manager.init_database()
        print("✅ 数据库表结构创建完成")
        
        # 设置默认数据
        print("正在设置默认数据...")
        if not setup_default_data(db_manager):
            print("❌ 默认数据设置失败")
            return False
        print("✅ 默认数据设置完成")
        
        # 验证数据库完整性
        print("正在验证数据库完整性...")
        if not verify_database_integrity(db_manager):
            print("❌ 数据库完整性验证失败")
            return False
        print("✅ 数据库完整性验证通过")
        
        # 创建备份
        print("正在创建初始备份...")
        backup_path = db_manager.backup_database()
        print(f"✅ 初始备份创建完成: {backup_path}")
        
        # 关闭数据库连接
        db_manager.close_connection()
        
        print("\n" + "=" * 60)
        print("数据库初始化完成！")
        print("=" * 60)
        print(f"数据库路径: {settings.database_path}")
        print(f"备份路径: {backup_path}")
        if not settings.database_encryption_key:
            print(f"加密密钥: {encryption_key}")
            print("请将加密密钥保存到 .env 文件中！")
        print("=" * 60)
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return False
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        print(f"❌ 数据库初始化失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
