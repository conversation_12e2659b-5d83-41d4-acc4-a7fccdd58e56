#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI提示词模板系统

此模块包含用于AI开仓引擎和AI持仓引擎的提示词模板，包括：
1. 开仓决策提示词模板
2. 持仓管理提示词模板
3. 技术分析数据格式化
4. 响应格式规范
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from src.utils.logger import get_logger

logger = get_logger(__name__)


class PromptTemplates:
    """AI提示词模板类"""
    
    @staticmethod
    def get_opening_prompt(symbol: str, technical_data: Dict[str, Any], 
                          market_context: Optional[Dict[str, Any]] = None) -> str:
        """获取开仓决策提示词
        
        Args:
            symbol: 交易对符号
            technical_data: 技术分析数据
            market_context: 市场环境信息
        
        Returns:
            str: 开仓决策提示词
        """
        
        # 格式化技术指标数据
        indicators_text = PromptTemplates._format_technical_indicators(technical_data)
        
        # 市场环境信息
        market_text = ""
        if market_context:
            market_text = f"""
市场环境信息：
- 当前时间：{market_context.get('current_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}
- 市场趋势：{market_context.get('market_trend', '未知')}
- 波动率水平：{market_context.get('volatility_level', '未知')}
"""
        
        prompt = f"""你是一个专业的加密货币永续合约量化交易AI分析师。请基于以下技术分析数据，为{symbol}交易对提供开仓建议。

{market_text}

技术分析数据：
{indicators_text}

请严格按照以下JSON格式返回分析结果：

{{
    "action": "open_long" | "open_short" | "no_action",
    "confidence": 85,
    "reasoning": "详细的分析理由，包括关键技术指标的解读",
    "suggested_leverage": 10,
    "risk_level": "low" | "medium" | "high",
    "key_levels": {{
        "entry_price": 50000.0,
        "stop_loss": 48000.0,
        "take_profit": 55000.0
    }},
    "market_conditions": "当前市场状况评估",
    "timeframe_analysis": "多时间周期分析结果"
}}

分析要求：
1. 综合考虑多个时间周期的技术指标
2. 重点关注趋势指标（SMA、EMA、MACD、ADX）的一致性
3. 结合震荡指标（RSI、Stochastic）判断超买超卖状态
4. 考虑波动率指标（布林带、ATR）评估风险
5. 分析成交量指标确认趋势强度
6. 置信度应基于指标的一致性和信号强度
7. 建议杠杆应考虑当前市场波动率
8. 只有在有明确信号时才建议开仓，不确定时选择no_action

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        return prompt
    
    @staticmethod
    def get_position_management_prompt(symbol: str, position_data: Dict[str, Any], 
                                     technical_data: Dict[str, Any], 
                                     profit_drawdown: Dict[str, Any]) -> str:
        """获取持仓管理提示词
        
        Args:
            symbol: 交易对符号
            position_data: 持仓数据
            technical_data: 技术分析数据
            profit_drawdown: 利润回撤数据
        
        Returns:
            str: 持仓管理提示词
        """
        
        # 格式化持仓信息
        position_text = PromptTemplates._format_position_data(position_data)
        
        # 格式化技术指标数据
        indicators_text = PromptTemplates._format_technical_indicators(technical_data)
        
        # 格式化利润回撤数据
        drawdown_text = PromptTemplates._format_profit_drawdown(profit_drawdown)
        
        prompt = f"""你是一个专业的加密货币永续合约量化交易AI分析师。请基于当前持仓状态、技术分析数据和利润回撤情况，为{symbol}交易对提供持仓管理建议。

当前持仓信息：
{position_text}

利润回撤分析：
{drawdown_text}

技术分析数据：
{indicators_text}

请严格按照以下JSON格式返回分析结果：

{{
    "action": "hold" | "close_position" | "adjust_stop_loss" | "take_partial_profit" | "add_position",
    "confidence": 85,
    "reasoning": "详细的分析理由，包括持仓状态和技术指标的综合评估",
    "suggested_adjustments": {{
        "new_stop_loss": 48000.0,
        "new_take_profit": 55000.0,
        "position_size_change": 0.0
    }},
    "risk_assessment": "当前持仓风险评估",
    "profit_protection": "利润保护策略建议",
    "market_outlook": "基于技术分析的市场前景判断"
}}

分析要求：
1. 重点关注利润回撤情况，及时保护已实现利润
2. 结合技术指标判断趋势是否发生变化
3. 评估当前持仓的风险收益比
4. 考虑止损和止盈位置的合理性
5. 如果利润回撤超过50%，应考虑部分获利了结
6. 如果技术指标显示趋势反转，应考虑平仓
7. 置信度应基于技术信号的明确程度
8. 优先保护资本，其次追求利润最大化

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        return prompt
    
    @staticmethod
    def _format_technical_indicators(technical_data: Dict[str, Any]) -> str:
        """格式化技术指标数据
        
        Args:
            technical_data: 技术分析数据
        
        Returns:
            str: 格式化后的技术指标文本
        """
        try:
            formatted_text = ""
            
            for timeframe, indicators in technical_data.items():
                if not isinstance(indicators, dict):
                    continue
                    
                formatted_text += f"\n{timeframe}时间周期：\n"
                
                # 趋势指标
                if 'trend' in indicators:
                    trend = indicators['trend']
                    formatted_text += f"  趋势指标：\n"
                    formatted_text += f"    SMA20: {trend.get('SMA_20', 'N/A')}\n"
                    formatted_text += f"    SMA50: {trend.get('SMA_50', 'N/A')}\n"
                    formatted_text += f"    EMA12: {trend.get('EMA_12', 'N/A')}\n"
                    formatted_text += f"    EMA26: {trend.get('EMA_26', 'N/A')}\n"
                    formatted_text += f"    MACD: {trend.get('MACD', 'N/A')}\n"
                    formatted_text += f"    MACD信号: {trend.get('MACD_SIGNAL', 'N/A')}\n"
                    formatted_text += f"    ADX: {trend.get('ADX', 'N/A')}\n"
                
                # 震荡指标
                if 'oscillator' in indicators:
                    osc = indicators['oscillator']
                    formatted_text += f"  震荡指标：\n"
                    formatted_text += f"    RSI: {osc.get('RSI_14', 'N/A')}\n"
                    formatted_text += f"    Stoch K: {osc.get('STOCH_K', 'N/A')}\n"
                    formatted_text += f"    Stoch D: {osc.get('STOCH_D', 'N/A')}\n"
                    formatted_text += f"    Williams %R: {osc.get('WILLIAMS_R', 'N/A')}\n"
                
                # 波动率指标
                if 'volatility' in indicators:
                    vol = indicators['volatility']
                    formatted_text += f"  波动率指标：\n"
                    formatted_text += f"    布林带上轨: {vol.get('BB_UPPER', 'N/A')}\n"
                    formatted_text += f"    布林带下轨: {vol.get('BB_LOWER', 'N/A')}\n"
                    formatted_text += f"    ATR: {vol.get('ATR', 'N/A')}\n"
                
                # 成交量指标
                if 'volume' in indicators:
                    volume = indicators['volume']
                    formatted_text += f"  成交量指标：\n"
                    formatted_text += f"    OBV: {volume.get('OBV', 'N/A')}\n"
                    formatted_text += f"    VWAP: {volume.get('VWAP', 'N/A')}\n"
                
                formatted_text += "\n"
            
            return formatted_text
            
        except Exception as e:
            logger.error(f"格式化技术指标数据失败: {e}")
            return "技术指标数据格式化失败"
    
    @staticmethod
    def _format_position_data(position_data: Dict[str, Any]) -> str:
        """格式化持仓数据
        
        Args:
            position_data: 持仓数据
        
        Returns:
            str: 格式化后的持仓文本
        """
        try:
            return f"""
- 交易对：{position_data.get('symbol', 'N/A')}
- 持仓方向：{position_data.get('side', 'N/A')}
- 持仓数量：{position_data.get('amount', 'N/A')}
- 开仓价格：{position_data.get('entry_price', 'N/A')}
- 当前价格：{position_data.get('current_price', 'N/A')}
- 杠杆倍数：{position_data.get('leverage', 'N/A')}x
- 未实现盈亏：{position_data.get('unrealized_pnl', 'N/A')} USDT
- 未实现盈亏率：{position_data.get('unrealized_pnl_percentage', 'N/A')}%
- 已用保证金：{position_data.get('margin_used', 'N/A')} USDT
"""
        except Exception as e:
            logger.error(f"格式化持仓数据失败: {e}")
            return "持仓数据格式化失败"
    
    @staticmethod
    def _format_profit_drawdown(profit_drawdown: Dict[str, Any]) -> str:
        """格式化利润回撤数据
        
        Args:
            profit_drawdown: 利润回撤数据
        
        Returns:
            str: 格式化后的利润回撤文本
        """
        try:
            return f"""
- 历史最高收益率：{profit_drawdown.get('max_profit_rate', 'N/A')}%
- 当前收益率：{profit_drawdown.get('current_profit_rate', 'N/A')}%
- 利润回撤幅度：{profit_drawdown.get('drawdown_amount', 'N/A')}%
- 利润回撤比例：{profit_drawdown.get('drawdown_percentage', 'N/A')}%
"""
        except Exception as e:
            logger.error(f"格式化利润回撤数据失败: {e}")
            return "利润回撤数据格式化失败"


class ResponseParser:
    """AI响应解析器"""
    
    @staticmethod
    def parse_opening_response(response_text: str) -> Dict[str, Any]:
        """解析开仓决策响应
        
        Args:
            response_text: AI响应文本
        
        Returns:
            Dict[str, Any]: 解析后的决策数据
        """
        try:
            # 尝试直接解析JSON
            response_data = json.loads(response_text.strip())
            
            # 验证必需字段
            required_fields = ['action', 'confidence', 'reasoning']
            for field in required_fields:
                if field not in response_data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 验证action值
            valid_actions = ['open_long', 'open_short', 'no_action']
            if response_data['action'] not in valid_actions:
                raise ValueError(f"无效的action值: {response_data['action']}")
            
            # 验证confidence范围
            confidence = response_data['confidence']
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                raise ValueError(f"无效的confidence值: {confidence}")
            
            return response_data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return ResponseParser._create_error_response("JSON格式错误")
        except ValueError as e:
            logger.error(f"响应验证失败: {e}")
            return ResponseParser._create_error_response(str(e))
        except Exception as e:
            logger.error(f"解析开仓响应失败: {e}")
            return ResponseParser._create_error_response("解析失败")
    
    @staticmethod
    def parse_position_response(response_text: str) -> Dict[str, Any]:
        """解析持仓管理响应
        
        Args:
            response_text: AI响应文本
        
        Returns:
            Dict[str, Any]: 解析后的管理决策数据
        """
        try:
            # 尝试直接解析JSON
            response_data = json.loads(response_text.strip())
            
            # 验证必需字段
            required_fields = ['action', 'confidence', 'reasoning']
            for field in required_fields:
                if field not in response_data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 验证action值
            valid_actions = ['hold', 'close_position', 'adjust_stop_loss', 'take_partial_profit', 'add_position']
            if response_data['action'] not in valid_actions:
                raise ValueError(f"无效的action值: {response_data['action']}")
            
            # 验证confidence范围
            confidence = response_data['confidence']
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                raise ValueError(f"无效的confidence值: {confidence}")
            
            return response_data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return ResponseParser._create_error_response("JSON格式错误")
        except ValueError as e:
            logger.error(f"响应验证失败: {e}")
            return ResponseParser._create_error_response(str(e))
        except Exception as e:
            logger.error(f"解析持仓响应失败: {e}")
            return ResponseParser._create_error_response("解析失败")
    
    @staticmethod
    def _create_error_response(error_message: str) -> Dict[str, Any]:
        """创建错误响应
        
        Args:
            error_message: 错误信息
        
        Returns:
            Dict[str, Any]: 错误响应数据
        """
        return {
            "action": "no_action",
            "confidence": 0,
            "reasoning": f"AI响应解析失败: {error_message}",
            "error": True,
            "error_message": error_message
        }
