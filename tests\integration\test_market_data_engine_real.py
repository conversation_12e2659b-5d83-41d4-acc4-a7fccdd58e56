#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场数据引擎真实API集成测试

使用真实的OKX模拟盘API进行测试。
"""

import pytest
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.market_data_engine import MarketDataEngine
from src.data.models import ExchangeConfig


@pytest.mark.integration
class TestMarketDataEngineReal:
    """市场数据引擎真实API测试类"""
    
    @pytest.fixture
    def okx_config(self):
        """OKX模拟盘配置"""
        return ExchangeConfig(
            exchange_name="okx",
            api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
            secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
            passphrase="CAOwei00!",
            sandbox_mode=True
        )
    
    @pytest.fixture
    def exchange_client(self, okx_config):
        """创建并连接交易所客户端"""
        client = ExchangeClient(okx_config)
        client.connect()
        yield client
        client.disconnect()
    
    @pytest.fixture
    def market_data_engine(self, exchange_client):
        """创建市场数据引擎"""
        symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT"]
        return MarketDataEngine(exchange_client, symbols)
    
    def test_market_data_engine_init(self, market_data_engine):
        """测试市场数据引擎初始化"""
        print("\n=== 测试市场数据引擎初始化 ===")
        
        assert len(market_data_engine.symbols) == 2
        assert "BTC/USDT:USDT" in market_data_engine.symbols
        assert "ETH/USDT:USDT" in market_data_engine.symbols
        assert market_data_engine.is_running is False
        
        # 检查数据需求配置
        assert len(market_data_engine.data_requirements) == 2
        for symbol in market_data_engine.symbols:
            assert symbol in market_data_engine.data_requirements
            requirement = market_data_engine.data_requirements[symbol]
            assert requirement.symbol == symbol
            assert len(requirement.timeframes) == 4  # 默认4个时间周期
        
        print("✅ 市场数据引擎初始化成功")
    
    @pytest.mark.asyncio
    async def test_fetch_multi_timeframe_data(self, market_data_engine):
        """测试获取多时间周期数据"""
        print("\n=== 测试获取多时间周期数据 ===")
        
        symbol = "BTC/USDT:USDT"
        
        # 获取多时间周期数据
        timeframe_data = await market_data_engine.fetch_multi_timeframe_data(symbol)
        
        assert isinstance(timeframe_data, dict)
        print(f"获取到 {len(timeframe_data)} 个时间周期的数据")
        
        # 检查每个时间周期的数据
        for timeframe, ohlcv_list in timeframe_data.items():
            print(f"  {timeframe}: {len(ohlcv_list)} 条K线数据")
            assert isinstance(ohlcv_list, list)
            
            if ohlcv_list:
                # 验证数据结构
                first_candle = ohlcv_list[0]
                assert hasattr(first_candle, 'timestamp')
                assert hasattr(first_candle, 'open')
                assert hasattr(first_candle, 'high')
                assert hasattr(first_candle, 'low')
                assert hasattr(first_candle, 'close')
                assert hasattr(first_candle, 'volume')
                
                # 验证时间戳递增
                timestamps = [candle.timestamp for candle in ohlcv_list]
                assert timestamps == sorted(timestamps)
        
        print("✅ 多时间周期数据获取成功")
    
    def test_fetch_multi_timeframe_data_sync(self, market_data_engine):
        """测试同步获取多时间周期数据"""
        print("\n=== 测试同步获取多时间周期数据 ===")
        
        symbol = "ETH/USDT:USDT"
        
        # 同步获取多时间周期数据
        timeframe_data = market_data_engine.fetch_multi_timeframe_data_sync(symbol)
        
        assert isinstance(timeframe_data, dict)
        print(f"同步获取到 {len(timeframe_data)} 个时间周期的数据")
        
        # 检查数据
        for timeframe, ohlcv_list in timeframe_data.items():
            print(f"  {timeframe}: {len(ohlcv_list)} 条K线数据")
            assert isinstance(ohlcv_list, list)
        
        print("✅ 同步多时间周期数据获取成功")
    
    @pytest.mark.asyncio
    async def test_update_market_data(self, market_data_engine):
        """测试更新市场数据"""
        print("\n=== 测试更新市场数据 ===")
        
        symbol = "BTC/USDT:USDT"
        
        # 获取数据
        timeframe_data = await market_data_engine.fetch_multi_timeframe_data(symbol)
        
        # 更新市场数据
        market_data_engine.update_market_data(symbol, timeframe_data)
        
        # 验证数据已更新
        for timeframe in timeframe_data.keys():
            stored_data = market_data_engine.get_latest_data(symbol, timeframe)
            assert len(stored_data) == len(timeframe_data[timeframe])
            print(f"  {timeframe}: 存储了 {len(stored_data)} 条数据")
        
        # 测试获取最新价格
        latest_price = market_data_engine.get_latest_price(symbol)
        if latest_price:
            print(f"  最新价格: {latest_price}")
            assert isinstance(latest_price, float)
            assert latest_price > 0
        
        print("✅ 市场数据更新成功")
    
    def test_data_sufficiency_check(self, market_data_engine):
        """测试数据充足性检查"""
        print("\n=== 测试数据充足性检查 ===")
        
        symbol = "BTC/USDT:USDT"
        timeframe = "1m"
        
        # 初始状态应该数据不足
        is_sufficient_before = market_data_engine.is_data_sufficient(symbol, timeframe)
        print(f"更新前数据是否充足: {is_sufficient_before}")
        
        # 获取并更新数据
        timeframe_data = market_data_engine.fetch_multi_timeframe_data_sync(symbol)
        market_data_engine.update_market_data(symbol, timeframe_data)
        
        # 检查数据数量
        data_count = market_data_engine.get_data_count(symbol, timeframe)
        print(f"数据数量: {data_count}")
        
        # 检查是否充足
        is_sufficient_after = market_data_engine.is_data_sufficient(symbol, timeframe)
        print(f"更新后数据是否充足: {is_sufficient_after}")
        
        # 如果获取到足够数据，应该显示充足
        if data_count >= 200:
            assert is_sufficient_after is True
        
        print("✅ 数据充足性检查完成")
    
    def test_symbol_management(self, market_data_engine):
        """测试交易对管理"""
        print("\n=== 测试交易对管理 ===")
        
        initial_count = len(market_data_engine.symbols)
        print(f"初始交易对数量: {initial_count}")
        
        # 添加新交易对
        new_symbol = "DOGE/USDT:USDT"
        market_data_engine.add_symbol(new_symbol)
        
        assert new_symbol in market_data_engine.symbols
        assert len(market_data_engine.symbols) == initial_count + 1
        assert new_symbol in market_data_engine.data_requirements
        print(f"添加 {new_symbol} 成功")
        
        # 设置时间周期
        custom_timeframes = ["1m", "5m"]
        market_data_engine.set_timeframes(new_symbol, custom_timeframes)
        
        requirement = market_data_engine.data_requirements[new_symbol]
        assert requirement.timeframes == custom_timeframes
        print(f"设置 {new_symbol} 时间周期: {custom_timeframes}")
        
        # 移除交易对
        market_data_engine.remove_symbol(new_symbol)
        
        assert new_symbol not in market_data_engine.symbols
        assert len(market_data_engine.symbols) == initial_count
        assert new_symbol not in market_data_engine.data_requirements
        print(f"移除 {new_symbol} 成功")
        
        print("✅ 交易对管理测试完成")
    
    def test_data_status(self, market_data_engine):
        """测试数据状态获取"""
        print("\n=== 测试数据状态获取 ===")
        
        # 获取初始状态
        status = market_data_engine.get_data_status()
        
        assert isinstance(status, dict)
        assert len(status) == len(market_data_engine.symbols)
        
        print("数据状态:")
        for symbol, symbol_status in status.items():
            print(f"  {symbol}:")
            print(f"    最后更新: {symbol_status['last_update']}")
            print(f"    数据充足: {symbol_status['is_sufficient']}")
            
            for timeframe, tf_status in symbol_status['timeframes'].items():
                print(f"    {timeframe}: {tf_status['count']}/{tf_status['required']} ({'充足' if tf_status['sufficient'] else '不足'})")
        
        print("✅ 数据状态获取成功")
    
    def test_market_summary(self, market_data_engine):
        """测试市场数据摘要"""
        print("\n=== 测试市场数据摘要 ===")
        
        summary = market_data_engine.get_market_summary()
        
        assert isinstance(summary, dict)
        assert "total_symbols" in summary
        assert "is_running" in summary
        assert "symbols" in summary
        assert "supported_timeframes" in summary
        assert "data_status" in summary
        
        print("市场数据摘要:")
        print(f"  总交易对数: {summary['total_symbols']}")
        print(f"  运行状态: {summary['is_running']}")
        print(f"  监控交易对: {summary['symbols']}")
        print(f"  支持时间周期: {summary['supported_timeframes']}")
        
        print("✅ 市场数据摘要获取成功")
    
    @pytest.mark.asyncio
    async def test_short_polling_cycle(self, market_data_engine):
        """测试短时间轮询周期"""
        print("\n=== 测试短时间轮询周期 ===")
        
        # 启动轮询（短时间测试）
        await market_data_engine.start_data_polling(interval=5)
        
        assert market_data_engine.is_running is True
        print("轮询已启动")
        
        # 等待一段时间让轮询运行
        await asyncio.sleep(10)
        
        # 检查数据是否已更新
        status = market_data_engine.get_data_status()
        for symbol, symbol_status in status.items():
            print(f"{symbol} 最后更新时间: {symbol_status['last_update']}")
            assert symbol_status['last_update'] > 0
        
        # 停止轮询
        await market_data_engine.stop_data_polling()
        
        assert market_data_engine.is_running is False
        print("轮询已停止")
        
        print("✅ 轮询周期测试完成")


if __name__ == "__main__":
    # 运行真实API测试
    pytest.main([__file__, "-v", "-s", "-m", "integration"])
