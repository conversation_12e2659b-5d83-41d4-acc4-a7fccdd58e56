# API 文档

## 概述

本文档详细描述了DeepSeek加密货币永续合约全自动量化系统前端JavaScript API的使用方法。

## 目录

- [核心模块](#核心模块)
- [工具函数](#工具函数)
- [服务层](#服务层)
- [组件系统](#组件系统)
- [测试框架](#测试框架)

## 核心模块

### StateManager - 状态管理器

状态管理器提供集中式的应用状态管理功能。

#### 导入

```javascript
import { stateManager } from './src/web/static/services/core/StateManager.js';
```

#### API 方法

##### `getState(path?: string): any`

获取状态值。

**参数:**
- `path` (可选): 状态路径，使用点号分隔，如 `'user.profile.name'`

**返回值:**
- 如果提供路径，返回对应的状态值
- 如果不提供路径，返回完整的状态对象

**示例:**
```javascript
// 获取完整状态
const fullState = stateManager.getState();

// 获取特定路径的状态
const userName = stateManager.getState('user.name');
const userProfile = stateManager.getState('user.profile');
```

##### `setState(pathOrState: string | object, value?: any, options?: object): StateManager`

设置状态值。

**参数:**
- `pathOrState`: 状态路径字符串或状态对象
- `value` (可选): 当第一个参数为路径时的状态值
- `options` (可选): 设置选项

**返回值:**
- 返回StateManager实例，支持链式调用

**示例:**
```javascript
// 设置单个状态
stateManager.setState('user.name', 'John');

// 设置嵌套状态
stateManager.setState('user.profile.avatar', 'avatar.jpg');

// 批量设置状态
stateManager.setState({
  'user.name': 'Jane',
  'app.theme': 'dark'
});

// 合并状态对象
stateManager.setState({ user: { age: 30 } });
```

##### `subscribe(pathOrCallback: string | function, callback?: function, options?: object): function`

订阅状态变化。

**参数:**
- `pathOrCallback`: 状态路径或全局回调函数
- `callback` (可选): 当第一个参数为路径时的回调函数
- `options` (可选): 订阅选项

**返回值:**
- 返回取消订阅的函数

**示例:**
```javascript
// 订阅全局状态变化
const unsubscribeGlobal = stateManager.subscribe((state, action, oldState) => {
  console.log('状态已更新:', state);
});

// 订阅特定路径的状态变化
const unsubscribeUser = stateManager.subscribe('user.name', (newValue, oldValue, action) => {
  console.log(`用户名从 ${oldValue} 变更为 ${newValue}`);
});

// 取消订阅
unsubscribeGlobal();
unsubscribeUser();
```

##### `reset(newState?: object): StateManager`

重置状态。

**参数:**
- `newState` (可选): 新的初始状态，默认为空对象

**示例:**
```javascript
// 重置为空状态
stateManager.reset();

// 重置为指定状态
stateManager.reset({ user: { name: 'Default' } });
```

### ErrorHandler - 错误处理器

全局错误处理和恢复系统。

#### 导入

```javascript
import { errorHandler } from './src/web/static/services/core/ErrorHandler.js';
```

#### API 方法

##### `handleError(error: Error | object | string, context?: object): string`

处理错误。

**参数:**
- `error`: 错误对象、错误信息对象或错误字符串
- `context` (可选): 错误上下文信息

**返回值:**
- 返回错误ID

**示例:**
```javascript
// 处理Error对象
try {
  throw new Error('Something went wrong');
} catch (error) {
  const errorId = errorHandler.handleError(error, { 
    component: 'UserProfile',
    action: 'updateProfile'
  });
}

// 处理自定义错误对象
errorHandler.handleError({
  type: 'validation',
  severity: 'warning',
  message: '输入数据无效',
  field: 'email'
});
```

##### `registerHandler(errorType: string, handler: function): void`

注册错误处理器。

**参数:**
- `errorType`: 错误类型
- `handler`: 处理函数

**示例:**
```javascript
errorHandler.registerHandler('network', (error) => {
  console.log('网络错误:', error.message);
  // 显示重试按钮
  showRetryButton();
});
```

##### `getErrorLog(filters?: object): Array`

获取错误日志。

**参数:**
- `filters` (可选): 过滤条件

**示例:**
```javascript
// 获取所有错误
const allErrors = errorHandler.getErrorLog();

// 获取特定类型的错误
const networkErrors = errorHandler.getErrorLog({ type: 'network' });

// 获取高严重级别的错误
const criticalErrors = errorHandler.getErrorLog({ severity: 'high' });
```

### PerformanceMonitor - 性能监控器

性能监控和分析系统。

#### 导入

```javascript
import { performanceMonitor } from './src/web/static/services/core/PerformanceMonitor.js';
```

#### API 方法

##### `mark(name: string): void`

添加性能标记。

**示例:**
```javascript
performanceMonitor.mark('component-render-start');
// ... 组件渲染逻辑
performanceMonitor.mark('component-render-end');
```

##### `measure(name: string, startMark: string, endMark: string): void`

测量性能指标。

**示例:**
```javascript
performanceMonitor.measure('component-render-time', 'component-render-start', 'component-render-end');
```

##### `getMetrics(): object`

获取性能指标。

**示例:**
```javascript
const metrics = performanceMonitor.getMetrics();
console.log('性能指标:', metrics);
```

## 工具函数

### 格式化工具

#### 货币格式化

```javascript
import { formatCurrency } from './src/web/static/utils/formatters/currency.js';

// 基础用法
const formatted = formatCurrency(1234.56); // "¥1,234.56"

// 自定义选项
const usdFormatted = formatCurrency(1234.56, {
  currency: 'USD',
  symbol: '$',
  precision: 2
}); // "$1,234.56"
```

#### 百分比格式化

```javascript
import { formatPercentage } from './src/web/static/utils/formatters/percentage.js';

const percentage = formatPercentage(0.1234); // "12.34%"
const customPrecision = formatPercentage(0.123456, { precision: 4 }); // "12.3456%"
```

#### 日期时间格式化

```javascript
import { formatDateTime, formatRelativeTime } from './src/web/static/utils/formatters/datetime.js';

const date = new Date();
const formatted = formatDateTime(date); // "2025-08-03 10:30:45"
const relative = formatRelativeTime(Date.now() - 300000); // "5分钟前"
```

### 数据验证

```javascript
import { validateEmail, validateNumber, validateRequired } from './src/web/static/utils/validators/index.js';

// 邮箱验证
const isValidEmail = validateEmail('<EMAIL>'); // true

// 数字验证
const isValidNumber = validateNumber('123.45', { min: 0, max: 1000 }); // true

// 必填验证
const isRequired = validateRequired('some value'); // true
```

## 服务层

### API客户端

```javascript
import { apiClient } from './src/web/static/services/api/client.js';

// GET请求
const response = await apiClient.get('/api/users');

// POST请求
const newUser = await apiClient.post('/api/users', {
  name: 'John',
  email: '<EMAIL>'
});

// 添加请求拦截器
apiClient.addRequestInterceptor((config) => {
  config.headers.Authorization = `Bearer ${token}`;
  return config;
});

// 添加响应拦截器
apiClient.addResponseInterceptor((response) => {
  if (response.status === 401) {
    // 处理未授权
    redirectToLogin();
  }
  return response;
});
```

### 缓存服务

```javascript
import { cacheService } from './src/web/static/services/api/cache.js';

// 设置缓存
cacheService.set('user-profile', userData, { ttl: 300000 }); // 5分钟TTL

// 获取缓存
const cachedData = cacheService.get('user-profile');

// 删除缓存
cacheService.delete('user-profile');

// 清空所有缓存
cacheService.clear();
```

### 通知服务

```javascript
import { notificationService } from './src/web/static/services/ui/notification.js';

// 显示成功通知
notificationService.success('操作成功', '数据已保存');

// 显示错误通知
notificationService.error('操作失败', '网络连接异常');

// 显示警告通知
notificationService.warning('注意', '数据即将过期');

// 显示信息通知
notificationService.info('提示', '有新消息');

// 自定义通知
notificationService.show({
  type: 'custom',
  title: '自定义通知',
  message: '这是一个自定义通知',
  duration: 5000,
  actions: [
    { text: '确定', action: () => console.log('确定') },
    { text: '取消', action: () => console.log('取消') }
  ]
});
```

## 组件系统

### 组件基类

```javascript
import { Component } from './src/web/static/components/base/Component.js';

class MyComponent extends Component {
  constructor(element, options = {}) {
    super(element, options);
  }

  // 重写生命周期方法
  onMount() {
    console.log('组件已挂载');
  }

  onUnmount() {
    console.log('组件已卸载');
  }

  // 自定义方法
  updateData(data) {
    this.setState({ data });
    this.render();
  }

  render() {
    const { data } = this.getState();
    this.element.innerHTML = `<div>${data}</div>`;
  }
}

// 使用组件
const element = document.querySelector('#my-component');
const component = new MyComponent(element, { 
  initialData: 'Hello World' 
});
```

### 组件工厂

```javascript
import { componentFactory } from './src/web/static/components/base/ComponentFactory.js';

// 注册组件
componentFactory.register('my-component', MyComponent);

// 创建组件
const component = componentFactory.create('my-component', element, options);

// 自动初始化页面组件
componentFactory.autoInitialize(); // 扫描页面中的 [data-component] 元素
```

## 测试框架

### 基础测试

```javascript
import { testFramework } from './src/web/static/services/testing/TestFramework.js';

// 测试套件
testFramework.describe('我的测试套件', () => {
  let testData;

  // 前置钩子
  testFramework.beforeEach(() => {
    testData = { count: 0 };
  });

  // 测试用例
  testFramework.it('应该正确增加计数', () => {
    testData.count++;
    testFramework.expect(testData.count).toBe(1);
  });

  // 异步测试
  testFramework.it('应该正确处理异步操作', async () => {
    const result = await new Promise(resolve => {
      setTimeout(() => resolve('success'), 100);
    });
    
    testFramework.expect(result).toBe('success');
  });

  // 后置钩子
  testFramework.afterEach(() => {
    testData = null;
  });
});

// 运行测试
testFramework.run().then(results => {
  console.log('测试结果:', results);
});
```

### 断言方法

```javascript
// 相等断言
testFramework.expect(actual).toBe(expected);
testFramework.expect(actual).toEqual(expected); // 深度相等

// 真假断言
testFramework.expect(value).toBeTruthy();
testFramework.expect(value).toBeFalsy();

// 包含断言
testFramework.expect(array).toContain(item);
testFramework.expect(string).toContain(substring);

// 异常断言
testFramework.expect(() => {
  throw new Error('test error');
}).toThrow('test error');

// 类型断言
testFramework.expect(instance).toBeInstanceOf(MyClass);

// 取反断言
testFramework.expect(value).not.toBe(otherValue);
```

### 模拟和间谍

```javascript
// 创建模拟对象
const mockObject = testFramework.mock({
  method1: () => 'mocked result',
  method2: (arg) => `mocked ${arg}`
});

// 创建间谍函数
const spy = testFramework.spy(object, 'methodName');

// 检查调用
testFramework.expect(spy.calls.length).toBe(1);
testFramework.expect(spy.calls[0].args).toEqual(['arg1', 'arg2']);
```

## 配置选项

### 环境配置

```javascript
import { config } from './src/web/static/utils/config/environment.js';

// 获取配置
const apiUrl = config.get('API_BASE_URL');
const isDevelopment = config.isDevelopment();
const isProduction = config.isProduction();

// 设置配置
config.set('CUSTOM_SETTING', 'value');
```

### 主题配置

```javascript
import { themeConfig } from './src/web/static/utils/config/theme.js';

// 应用主题
themeConfig.applyTheme('dark');

// 获取当前主题
const currentTheme = themeConfig.getCurrentTheme();

// 注册自定义主题
themeConfig.registerTheme('custom', {
  colors: {
    primary: '#007bff',
    secondary: '#6c757d'
  }
});
```

## 错误处理

所有API方法都遵循统一的错误处理模式：

```javascript
try {
  const result = await apiMethod();
  // 处理成功结果
} catch (error) {
  // 错误会自动被ErrorHandler处理
  console.error('操作失败:', error.message);
}
```

## 性能考虑

- 所有异步操作都支持取消
- 大数据集操作使用分页或虚拟滚动
- 组件支持懒加载和按需渲染
- 状态更新使用批处理优化
- 自动内存泄漏检测和清理

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 更新日志

### v1.0.0 (2025-08-03)
- 初始版本发布
- 完整的模块化架构
- 状态管理系统
- 错误处理系统
- 性能监控系统
- 测试框架

## 相关文档

- [用户指南](./user-guide.md) - 详细的使用指南和最佳实践
- [部署指南](./deployment-guide.md) - 部署和配置说明
- [故障排除](./troubleshooting.md) - 常见问题和解决方案
- [代码质量审查](./code-quality-review.md) - 代码质量评估报告
