#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理服务

此模块负责系统配置的管理，包括：
1. 配置文件的加载和保存
2. 配置验证和校验
3. 配置变更通知
4. 配置备份和恢复
5. 运行时配置更新
"""

import os
import json
import time
import shutil
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from src.utils.logger import get_logger
from src.utils.exceptions import ConfigurationError
from src.data.database_manager import DatabaseManager
from src.data.models import ExchangeConfig, TradingParameters, RiskParameters

logger = get_logger(__name__)


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    config_type: str
    old_value: Any
    new_value: Any
    timestamp: float
    user: Optional[str] = None


class ConfigService:
    """配置管理服务类"""
    
    def __init__(self, config_dir: str = "config", backup_dir: str = "data/backups"):
        """初始化配置服务
        
        Args:
            config_dir: 配置文件目录
            backup_dir: 备份目录
        """
        self.config_dir = Path(config_dir)
        self.backup_dir = Path(backup_dir)
        self.db_manager = DatabaseManager()
        
        # 配置变更监听器
        self.change_listeners: Dict[str, List[Callable]] = {}
        
        # 配置缓存
        self.config_cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, float] = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        # 确保目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("配置管理服务初始化完成")
    
    def get_exchange_config(self, use_cache: bool = True) -> Optional[ExchangeConfig]:
        """获取交易所配置
        
        Args:
            use_cache: 是否使用缓存
        
        Returns:
            ExchangeConfig: 交易所配置对象
        """
        try:
            cache_key = "exchange_config"
            
            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key):
                config_dict = self.config_cache[cache_key]
                return ExchangeConfig(**config_dict) if config_dict else None
            
            # 从数据库加载
            config_dict = self.db_manager.load_exchange_config()
            
            # 更新缓存
            self.config_cache[cache_key] = config_dict
            self.cache_timestamps[cache_key] = time.time()
            
            if config_dict:
                return ExchangeConfig(**config_dict)
            return None
            
        except Exception as e:
            logger.error(f"获取交易所配置失败: {e}")
            raise ConfigurationError(f"获取交易所配置失败: {e}")
    
    def save_exchange_config(self, config: ExchangeConfig, user: Optional[str] = None) -> bool:
        """保存交易所配置
        
        Args:
            config: 交易所配置对象
            user: 操作用户
        
        Returns:
            bool: 是否保存成功
        """
        try:
            # 获取旧配置用于变更通知
            old_config = self.get_exchange_config(use_cache=False)
            
            # 验证配置
            self._validate_exchange_config(config)
            
            # 备份当前配置
            if old_config:
                self._backup_config("exchange", asdict(old_config))
            
            # 保存到数据库
            config_dict = asdict(config)
            success = self.db_manager.save_exchange_config(config_dict)
            
            if success:
                # 清除缓存
                self._clear_cache("exchange_config")
                
                # 触发变更事件
                self._notify_config_change(
                    "exchange_config",
                    asdict(old_config) if old_config else None,
                    config_dict,
                    user
                )
                
                logger.info(f"交易所配置保存成功: {config.exchange_name}")
                return True
            else:
                logger.error("交易所配置保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存交易所配置失败: {e}")
            raise ConfigurationError(f"保存交易所配置失败: {e}")
    
    def get_trading_parameters(self, use_cache: bool = True) -> TradingParameters:
        """获取交易参数
        
        Args:
            use_cache: 是否使用缓存
        
        Returns:
            TradingParameters: 交易参数对象
        """
        try:
            cache_key = "trading_parameters"
            
            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key):
                params_dict = self.config_cache[cache_key]
                return TradingParameters(**params_dict) if params_dict else TradingParameters()
            
            # 从数据库加载
            params_dict = self.db_manager.load_trading_parameters()
            
            # 更新缓存
            self.config_cache[cache_key] = params_dict
            self.cache_timestamps[cache_key] = time.time()
            
            if params_dict:
                return TradingParameters(**params_dict)
            return TradingParameters()  # 返回默认参数
            
        except Exception as e:
            logger.error(f"获取交易参数失败: {e}")
            raise ConfigurationError(f"获取交易参数失败: {e}")
    
    def save_trading_parameters(self, params: TradingParameters, user: Optional[str] = None) -> bool:
        """保存交易参数
        
        Args:
            params: 交易参数对象
            user: 操作用户
        
        Returns:
            bool: 是否保存成功
        """
        try:
            # 获取旧参数用于变更通知
            old_params = self.get_trading_parameters(use_cache=False)
            
            # 验证参数
            self._validate_trading_parameters(params)
            
            # 备份当前参数
            self._backup_config("trading_parameters", asdict(old_params))
            
            # 保存到数据库
            params_dict = asdict(params)
            success = self.db_manager.save_trading_parameters(params_dict)
            
            if success:
                # 清除缓存
                self._clear_cache("trading_parameters")
                
                # 触发变更事件
                self._notify_config_change(
                    "trading_parameters",
                    asdict(old_params),
                    params_dict,
                    user
                )
                
                logger.info("交易参数保存成功")
                return True
            else:
                logger.error("交易参数保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存交易参数失败: {e}")
            raise ConfigurationError(f"保存交易参数失败: {e}")
    
    def get_risk_parameters(self, use_cache: bool = True) -> RiskParameters:
        """获取风险参数
        
        Args:
            use_cache: 是否使用缓存
        
        Returns:
            RiskParameters: 风险参数对象
        """
        try:
            cache_key = "risk_parameters"
            
            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key):
                params_dict = self.config_cache[cache_key]
                return RiskParameters(**params_dict) if params_dict else RiskParameters()
            
            # 从数据库加载
            params_dict = self.db_manager.load_risk_parameters()
            
            # 更新缓存
            self.config_cache[cache_key] = params_dict
            self.cache_timestamps[cache_key] = time.time()
            
            if params_dict:
                return RiskParameters(**params_dict)
            return RiskParameters()  # 返回默认参数
            
        except Exception as e:
            logger.error(f"获取风险参数失败: {e}")
            raise ConfigurationError(f"获取风险参数失败: {e}")
    
    def save_risk_parameters(self, params: RiskParameters, user: Optional[str] = None) -> bool:
        """保存风险参数
        
        Args:
            params: 风险参数对象
            user: 操作用户
        
        Returns:
            bool: 是否保存成功
        """
        try:
            # 获取旧参数用于变更通知
            old_params = self.get_risk_parameters(use_cache=False)
            
            # 验证参数
            self._validate_risk_parameters(params)
            
            # 备份当前参数
            self._backup_config("risk_parameters", asdict(old_params))
            
            # 保存到数据库
            params_dict = asdict(params)
            success = self.db_manager.save_risk_parameters(params_dict)
            
            if success:
                # 清除缓存
                self._clear_cache("risk_parameters")
                
                # 触发变更事件
                self._notify_config_change(
                    "risk_parameters",
                    asdict(old_params),
                    params_dict,
                    user
                )
                
                logger.info("风险参数保存成功")
                return True
            else:
                logger.error("风险参数保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存风险参数失败: {e}")
            raise ConfigurationError(f"保存风险参数失败: {e}")
    
    def get_selected_symbols(self, use_cache: bool = True) -> List[str]:
        """获取选择的交易对
        
        Args:
            use_cache: 是否使用缓存
        
        Returns:
            List[str]: 交易对列表
        """
        try:
            cache_key = "selected_symbols"
            
            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key):
                return self.config_cache[cache_key] or []
            
            # 从数据库加载
            symbols = self.db_manager.load_selected_symbols()
            
            # 更新缓存
            self.config_cache[cache_key] = symbols
            self.cache_timestamps[cache_key] = time.time()
            
            return symbols or []
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}")
            raise ConfigurationError(f"获取交易对列表失败: {e}")
    
    def save_selected_symbols(self, symbols: List[str], user: Optional[str] = None) -> bool:
        """保存选择的交易对
        
        Args:
            symbols: 交易对列表
            user: 操作用户
        
        Returns:
            bool: 是否保存成功
        """
        try:
            # 获取旧列表用于变更通知
            old_symbols = self.get_selected_symbols(use_cache=False)
            
            # 验证交易对
            validated_symbols = self._validate_symbols(symbols)
            
            # 备份当前列表
            self._backup_config("selected_symbols", old_symbols)
            
            # 保存到数据库
            success = self.db_manager.save_selected_symbols(validated_symbols)
            
            if success:
                # 清除缓存
                self._clear_cache("selected_symbols")
                
                # 触发变更事件
                self._notify_config_change(
                    "selected_symbols",
                    old_symbols,
                    validated_symbols,
                    user
                )
                
                logger.info(f"交易对列表保存成功: {len(validated_symbols)} 个")
                return True
            else:
                logger.error("交易对列表保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存交易对列表失败: {e}")
            raise ConfigurationError(f"保存交易对列表失败: {e}")
    
    def add_config_change_listener(self, config_type: str, callback: Callable):
        """添加配置变更监听器
        
        Args:
            config_type: 配置类型
            callback: 回调函数
        """
        if config_type not in self.change_listeners:
            self.change_listeners[config_type] = []
        self.change_listeners[config_type].append(callback)
        logger.debug(f"添加配置变更监听器: {config_type}")
    
    def remove_config_change_listener(self, config_type: str, callback: Callable):
        """移除配置变更监听器
        
        Args:
            config_type: 配置类型
            callback: 回调函数
        """
        if config_type in self.change_listeners:
            try:
                self.change_listeners[config_type].remove(callback)
                logger.debug(f"移除配置变更监听器: {config_type}")
            except ValueError:
                pass
    
    def clear_cache(self):
        """清除所有缓存"""
        self.config_cache.clear()
        self.cache_timestamps.clear()
        logger.info("配置缓存已清除")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.config_cache:
            return False
        
        timestamp = self.cache_timestamps.get(cache_key, 0)
        return time.time() - timestamp < self.cache_ttl
    
    def _clear_cache(self, cache_key: str):
        """清除指定缓存"""
        self.config_cache.pop(cache_key, None)
        self.cache_timestamps.pop(cache_key, None)
    
    def _validate_exchange_config(self, config: ExchangeConfig):
        """验证交易所配置"""
        if not config.exchange_name:
            raise ConfigurationError("交易所名称不能为空")
        
        if not config.api_key:
            raise ConfigurationError("API密钥不能为空")
        
        if not config.secret_key:
            raise ConfigurationError("密钥不能为空")
        
        # 验证交易所名称
        supported_exchanges = ["okx", "binance", "huobi", "bybit"]
        if config.exchange_name.lower() not in supported_exchanges:
            raise ConfigurationError(f"不支持的交易所: {config.exchange_name}")
    
    def _validate_trading_parameters(self, params: TradingParameters):
        """验证交易参数"""
        if params.max_leverage < 1 or params.max_leverage > 100:
            raise ConfigurationError("最大杠杆必须在1-100之间")
        
        if params.max_position_ratio < 0.1 or params.max_position_ratio > 1.0:
            raise ConfigurationError("最大仓位比例必须在0.1-1.0之间")
        
        if params.opening_confidence_threshold < 0 or params.opening_confidence_threshold > 100:
            raise ConfigurationError("开仓置信度阈值必须在0-100之间")
        
        if params.position_confidence_threshold < 0 or params.position_confidence_threshold > 100:
            raise ConfigurationError("持仓置信度阈值必须在0-100之间")
    
    def _validate_risk_parameters(self, params: RiskParameters):
        """验证风险参数"""
        if params.max_leverage < 1 or params.max_leverage > 100:
            raise ConfigurationError("最大杠杆必须在1-100之间")
        
        if params.max_position_ratio < 0.1 or params.max_position_ratio > 1.0:
            raise ConfigurationError("最大仓位比例必须在0.1-1.0之间")
        
        if params.min_balance_threshold < 10:
            raise ConfigurationError("最小余额阈值不能小于10")
    
    def _validate_symbols(self, symbols: List[str]) -> List[str]:
        """验证交易对列表"""
        validated = []
        for symbol in symbols:
            if "/" in symbol and ":" in symbol:  # 永续合约格式
                validated.append(symbol.upper())
            else:
                logger.warning(f"无效的交易对格式: {symbol}")
        
        if not validated:
            raise ConfigurationError("没有有效的交易对")
        
        return validated
    
    def _backup_config(self, config_type: str, config_data: Any):
        """备份配置"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"{config_type}_{timestamp}.json"
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"配置已备份: {backup_file}")
            
        except Exception as e:
            logger.error(f"备份配置失败: {e}")
    
    def _notify_config_change(self, config_type: str, old_value: Any, new_value: Any, user: Optional[str]):
        """通知配置变更"""
        try:
            event = ConfigChangeEvent(
                config_type=config_type,
                old_value=old_value,
                new_value=new_value,
                timestamp=time.time(),
                user=user
            )
            
            # 调用监听器
            listeners = self.change_listeners.get(config_type, [])
            for listener in listeners:
                try:
                    listener(event)
                except Exception as e:
                    logger.error(f"配置变更监听器执行失败: {e}")
            
            logger.info(f"配置变更通知已发送: {config_type}")
            
        except Exception as e:
            logger.error(f"发送配置变更通知失败: {e}")
