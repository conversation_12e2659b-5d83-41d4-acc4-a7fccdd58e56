#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓计算调试工具

详细分析持仓数据和敞口计算逻辑。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


def debug_position_calculation():
    """调试持仓计算"""
    print("=== 持仓计算调试工具 ===")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    
    try:
        # 连接交易所
        exchange_client.connect()
        
        # 获取原始数据
        print("\n1. 获取原始数据...")
        balance = exchange_client.fetch_balance()
        positions = exchange_client.fetch_positions()
        
        print(f"余额数据: {len(balance)} 个币种")
        print(f"持仓数据: {len(positions)} 个持仓")
        
        # 详细分析余额
        print(f"\n2. 详细余额分析:")
        total_balance = 0
        for currency, bal in balance.items():
            if bal.total > 0:
                print(f"  {currency}:")
                print(f"    总计: {bal.total}")
                print(f"    可用: {bal.available}")
                print(f"    已用: {bal.used}")
                total_balance += bal.total
        
        print(f"\n总余额: {total_balance:.2f} USDT")
        
        # 详细分析每个持仓
        print(f"\n3. 详细持仓分析:")
        total_exposure = 0
        total_margin = 0
        
        for i, pos in enumerate(positions):
            print(f"\n持仓 {i+1}: {pos.symbol}")
            print(f"  方向: {pos.side.value}")
            print(f"  数量: {pos.amount}")
            print(f"  开仓价格: {pos.entry_price}")
            print(f"  当前价格: {pos.current_price}")
            print(f"  杠杆: {pos.leverage}x")
            print(f"  未实现盈亏: {pos.unrealized_pnl}")
            print(f"  未实现盈亏%: {pos.unrealized_pnl_percentage:.2f}%")
            print(f"  保证金: {pos.margin_used}")
            
            # 计算敞口的不同方法
            method1_exposure = abs(pos.amount) * pos.current_price
            method2_exposure = pos.margin_used * pos.leverage
            
            print(f"\n  敞口计算:")
            print(f"    方法1 (数量×价格): {method1_exposure:,.2f}")
            print(f"    方法2 (保证金×杠杆): {method2_exposure:,.2f}")
            
            # 检查哪种方法更合理
            if abs(method1_exposure - method2_exposure) / max(method1_exposure, method2_exposure) > 0.1:
                print(f"    ⚠️  两种方法差异较大: {abs(method1_exposure - method2_exposure):,.2f}")
            
            # 分析数据合理性
            print(f"\n  数据合理性检查:")
            if pos.amount > 1000:
                print(f"    ⚠️  持仓数量异常大: {pos.amount}")
            
            if method1_exposure > total_balance * 10:
                print(f"    🚨 敞口异常大: {method1_exposure:,.2f} (余额的{method1_exposure/total_balance:.1f}倍)")
            elif method1_exposure > total_balance:
                print(f"    ⚠️  敞口较大: {method1_exposure:,.2f} (余额的{method1_exposure/total_balance:.1f}倍)")
            else:
                print(f"    ✅ 敞口正常: {method1_exposure:,.2f}")
            
            # 使用方法1计算总敞口
            total_exposure += method1_exposure
            total_margin += pos.margin_used
        
        # 总体分析
        print(f"\n4. 总体风险分析:")
        print(f"总余额: {total_balance:,.2f} USDT")
        print(f"总敞口: {total_exposure:,.2f} USDT")
        print(f"总保证金: {total_margin:,.2f} USDT")
        print(f"敞口比率: {total_exposure/total_balance:.2%}")
        print(f"保证金使用率: {total_margin/total_balance:.2%}")
        
        # 分析问题
        print(f"\n5. 问题分析:")
        if total_exposure > total_balance * 2:
            print(f"🚨 敞口过高问题:")
            print(f"  - 敞口是余额的 {total_exposure/total_balance:.1f} 倍")
            print(f"  - 可能原因:")
            print(f"    1. 持仓数量单位错误")
            print(f"    2. 价格单位错误") 
            print(f"    3. 测试环境数据异常")
            print(f"    4. API返回数据格式问题")
        
        # 建议的修复方案
        print(f"\n6. 建议修复方案:")
        
        # 方案1：使用保证金×杠杆计算敞口
        total_exposure_method2 = sum(pos.margin_used * pos.leverage for pos in positions)
        print(f"方案1 - 使用保证金×杠杆:")
        print(f"  总敞口: {total_exposure_method2:,.2f} USDT")
        print(f"  敞口比率: {total_exposure_method2/total_balance:.2%}")
        
        # 方案2：过滤异常持仓
        normal_positions = []
        for pos in positions:
            exposure = abs(pos.amount) * pos.current_price
            if exposure <= total_balance * 5:  # 敞口不超过余额5倍
                normal_positions.append(pos)
        
        total_exposure_filtered = sum(abs(pos.amount) * pos.current_price for pos in normal_positions)
        print(f"\n方案2 - 过滤异常持仓:")
        print(f"  正常持仓: {len(normal_positions)}/{len(positions)}")
        print(f"  总敞口: {total_exposure_filtered:,.2f} USDT")
        print(f"  敞口比率: {total_exposure_filtered/total_balance:.2%}")
        
        # 方案3：检查原始API数据
        print(f"\n方案3 - 检查原始API数据:")
        print(f"建议检查交易所API返回的原始数据格式")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print(f"\n🧹 连接已断开")


if __name__ == "__main__":
    success = debug_position_calculation()
    if success:
        print(f"\n✅ 持仓计算调试完成！")
    else:
        print(f"\n❌ 持仓计算调试失败！")
        sys.exit(1)
