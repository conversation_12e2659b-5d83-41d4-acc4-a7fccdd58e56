#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单异步测试

只测试核心异步功能。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


async def test_simple_async():
    """简单异步测试"""
    print("=== 简单异步测试 ===")
    
    # 创建配置
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建交易所客户端
    exchange_client = ExchangeClient(config)
    
    try:
        # 连接交易所
        print("1. 连接交易所...")
        exchange_client.connect()
        print("✅ 交易所连接成功")
        
        # 测试异步获取K线数据
        print("\n2. 测试异步获取K线数据...")
        symbol = "BTC/USDT:USDT"
        timeframe = "1m"
        limit = 5
        
        print(f"异步获取 {symbol} {timeframe} K线数据...")
        ohlcv_data = await exchange_client.fetch_ohlcv(symbol, timeframe, limit)
        
        print(f"获取到 {len(ohlcv_data)} 条K线数据")
        if ohlcv_data:
            latest = ohlcv_data[-1]
            print(f"最新K线: {latest.datetime} - 收盘价: {latest.close}")
        
        print("✅ 异步K线数据获取成功")
        
        # 测试并发获取多个时间周期
        print("\n3. 测试并发获取多个时间周期...")
        
        timeframes = ["1m", "5m", "15m"]
        tasks = []
        
        for tf in timeframes:
            task = asyncio.create_task(exchange_client.fetch_ohlcv(symbol, tf, 3))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        print("并发获取结果:")
        for tf, result in zip(timeframes, results):
            print(f"  {tf}: {len(result)} 条数据")
        
        print("✅ 并发获取成功")
        
        print("\n🎉 异步测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    # 运行异步测试
    success = asyncio.run(test_simple_async())
    if success:
        print("\n✅ 简单异步测试通过！")
    else:
        print("\n❌ 简单异步测试失败！")
        sys.exit(1)
