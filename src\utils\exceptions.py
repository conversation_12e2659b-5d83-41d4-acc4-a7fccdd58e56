#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统自定义异常类模块

此模块定义了系统中使用的所有自定义异常类，包括：
1. 基础异常类
2. 交易所相关异常
3. AI服务相关异常
4. 数据相关异常
5. 风险管理相关异常
"""

from typing import Optional, Dict, Any


class TradingSystemError(Exception):
    """交易系统基础异常类
    
    所有系统自定义异常的基类，提供统一的异常处理接口。
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        """初始化异常
        
        Args:
            message: 异常消息
            error_code: 错误代码
            details: 异常详细信息
            original_exception: 原始异常（如果是包装其他异常）
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.original_exception = original_exception
    
    def to_dict(self) -> Dict[str, Any]:
        """将异常转换为字典格式
        
        Returns:
            Dict[str, Any]: 异常信息字典
        """
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


# =============================================================================
# 交易所相关异常
# =============================================================================

class ExchangeError(TradingSystemError):
    """交易所相关异常基类"""
    pass


class ExchangeConnectionError(ExchangeError):
    """交易所连接异常
    
    当无法连接到交易所API时抛出此异常。
    处理策略：重试连接，记录错误，暂停交易
    """
    
    def __init__(self, exchange_name: str, message: str = "", **kwargs):
        super().__init__(
            message or f"无法连接到交易所 {exchange_name}",
            error_code="EXCHANGE_CONNECTION_ERROR",
            details={"exchange_name": exchange_name},
            **kwargs
        )
        self.exchange_name = exchange_name


class ExchangeAPIError(ExchangeError):
    """交易所API异常
    
    当交易所API返回错误时抛出此异常。
    """
    
    def __init__(self, exchange_name: str, api_error: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(
            f"交易所 {exchange_name} API错误: {api_error}",
            error_code="EXCHANGE_API_ERROR",
            details={
                "exchange_name": exchange_name,
                "api_error": api_error,
                "status_code": status_code
            },
            **kwargs
        )
        self.exchange_name = exchange_name
        self.api_error = api_error
        self.status_code = status_code


class ExchangeAuthenticationError(ExchangeError):
    """交易所认证异常
    
    当API密钥认证失败时抛出此异常。
    """
    
    def __init__(self, exchange_name: str, **kwargs):
        super().__init__(
            f"交易所 {exchange_name} 认证失败，请检查API密钥配置",
            error_code="EXCHANGE_AUTH_ERROR",
            details={"exchange_name": exchange_name},
            **kwargs
        )
        self.exchange_name = exchange_name


class InsufficientBalanceError(ExchangeError):
    """余额不足异常
    
    当账户余额不足以执行交易时抛出此异常。
    处理策略：拒绝交易，发出警告
    """
    
    def __init__(self, required_amount: float, available_amount: float, currency: str = "USDT", **kwargs):
        super().__init__(
            f"余额不足：需要 {required_amount} {currency}，可用 {available_amount} {currency}",
            error_code="INSUFFICIENT_BALANCE",
            details={
                "required_amount": required_amount,
                "available_amount": available_amount,
                "currency": currency
            },
            **kwargs
        )
        self.required_amount = required_amount
        self.available_amount = available_amount
        self.currency = currency


class OrderExecutionError(ExchangeError):
    """订单执行异常
    
    当订单执行失败时抛出此异常。
    """
    
    def __init__(self, order_type: str, symbol: str, reason: str, **kwargs):
        super().__init__(
            f"订单执行失败：{order_type} {symbol} - {reason}",
            error_code="ORDER_EXECUTION_ERROR",
            details={
                "order_type": order_type,
                "symbol": symbol,
                "reason": reason
            },
            **kwargs
        )
        self.order_type = order_type
        self.symbol = symbol
        self.reason = reason


# =============================================================================
# AI服务相关异常
# =============================================================================

class AIServiceError(TradingSystemError):
    """AI服务异常基类
    
    处理策略：保持当前状态，记录错误，等待恢复
    """
    pass


class DeepSeekAPIError(AIServiceError):
    """DeepSeek API异常
    
    当DeepSeek API调用失败时抛出此异常。
    """
    
    def __init__(self, api_error: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(
            f"DeepSeek API错误: {api_error}",
            error_code="DEEPSEEK_API_ERROR",
            details={
                "api_error": api_error,
                "status_code": status_code
            },
            **kwargs
        )
        self.api_error = api_error
        self.status_code = status_code


class AIResponseParsingError(AIServiceError):
    """AI响应解析异常
    
    当无法解析AI响应时抛出此异常。
    """
    
    def __init__(self, response_text: str, parsing_error: str, **kwargs):
        super().__init__(
            f"AI响应解析失败: {parsing_error}",
            error_code="AI_RESPONSE_PARSING_ERROR",
            details={
                "response_text": response_text,
                "parsing_error": parsing_error
            },
            **kwargs
        )
        self.response_text = response_text
        self.parsing_error = parsing_error


class AIConfidenceTooLowError(AIServiceError):
    """AI置信度过低异常
    
    当AI决策置信度低于阈值时抛出此异常。
    """
    
    def __init__(self, confidence: float, threshold: float, decision_type: str, **kwargs):
        super().__init__(
            f"AI {decision_type} 置信度过低: {confidence}% < {threshold}%",
            error_code="AI_CONFIDENCE_TOO_LOW",
            details={
                "confidence": confidence,
                "threshold": threshold,
                "decision_type": decision_type
            },
            **kwargs
        )
        self.confidence = confidence
        self.threshold = threshold
        self.decision_type = decision_type


# =============================================================================
# 数据相关异常
# =============================================================================

class DataError(TradingSystemError):
    """数据相关异常基类"""
    pass


class DataInsufficientError(DataError):
    """数据不足异常
    
    当历史数据不足以进行分析时抛出此异常。
    处理策略：等待更多数据，跳过当前分析
    """
    
    def __init__(self, required_count: int, available_count: int, data_type: str, **kwargs):
        super().__init__(
            f"{data_type} 数据不足：需要 {required_count} 条，可用 {available_count} 条",
            error_code="DATA_INSUFFICIENT",
            details={
                "required_count": required_count,
                "available_count": available_count,
                "data_type": data_type
            },
            **kwargs
        )
        self.required_count = required_count
        self.available_count = available_count
        self.data_type = data_type


class DataValidationError(DataError):
    """数据验证异常
    
    当数据验证失败时抛出此异常。
    """
    
    def __init__(self, field_name: str, field_value: Any, validation_rule: str, **kwargs):
        super().__init__(
            f"数据验证失败：{field_name} = {field_value} 不符合规则 {validation_rule}",
            error_code="DATA_VALIDATION_ERROR",
            details={
                "field_name": field_name,
                "field_value": field_value,
                "validation_rule": validation_rule
            },
            **kwargs
        )
        self.field_name = field_name
        self.field_value = field_value
        self.validation_rule = validation_rule


class DatabaseError(DataError):
    """数据库异常
    
    当数据库操作失败时抛出此异常。
    """
    
    def __init__(self, operation: str, table_name: str, db_error: str, **kwargs):
        super().__init__(
            f"数据库操作失败：{operation} {table_name} - {db_error}",
            error_code="DATABASE_ERROR",
            details={
                "operation": operation,
                "table_name": table_name,
                "db_error": db_error
            },
            **kwargs
        )
        self.operation = operation
        self.table_name = table_name
        self.db_error = db_error


# =============================================================================
# 风险管理相关异常
# =============================================================================

class RiskManagementError(TradingSystemError):
    """风险管理异常基类"""
    pass


class RiskViolationError(RiskManagementError):
    """风险控制违规异常
    
    当操作违反风险控制规则时抛出此异常。
    处理策略：拒绝操作，记录原因
    """
    
    def __init__(self, rule_name: str, rule_description: str, violation_details: Dict[str, Any], **kwargs):
        super().__init__(
            f"违反风险控制规则：{rule_name} - {rule_description}",
            error_code="RISK_VIOLATION",
            details={
                "rule_name": rule_name,
                "rule_description": rule_description,
                "violation_details": violation_details
            },
            **kwargs
        )
        self.rule_name = rule_name
        self.rule_description = rule_description
        self.violation_details = violation_details


class LeverageTooHighError(RiskViolationError):
    """杠杆过高异常"""
    
    def __init__(self, requested_leverage: int, max_leverage: int, **kwargs):
        super().__init__(
            rule_name="最大杠杆限制",
            rule_description=f"杠杆不能超过 {max_leverage} 倍",
            violation_details={
                "requested_leverage": requested_leverage,
                "max_leverage": max_leverage
            },
            **kwargs
        )
        self.requested_leverage = requested_leverage
        self.max_leverage = max_leverage


class PositionSizeTooLargeError(RiskViolationError):
    """仓位过大异常"""
    
    def __init__(self, requested_ratio: float, max_ratio: float, **kwargs):
        super().__init__(
            rule_name="最大仓位比例限制",
            rule_description=f"仓位比例不能超过 {max_ratio * 100}%",
            violation_details={
                "requested_ratio": requested_ratio,
                "max_ratio": max_ratio
            },
            **kwargs
        )
        self.requested_ratio = requested_ratio
        self.max_ratio = max_ratio


# =============================================================================
# 系统监控相关异常
# =============================================================================

class SystemMonitoringError(TradingSystemError):
    """系统监控异常基类"""
    pass


class NotificationError(TradingSystemError):
    """通知服务异常基类"""
    pass


# =============================================================================
# 配置相关异常
# =============================================================================

class ConfigurationError(TradingSystemError):
    """配置异常基类"""
    pass


class MissingConfigurationError(ConfigurationError):
    """缺少配置异常"""
    
    def __init__(self, config_name: str, **kwargs):
        super().__init__(
            f"缺少必需的配置项: {config_name}",
            error_code="MISSING_CONFIGURATION",
            details={"config_name": config_name},
            **kwargs
        )
        self.config_name = config_name


class InvalidConfigurationError(ConfigurationError):
    """无效配置异常"""
    
    def __init__(self, config_name: str, config_value: Any, reason: str, **kwargs):
        super().__init__(
            f"无效的配置项 {config_name} = {config_value}: {reason}",
            error_code="INVALID_CONFIGURATION",
            details={
                "config_name": config_name,
                "config_value": config_value,
                "reason": reason
            },
            **kwargs
        )
        self.config_name = config_name
        self.config_value = config_value
        self.reason = reason
