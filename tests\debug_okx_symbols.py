#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试OKX交易对符号格式
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


def debug_okx_symbols():
    """调试OKX交易对符号"""
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    client = ExchangeClient(config)
    
    try:
        client.connect()
        
        # 获取市场信息
        markets = client.fetch_markets()
        
        print(f"总共 {len(markets)} 个永续合约")
        print("\n=== BTC相关的永续合约 ===")
        
        btc_markets = []
        for market in markets:
            symbol = market.get('symbol', '')
            if 'BTC' in symbol and 'USDT' in symbol and market.get('type') == 'swap':
                btc_markets.append(market)
        
        print(f"找到 {len(btc_markets)} 个BTC/USDT相关的永续合约:")
        for i, market in enumerate(btc_markets[:10]):  # 只显示前10个
            print(f"  {i+1}. {market['symbol']} - {market.get('type', 'N/A')}")
        
        # 尝试找到正确的BTC/USDT永续合约符号
        if btc_markets:
            test_symbol = btc_markets[0]['symbol']
            print(f"\n=== 测试符号: {test_symbol} ===")
            
            try:
                # 测试获取K线数据
                ohlcv_data = client.fetch_ohlcv_sync(test_symbol, "1m", 2)
                print(f"✅ 成功获取 {len(ohlcv_data)} 条K线数据")
                if ohlcv_data:
                    latest = ohlcv_data[-1]
                    print(f"最新K线: {latest.datetime} - 收盘价: {latest.close}")
            except Exception as e:
                print(f"❌ 获取K线失败: {e}")
        
        print("\n=== ETH相关的永续合约 ===")
        eth_markets = []
        for market in markets:
            symbol = market.get('symbol', '')
            if 'ETH' in symbol and 'USDT' in symbol and market.get('type') == 'swap':
                eth_markets.append(market)
        
        print(f"找到 {len(eth_markets)} 个ETH/USDT相关的永续合约:")
        for i, market in enumerate(eth_markets[:5]):  # 只显示前5个
            print(f"  {i+1}. {market['symbol']} - {market.get('type', 'N/A')}")
    
    finally:
        client.disconnect()


if __name__ == "__main__":
    debug_okx_symbols()
