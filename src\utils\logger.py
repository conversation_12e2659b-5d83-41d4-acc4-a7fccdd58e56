#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统日志工具模块

此模块提供便捷的日志记录工具函数，包括：
1. 日志记录器获取
2. 结构化日志记录
3. 性能监控日志
4. 错误追踪日志
"""

import time
import functools
from typing import Any, Callable, Optional, Dict
from loguru import logger


def get_logger(name: str) -> Any:
    """获取命名日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        Any: loguru日志记录器实例
    """
    return logger.bind(name=name)


def log_execution_time(func_name: Optional[str] = None):
    """记录函数执行时间的装饰器
    
    Args:
        func_name: 自定义函数名称，如果为None则使用实际函数名
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = func_name or func.__name__
            logger_instance = get_logger(func.__module__)
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger_instance.info(
                    f"函数 {function_name} 执行完成",
                    execution_time=f"{execution_time:.4f}s"
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger_instance.error(
                    f"函数 {function_name} 执行失败: {e}",
                    execution_time=f"{execution_time:.4f}s",
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


def log_async_execution_time(func_name: Optional[str] = None):
    """记录异步函数执行时间的装饰器
    
    Args:
        func_name: 自定义函数名称，如果为None则使用实际函数名
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = func_name or func.__name__
            logger_instance = get_logger(func.__module__)
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger_instance.info(
                    f"异步函数 {function_name} 执行完成",
                    execution_time=f"{execution_time:.4f}s"
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger_instance.error(
                    f"异步函数 {function_name} 执行失败: {e}",
                    execution_time=f"{execution_time:.4f}s",
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


def log_api_call(api_name: str, endpoint: str = ""):
    """记录API调用的装饰器
    
    Args:
        api_name: API名称
        endpoint: API端点
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger_instance = get_logger("api")
            start_time = time.time()
            
            logger_instance.info(
                f"开始调用 {api_name} API",
                endpoint=endpoint,
                function=func.__name__
            )
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger_instance.info(
                    f"{api_name} API调用成功",
                    endpoint=endpoint,
                    execution_time=f"{execution_time:.4f}s"
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger_instance.error(
                    f"{api_name} API调用失败: {e}",
                    endpoint=endpoint,
                    execution_time=f"{execution_time:.4f}s",
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


def log_trading_action(action_type: str):
    """记录交易操作的装饰器
    
    Args:
        action_type: 交易操作类型（如：开仓、平仓、查询等）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger_instance = get_logger("trading")
            start_time = time.time()
            
            logger_instance.info(
                f"开始执行交易操作: {action_type}",
                function=func.__name__
            )
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger_instance.info(
                    f"交易操作 {action_type} 执行成功",
                    execution_time=f"{execution_time:.4f}s",
                    result=result
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger_instance.error(
                    f"交易操作 {action_type} 执行失败: {e}",
                    execution_time=f"{execution_time:.4f}s",
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


def log_ai_decision(engine_type: str):
    """记录AI决策的装饰器
    
    Args:
        engine_type: AI引擎类型（如：开仓引擎、持仓引擎）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger_instance = get_logger("ai")
            start_time = time.time()
            
            logger_instance.info(
                f"开始 {engine_type} AI分析",
                function=func.__name__
            )
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger_instance.info(
                    f"{engine_type} AI分析完成",
                    execution_time=f"{execution_time:.4f}s",
                    decision=result
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger_instance.error(
                    f"{engine_type} AI分析失败: {e}",
                    execution_time=f"{execution_time:.4f}s",
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


class StructuredLogger:
    """结构化日志记录器类"""
    
    def __init__(self, name: str):
        """初始化结构化日志记录器
        
        Args:
            name: 日志记录器名称
        """
        self.logger = get_logger(name)
        self.name = name
    
    def info(self, message: str, **kwargs):
        """记录信息级别日志"""
        self.logger.info(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试级别日志"""
        self.logger.debug(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告级别日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误级别日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误级别日志"""
        self.logger.critical(message, **kwargs)
    
    def log_dict(self, level: str, data: Dict[str, Any], message: str = ""):
        """记录字典数据
        
        Args:
            level: 日志级别
            data: 要记录的字典数据
            message: 日志消息
        """
        log_func = getattr(self.logger, level.lower())
        log_func(message or f"数据记录 - {self.name}", **data)
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能数据
        
        Args:
            operation: 操作名称
            duration: 执行时间（秒）
            **kwargs: 其他性能相关数据
        """
        self.logger.info(
            f"性能监控 - {operation}",
            duration=f"{duration:.4f}s",
            operation=operation,
            **kwargs
        )
    
    def log_market_data(self, symbol: str, timeframe: str, data_count: int, **kwargs):
        """记录市场数据获取日志
        
        Args:
            symbol: 交易对
            timeframe: 时间周期
            data_count: 数据数量
            **kwargs: 其他市场数据相关信息
        """
        self.logger.info(
            f"市场数据获取 - {symbol}",
            symbol=symbol,
            timeframe=timeframe,
            data_count=data_count,
            **kwargs
        )
    
    def log_technical_analysis(self, symbol: str, timeframe: str, indicators: Dict[str, Any]):
        """记录技术分析结果
        
        Args:
            symbol: 交易对
            timeframe: 时间周期
            indicators: 技术指标结果
        """
        self.logger.info(
            f"技术分析完成 - {symbol} {timeframe}",
            symbol=symbol,
            timeframe=timeframe,
            indicators=indicators
        )
    
    def log_trade_execution(self, action: str, symbol: str, amount: float, price: float, **kwargs):
        """记录交易执行日志
        
        Args:
            action: 交易动作（开仓、平仓等）
            symbol: 交易对
            amount: 交易数量
            price: 交易价格
            **kwargs: 其他交易相关信息
        """
        self.logger.info(
            f"交易执行 - {action} {symbol}",
            action=action,
            symbol=symbol,
            amount=amount,
            price=price,
            **kwargs
        )


# 预定义的结构化日志记录器
system_logger = StructuredLogger("system")
trading_logger = StructuredLogger("trading")
ai_logger = StructuredLogger("ai")
market_data_logger = StructuredLogger("market_data")
technical_analysis_logger = StructuredLogger("technical_analysis")
api_logger = StructuredLogger("api")
error_logger = StructuredLogger("error")
