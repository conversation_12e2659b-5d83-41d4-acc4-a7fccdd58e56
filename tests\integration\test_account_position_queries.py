#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户和持仓查询功能真实API测试

测试账户余额、持仓信息、订单状态查询和数据格式标准化。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig, AccountBalance, Position, Order, TradingSide, OrderType, OrderStatus


def test_account_position_queries():
    """测试账户和持仓查询功能"""
    print("=== 账户和持仓查询功能测试 ===")
    
    # 创建配置
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建交易所客户端
    exchange_client = ExchangeClient(config)
    
    try:
        # 连接交易所
        print("1. 连接交易所...")
        exchange_client.connect()
        print("✅ 交易所连接成功")
        
        # 测试账户余额查询
        print("\n2. 测试账户余额查询...")
        balances = exchange_client.fetch_balance()
        
        print(f"获取到 {len(balances)} 个币种的余额信息")
        
        # 验证数据格式标准化
        for currency, balance in balances.items():
            print(f"  {currency}:")
            print(f"    类型: {type(balance).__name__}")
            print(f"    总计: {balance.total}")
            print(f"    可用: {balance.available}")
            print(f"    已用: {balance.used}")
            
            # 验证数据模型
            assert isinstance(balance, AccountBalance)
            assert balance.currency == currency
            assert isinstance(balance.total, float)
            assert isinstance(balance.available, float)
            assert isinstance(balance.used, float)
            assert balance.total >= 0
            assert balance.available >= 0
            assert balance.used >= 0
            assert abs(balance.total - (balance.available + balance.used)) < 0.01  # 允许小数精度误差
            
            # 测试to_dict方法
            balance_dict = balance.to_dict()
            assert isinstance(balance_dict, dict)
            assert balance_dict['currency'] == currency
            assert balance_dict['total'] == balance.total
            assert balance_dict['available'] == balance.available
            assert balance_dict['used'] == balance.used
        
        print("✅ 账户余额查询和数据格式验证成功")
        
        # 测试持仓信息查询
        print("\n3. 测试持仓信息查询...")
        positions = exchange_client.fetch_positions()
        
        print(f"获取到 {len(positions)} 个持仓")
        
        # 验证持仓数据格式标准化
        for i, position in enumerate(positions):
            print(f"  持仓 {i+1}:")
            print(f"    交易对: {position.symbol}")
            print(f"    方向: {position.side}")
            print(f"    数量: {position.amount}")
            print(f"    开仓价: {position.entry_price}")
            print(f"    当前价: {position.current_price}")
            print(f"    杠杆: {position.leverage}")
            print(f"    未实现盈亏: {position.unrealized_pnl}")
            print(f"    盈亏百分比: {position.unrealized_pnl_percentage}%")
            print(f"    已用保证金: {position.margin_used}")
            
            # 验证数据模型
            assert isinstance(position, Position)
            assert isinstance(position.symbol, str)
            assert isinstance(position.side, TradingSide)
            assert isinstance(position.amount, float)
            assert position.amount > 0  # 只返回有持仓的
            assert isinstance(position.entry_price, float)
            assert position.entry_price > 0
            assert isinstance(position.current_price, float)
            assert position.current_price > 0
            assert isinstance(position.leverage, int)
            assert position.leverage >= 1
            assert isinstance(position.unrealized_pnl, float)
            assert isinstance(position.unrealized_pnl_percentage, float)
            assert isinstance(position.margin_used, float)
            assert position.margin_used >= 0
            
            # 测试更新当前价格功能
            old_pnl = position.unrealized_pnl
            position.update_current_price(position.current_price * 1.01)  # 价格上涨1%
            print(f"    价格更新后盈亏: {position.unrealized_pnl} (原: {old_pnl})")
            
            # 测试to_dict方法
            position_dict = position.to_dict()
            assert isinstance(position_dict, dict)
            assert position_dict['symbol'] == position.symbol
            assert position_dict['side'] == position.side.value
            assert position_dict['amount'] == position.amount
        
        print("✅ 持仓信息查询和数据格式验证成功")
        
        # 测试订单状态查询
        print("\n4. 测试订单状态查询...")
        
        # 测试获取所有订单
        all_orders = exchange_client.fetch_orders()
        print(f"获取到 {len(all_orders)} 个未完成订单")
        
        # 如果有持仓，测试获取特定交易对的订单
        if positions:
            test_symbol = positions[0].symbol
            symbol_orders = exchange_client.fetch_orders(test_symbol)
            print(f"获取到 {test_symbol} 的 {len(symbol_orders)} 个未完成订单")
        
        # 验证订单数据格式标准化
        for i, order in enumerate(all_orders):
            print(f"  订单 {i+1}:")
            print(f"    ID: {order.id}")
            print(f"    交易对: {order.symbol}")
            print(f"    方向: {order.side}")
            print(f"    数量: {order.amount}")
            print(f"    价格: {order.price}")
            print(f"    类型: {order.order_type}")
            print(f"    状态: {order.status}")
            print(f"    已成交: {order.filled_amount}")
            print(f"    平均价: {order.average_price}")
            
            # 验证数据模型
            assert isinstance(order, Order)
            assert isinstance(order.id, str)
            assert len(order.id) > 0
            assert isinstance(order.symbol, str)
            assert isinstance(order.side, TradingSide)
            assert isinstance(order.amount, float)
            assert order.amount > 0
            assert order.price is None or isinstance(order.price, float)
            assert isinstance(order.order_type, OrderType)
            assert isinstance(order.status, OrderStatus)
            assert isinstance(order.filled_amount, float)
            assert order.filled_amount >= 0
            assert order.average_price is None or isinstance(order.average_price, float)
            
            # 测试to_dict方法
            order_dict = order.to_dict()
            assert isinstance(order_dict, dict)
            assert order_dict['id'] == order.id
            assert order_dict['symbol'] == order.symbol
            assert order_dict['side'] == order.side.value
        
        print("✅ 订单状态查询和数据格式验证成功")
        
        # 测试数据一致性
        print("\n5. 测试数据一致性...")
        
        # 验证持仓和余额的一致性
        if positions and balances:
            print("验证持仓和余额数据一致性:")
            
            # 计算持仓占用的保证金总额
            total_margin_used = sum(pos.margin_used for pos in positions)
            print(f"  持仓占用保证金总额: {total_margin_used}")
            
            # 检查USDT余额（通常是保证金币种）
            if 'USDT' in balances:
                usdt_balance = balances['USDT']
                print(f"  USDT总余额: {usdt_balance.total}")
                print(f"  USDT可用余额: {usdt_balance.available}")
                print(f"  USDT已用余额: {usdt_balance.used}")
                
                # 已用余额应该包含持仓保证金
                print(f"  数据一致性检查: 已用余额 >= 持仓保证金 ({usdt_balance.used} >= {total_margin_used})")
        
        print("✅ 数据一致性验证完成")
        
        # 测试错误处理
        print("\n6. 测试错误处理...")
        
        try:
            # 测试无效交易对的订单查询
            invalid_orders = exchange_client.fetch_orders("INVALID/SYMBOL")
            print(f"无效交易对查询结果: {len(invalid_orders)} 个订单")
        except Exception as e:
            print(f"无效交易对查询异常处理: {type(e).__name__}: {e}")
        
        print("✅ 错误处理测试完成")
        
        print("\n🎉 所有账户和持仓查询功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = test_account_position_queries()
    if success:
        print("\n✅ 账户和持仓查询功能测试全部通过！")
    else:
        print("\n❌ 账户和持仓查询功能测试失败！")
        sys.exit(1)
