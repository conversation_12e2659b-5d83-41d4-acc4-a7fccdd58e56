#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统交易流程协调器

此模块负责协调整个交易流程，包括：
1. 市场数据获取调度
2. 技术分析计算调度
3. AI引擎决策调度
4. 风险管理验证
5. 交易执行协调
6. 系统状态管理
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from src.core.exchange_client import ExchangeClient
from src.core.market_data_engine import MarketDataEngine
from src.core.technical_analysis import TechnicalAnalysisEngine
from src.core.ai_opening_engine import AIOpeningEngine
from src.core.ai_position_engine import AIPositionEngine
from src.core.risk_manager import RiskManager
from src.data.models import Position, TradingSide, RiskParameters
from src.ai.deepseek_client import DeepSeekConfig
from src.utils.logger import get_logger, log_execution_time
from src.utils.exceptions import TradingSystemError, RiskViolationError

logger = get_logger(__name__)


class SystemState(Enum):
    """系统状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class TradingConfig:
    """交易配置"""
    symbols: List[str]                    # 交易对列表
    timeframes: List[str]                # 时间周期列表
    analysis_interval: int               # 分析间隔（秒）
    max_positions: int                   # 最大持仓数量
    enable_opening: bool = True          # 是否启用开仓
    enable_position_management: bool = True  # 是否启用持仓管理


@dataclass
class SystemStatus:
    """系统状态"""
    state: SystemState                   # 当前状态
    uptime: float                       # 运行时间
    last_analysis_time: float          # 上次分析时间
    total_analyses: int                 # 总分析次数
    successful_analyses: int            # 成功分析次数
    failed_analyses: int               # 失败分析次数
    active_positions: int              # 活跃持仓数量
    last_error: Optional[str] = None   # 最后错误信息


class TradingCoordinator:
    """交易流程协调器类
    
    负责协调整个交易系统的运行流程。
    """
    
    def __init__(self, exchange_client: ExchangeClient, deepseek_config: DeepSeekConfig,
                 risk_params: RiskParameters, trading_config: TradingConfig):
        """初始化交易协调器
        
        Args:
            exchange_client: 交易所客户端
            deepseek_config: DeepSeek配置
            risk_params: 风险参数
            trading_config: 交易配置
        """
        self.exchange_client = exchange_client
        self.trading_config = trading_config
        
        # 初始化各个引擎
        self.market_data_engine = MarketDataEngine(exchange_client, trading_config.symbols)
        self.technical_analysis_engine = TechnicalAnalysisEngine()
        self.ai_opening_engine = AIOpeningEngine(deepseek_config)
        self.ai_position_engine = AIPositionEngine(deepseek_config)
        self.risk_manager = RiskManager(risk_params)
        
        # 系统状态
        self.system_state = SystemState.STOPPED
        self.start_time = 0
        self.last_analysis_time = 0
        self.total_analyses = 0
        self.successful_analyses = 0
        self.failed_analyses = 0
        
        # 运行控制
        self.running = False
        self.analysis_task = None
        
        # 缓存
        self.current_positions = []
        self.current_balance = {}
        
    async def start(self):
        """启动交易系统"""
        try:
            logger.info("启动交易协调器...")
            self.system_state = SystemState.STARTING
            
            # 1. 连接交易所
            if not self.exchange_client.is_connected:
                self.exchange_client.connect()
                logger.info("交易所连接成功")
            
            # 2. 测试AI引擎连接
            if not self.ai_opening_engine.test_connection():
                raise TradingSystemError("AI开仓引擎连接失败")
            
            if not self.ai_position_engine.test_connection():
                raise TradingSystemError("AI持仓引擎连接失败")
            
            logger.info("AI引擎连接测试成功")
            
            # 3. 启动市场数据引擎
            await self.market_data_engine.start()
            logger.info("市场数据引擎启动成功")
            
            # 4. 初始化数据
            await self._initialize_data()
            
            # 5. 启动分析循环
            self.running = True
            self.start_time = time.time()
            self.system_state = SystemState.RUNNING
            
            self.analysis_task = asyncio.create_task(self._analysis_loop())
            
            logger.info("交易协调器启动完成")
            
        except Exception as e:
            logger.error(f"启动交易协调器失败: {e}")
            self.system_state = SystemState.ERROR
            raise TradingSystemError(f"启动失败: {e}")
    
    async def stop(self):
        """停止交易系统"""
        try:
            logger.info("停止交易协调器...")
            self.system_state = SystemState.STOPPING
            
            # 1. 停止分析循环
            self.running = False
            if self.analysis_task:
                self.analysis_task.cancel()
                try:
                    await self.analysis_task
                except asyncio.CancelledError:
                    pass
            
            # 2. 停止市场数据引擎
            await self.market_data_engine.stop()
            
            # 3. 断开交易所连接
            self.exchange_client.disconnect()
            
            self.system_state = SystemState.STOPPED
            logger.info("交易协调器停止完成")
            
        except Exception as e:
            logger.error(f"停止交易协调器失败: {e}")
            self.system_state = SystemState.ERROR
    
    async def pause(self):
        """暂停交易系统"""
        if self.system_state == SystemState.RUNNING:
            self.system_state = SystemState.PAUSED
            logger.info("交易协调器已暂停")
    
    async def resume(self):
        """恢复交易系统"""
        if self.system_state == SystemState.PAUSED:
            self.system_state = SystemState.RUNNING
            logger.info("交易协调器已恢复")
    
    async def _initialize_data(self):
        """初始化数据"""
        try:
            # 获取当前持仓
            self.current_positions = self.exchange_client.fetch_positions()
            logger.info(f"获取到 {len(self.current_positions)} 个持仓")
            
            # 获取账户余额
            self.current_balance = self.exchange_client.fetch_balance()
            logger.info(f"获取到 {len(self.current_balance)} 个币种余额")
            
            # 等待市场数据准备就绪
            await asyncio.sleep(5)  # 给市场数据引擎一些时间获取数据
            
        except Exception as e:
            logger.error(f"初始化数据失败: {e}")
            raise
    
    async def _analysis_loop(self):
        """分析循环"""
        logger.info("开始分析循环")
        
        while self.running:
            try:
                if self.system_state != SystemState.RUNNING:
                    await asyncio.sleep(1)
                    continue
                
                # 执行一轮分析
                await self._perform_analysis()
                
                # 等待下一轮分析
                await asyncio.sleep(self.trading_config.analysis_interval)
                
            except asyncio.CancelledError:
                logger.info("分析循环被取消")
                break
            except Exception as e:
                logger.error(f"分析循环异常: {e}")
                self.failed_analyses += 1
                await asyncio.sleep(10)  # 异常后等待10秒再继续
    
    @log_execution_time("完整分析周期")
    async def _perform_analysis(self):
        """执行一轮完整分析"""
        try:
            self.total_analyses += 1
            self.last_analysis_time = time.time()
            
            logger.debug("开始执行分析周期")
            
            # 1. 更新账户信息
            await self._update_account_info()
            
            # 2. 检查投资组合风险
            portfolio_risk = self.risk_manager.check_portfolio_risk(
                self.current_positions, self.current_balance
            )
            
            if not portfolio_risk.passed:
                logger.warning(f"投资组合风险检查失败: {portfolio_risk.errors}")
                # 风险管理器会自动处理紧急模式的启用和禁用
            
            # 3. 处理现有持仓
            if self.trading_config.enable_position_management:
                await self._manage_existing_positions()
            
            # 4. 寻找新的开仓机会
            if self.trading_config.enable_opening and len(self.current_positions) < self.trading_config.max_positions:
                await self._find_opening_opportunities()
            
            self.successful_analyses += 1
            logger.debug("分析周期完成")
            
        except Exception as e:
            logger.error(f"执行分析失败: {e}")
            self.failed_analyses += 1
            raise
    
    async def _update_account_info(self):
        """更新账户信息"""
        try:
            # 更新持仓信息
            self.current_positions = self.exchange_client.fetch_positions()
            
            # 更新余额信息
            self.current_balance = self.exchange_client.fetch_balance()
            
        except Exception as e:
            logger.error(f"更新账户信息失败: {e}")
            raise
    
    async def _manage_existing_positions(self):
        """管理现有持仓"""
        if not self.current_positions:
            return
        
        logger.debug(f"管理 {len(self.current_positions)} 个持仓")
        
        for position in self.current_positions:
            try:
                await self._manage_single_position(position)
            except Exception as e:
                logger.error(f"管理持仓 {position.symbol} 失败: {e}")
                continue
    
    async def _manage_single_position(self, position: Position):
        """管理单个持仓"""
        try:
            # 1. 获取市场数据
            market_data = await self.market_data_engine.get_symbol_data(position.symbol)
            if not market_data:
                logger.warning(f"无法获取 {position.symbol} 市场数据")
                return
            
            # 2. 计算技术指标
            technical_indicators = self.technical_analysis_engine.analyze_multi_timeframe(market_data)
            
            # 3. 计算利润回撤
            current_price = market_data[self.trading_config.timeframes[0]][-1].close
            price_history = [candle.close for candle in market_data[self.trading_config.timeframes[0]][-50:]]
            
            profit_drawdown = self.ai_position_engine.calculate_profit_drawdown(
                position, current_price, price_history
            )
            
            # 4. AI持仓分析
            decision = await self.ai_position_engine.analyze_position_management(
                position=position,
                technical_data=technical_indicators,
                profit_drawdown=profit_drawdown,
                current_price=current_price
            )
            
            # 5. 执行持仓管理决策
            if self.ai_position_engine.should_manage_position(decision):
                await self._execute_position_management(position, decision)
            
        except Exception as e:
            logger.error(f"管理持仓 {position.symbol} 失败: {e}")
            raise
    
    async def _find_opening_opportunities(self):
        """寻找开仓机会"""
        logger.debug("寻找开仓机会")
        
        # 获取没有持仓的交易对
        position_symbols = {pos.symbol for pos in self.current_positions}
        available_symbols = [symbol for symbol in self.trading_config.symbols 
                           if symbol not in position_symbols]
        
        if not available_symbols:
            logger.debug("所有交易对都有持仓，跳过开仓分析")
            return
        
        # 分析每个可用的交易对
        for symbol in available_symbols:
            try:
                await self._analyze_opening_opportunity(symbol)
            except Exception as e:
                logger.error(f"分析 {symbol} 开仓机会失败: {e}")
                continue
    
    async def _analyze_opening_opportunity(self, symbol: str):
        """分析单个交易对的开仓机会"""
        try:
            # 1. 获取市场数据
            market_data = await self.market_data_engine.get_symbol_data(symbol)
            if not market_data:
                logger.debug(f"无法获取 {symbol} 市场数据")
                return
            
            # 2. 计算技术指标
            technical_indicators = self.technical_analysis_engine.analyze_multi_timeframe(market_data)
            
            # 3. AI开仓分析
            current_price = market_data[self.trading_config.timeframes[0]][-1].close
            decision = await self.ai_opening_engine.analyze_market_for_opening(
                symbol=symbol,
                technical_data=technical_indicators,
                current_price=current_price
            )
            
            # 4. 执行开仓决策
            if self.ai_opening_engine.should_open_position(decision):
                await self._execute_opening_decision(symbol, decision, current_price)
            
        except Exception as e:
            logger.error(f"分析 {symbol} 开仓机会失败: {e}")
            raise
    
    async def _execute_opening_decision(self, symbol: str, decision, current_price: float):
        """执行开仓决策"""
        try:
            # 1. 获取交易参数
            trading_params = self.ai_opening_engine.get_trading_parameters(
                decision, current_price, self.current_balance.get("USDT", {}).get("available", 0)
            )
            
            # 2. 风险验证
            risk_result = self.risk_manager.validate_opening_order(
                symbol=symbol,
                side=trading_params["side"],
                amount=trading_params["amount"],
                leverage=trading_params["leverage"],
                price=current_price,
                balance=self.current_balance
            )
            
            if not risk_result.passed:
                logger.warning(f"开仓风险检查失败 {symbol}: {risk_result.errors}")
                return
            
            # 3. 执行开仓
            order_result = self.exchange_client.create_market_order(
                symbol=symbol,
                side=trading_params["side"],
                amount=trading_params["amount"],
                leverage=trading_params["leverage"]
            )
            
            logger.info(f"开仓成功: {symbol} {trading_params['side']} {trading_params['amount']} @ {current_price}")
            
        except Exception as e:
            logger.error(f"执行开仓决策失败 {symbol}: {e}")
            raise
    
    async def _execute_position_management(self, position: Position, decision):
        """执行持仓管理决策"""
        try:
            management_params = self.ai_position_engine.get_management_parameters(decision, position)
            
            if decision.action == "close_partial":
                # 部分平仓
                close_amount = management_params["close_amount"]
                side = "sell" if position.side == TradingSide.LONG else "buy"
                
                order_result = self.exchange_client.create_market_order(
                    symbol=position.symbol,
                    side=side,
                    amount=close_amount
                )
                
                logger.info(f"部分平仓成功: {position.symbol} {close_amount}")
                
            elif decision.action == "close_all":
                # 全部平仓
                order_result = self.exchange_client.close_position(
                    symbol=position.symbol,
                    side=position.side.value
                )
                
                logger.info(f"全部平仓成功: {position.symbol}")
            
            # TODO: 实现止损止盈调整
            
        except Exception as e:
            logger.error(f"执行持仓管理失败 {position.symbol}: {e}")
            raise
    
    def get_system_status(self) -> SystemStatus:
        """获取系统状态"""
        uptime = time.time() - self.start_time if self.start_time > 0 else 0
        
        return SystemStatus(
            state=self.system_state,
            uptime=uptime,
            last_analysis_time=self.last_analysis_time,
            total_analyses=self.total_analyses,
            successful_analyses=self.successful_analyses,
            failed_analyses=self.failed_analyses,
            active_positions=len(self.current_positions)
        )
    
    def update_trading_config(self, new_config: TradingConfig):
        """更新交易配置"""
        self.trading_config = new_config
        logger.info("交易配置已更新")
    
    def update_risk_parameters(self, new_params: RiskParameters):
        """更新风险参数"""
        self.risk_manager.update_risk_parameters(new_params)
        logger.info("风险参数已更新")
