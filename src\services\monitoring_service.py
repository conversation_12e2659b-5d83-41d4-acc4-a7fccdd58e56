#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控服务

此模块负责监控系统运行状态，包括：
1. 系统性能监控
2. 交易状态监控
3. AI引擎状态监控
4. 风险指标监控
5. 异常检测和告警
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json

from src.utils.logger import get_logger
from src.utils.exceptions import SystemMonitoringError

logger = get_logger(__name__)


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    uptime: float


@dataclass
class TradingMetrics:
    """交易指标"""
    timestamp: float
    total_positions: int
    total_exposure: float
    unrealized_pnl: float
    daily_trades: int
    success_rate: float
    risk_level: str


@dataclass
class AIMetrics:
    """AI引擎指标"""
    timestamp: float
    opening_engine_status: str
    position_engine_status: str
    last_decision_time: Optional[float]
    decision_count_24h: int
    average_confidence: float
    error_count: int


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    condition: Callable[[Dict[str, Any]], bool]
    message: str
    severity: str  # "info", "warning", "error", "critical"
    cooldown: int = 300  # 冷却时间（秒）
    last_triggered: float = 0


class MonitoringService:
    """系统监控服务类"""
    
    def __init__(self):
        """初始化监控服务"""
        self.is_running = False
        self.monitoring_thread = None
        self.start_time = time.time()
        
        # 监控数据存储
        self.system_metrics_history: List[SystemMetrics] = []
        self.trading_metrics_history: List[TradingMetrics] = []
        self.ai_metrics_history: List[AIMetrics] = []
        
        # 告警规则
        self.alert_rules: List[AlertRule] = []
        self.alerts_history: List[Dict[str, Any]] = []
        
        # 监控配置
        self.monitoring_interval = 30  # 监控间隔（秒）
        self.max_history_size = 1000  # 最大历史记录数
        
        # 初始化默认告警规则
        self._init_default_alert_rules()
        
        logger.info("系统监控服务初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_running:
            logger.warning("监控服务已在运行")
            return
        
        self.is_running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("系统监控服务已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_running:
            logger.warning("监控服务未在运行")
            return
        
        self.is_running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("系统监控服务已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                self._add_system_metrics(system_metrics)
                
                # 收集交易指标（如果交易系统可用）
                trading_metrics = self._collect_trading_metrics()
                if trading_metrics:
                    self._add_trading_metrics(trading_metrics)
                
                # 收集AI指标（如果AI引擎可用）
                ai_metrics = self._collect_ai_metrics()
                if ai_metrics:
                    self._add_ai_metrics(ai_metrics)
                
                # 检查告警规则
                self._check_alert_rules()
                
                # 清理历史数据
                self._cleanup_history()
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
            
            time.sleep(self.monitoring_interval)
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            
            # 网络IO
            network = psutil.net_io_counters()
            network_io = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv
            }
            
            # 进程数
            process_count = len(psutil.pids())
            
            # 运行时间
            uptime = time.time() - self.start_time
            
            return SystemMetrics(
                timestamp=time.time(),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
                uptime=uptime
            )
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            raise SystemMonitoringError(f"系统指标收集失败: {e}")
    
    def _collect_trading_metrics(self) -> Optional[TradingMetrics]:
        """收集交易指标"""
        try:
            # 这里应该从交易系统获取实际数据
            # 目前返回模拟数据
            return TradingMetrics(
                timestamp=time.time(),
                total_positions=0,
                total_exposure=0.0,
                unrealized_pnl=0.0,
                daily_trades=0,
                success_rate=0.0,
                risk_level="low"
            )
            
        except Exception as e:
            logger.error(f"收集交易指标失败: {e}")
            return None
    
    def _collect_ai_metrics(self) -> Optional[AIMetrics]:
        """收集AI指标"""
        try:
            # 这里应该从AI引擎获取实际数据
            # 目前返回模拟数据
            return AIMetrics(
                timestamp=time.time(),
                opening_engine_status="idle",
                position_engine_status="idle",
                last_decision_time=None,
                decision_count_24h=0,
                average_confidence=0.0,
                error_count=0
            )
            
        except Exception as e:
            logger.error(f"收集AI指标失败: {e}")
            return None
    
    def _add_system_metrics(self, metrics: SystemMetrics):
        """添加系统指标"""
        self.system_metrics_history.append(metrics)
    
    def _add_trading_metrics(self, metrics: TradingMetrics):
        """添加交易指标"""
        self.trading_metrics_history.append(metrics)
    
    def _add_ai_metrics(self, metrics: AIMetrics):
        """添加AI指标"""
        self.ai_metrics_history.append(metrics)
    
    def _init_default_alert_rules(self):
        """初始化默认告警规则"""
        # CPU使用率告警
        self.alert_rules.append(AlertRule(
            name="high_cpu_usage",
            condition=lambda data: data.get("cpu_usage", 0) > 80,
            message="CPU使用率过高: {cpu_usage:.1f}%",
            severity="warning"
        ))
        
        # 内存使用率告警
        self.alert_rules.append(AlertRule(
            name="high_memory_usage",
            condition=lambda data: data.get("memory_usage", 0) > 85,
            message="内存使用率过高: {memory_usage:.1f}%",
            severity="warning"
        ))
        
        # 磁盘使用率告警
        self.alert_rules.append(AlertRule(
            name="high_disk_usage",
            condition=lambda data: data.get("disk_usage", 0) > 90,
            message="磁盘使用率过高: {disk_usage:.1f}%",
            severity="error"
        ))
        
        # 风险等级告警
        self.alert_rules.append(AlertRule(
            name="high_risk_level",
            condition=lambda data: data.get("risk_level") in ["high", "critical"],
            message="风险等级过高: {risk_level}",
            severity="error"
        ))
    
    def _check_alert_rules(self):
        """检查告警规则"""
        current_time = time.time()
        
        # 获取最新指标数据
        latest_data = {}
        
        if self.system_metrics_history:
            latest_system = self.system_metrics_history[-1]
            latest_data.update({
                "cpu_usage": latest_system.cpu_usage,
                "memory_usage": latest_system.memory_usage,
                "disk_usage": latest_system.disk_usage
            })
        
        if self.trading_metrics_history:
            latest_trading = self.trading_metrics_history[-1]
            latest_data.update({
                "risk_level": latest_trading.risk_level,
                "total_exposure": latest_trading.total_exposure,
                "unrealized_pnl": latest_trading.unrealized_pnl
            })
        
        # 检查每个告警规则
        for rule in self.alert_rules:
            try:
                # 检查冷却时间
                if current_time - rule.last_triggered < rule.cooldown:
                    continue
                
                # 检查条件
                if rule.condition(latest_data):
                    # 触发告警
                    alert = {
                        "rule_name": rule.name,
                        "message": rule.message.format(**latest_data),
                        "severity": rule.severity,
                        "timestamp": current_time,
                        "data": latest_data.copy()
                    }
                    
                    self.alerts_history.append(alert)
                    rule.last_triggered = current_time
                    
                    logger.warning(f"告警触发: {alert['message']}")
                    
            except Exception as e:
                logger.error(f"检查告警规则 {rule.name} 失败: {e}")
    
    def _cleanup_history(self):
        """清理历史数据"""
        # 保持历史记录在限制范围内
        if len(self.system_metrics_history) > self.max_history_size:
            self.system_metrics_history = self.system_metrics_history[-self.max_history_size:]
        
        if len(self.trading_metrics_history) > self.max_history_size:
            self.trading_metrics_history = self.trading_metrics_history[-self.max_history_size:]
        
        if len(self.ai_metrics_history) > self.max_history_size:
            self.ai_metrics_history = self.ai_metrics_history[-self.max_history_size:]
        
        if len(self.alerts_history) > self.max_history_size:
            self.alerts_history = self.alerts_history[-self.max_history_size:]
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        latest_system = self.system_metrics_history[-1] if self.system_metrics_history else None
        latest_trading = self.trading_metrics_history[-1] if self.trading_metrics_history else None
        latest_ai = self.ai_metrics_history[-1] if self.ai_metrics_history else None
        
        return {
            "monitoring_status": "running" if self.is_running else "stopped",
            "uptime": time.time() - self.start_time,
            "system_metrics": {
                "cpu_usage": latest_system.cpu_usage if latest_system else 0,
                "memory_usage": latest_system.memory_usage if latest_system else 0,
                "disk_usage": latest_system.disk_usage if latest_system else 0
            } if latest_system else {},
            "trading_metrics": {
                "total_positions": latest_trading.total_positions if latest_trading else 0,
                "risk_level": latest_trading.risk_level if latest_trading else "unknown"
            } if latest_trading else {},
            "ai_metrics": {
                "opening_engine_status": latest_ai.opening_engine_status if latest_ai else "unknown",
                "position_engine_status": latest_ai.position_engine_status if latest_ai else "unknown"
            } if latest_ai else {},
            "recent_alerts": self.alerts_history[-10:] if self.alerts_history else []
        }
    
    def get_metrics_history(self, metric_type: str, hours: int = 24) -> List[Dict[str, Any]]:
        """获取指标历史"""
        cutoff_time = time.time() - (hours * 3600)
        
        if metric_type == "system":
            return [
                {
                    "timestamp": m.timestamp,
                    "cpu_usage": m.cpu_usage,
                    "memory_usage": m.memory_usage,
                    "disk_usage": m.disk_usage
                }
                for m in self.system_metrics_history
                if m.timestamp > cutoff_time
            ]
        elif metric_type == "trading":
            return [
                {
                    "timestamp": m.timestamp,
                    "total_positions": m.total_positions,
                    "total_exposure": m.total_exposure,
                    "unrealized_pnl": m.unrealized_pnl
                }
                for m in self.trading_metrics_history
                if m.timestamp > cutoff_time
            ]
        elif metric_type == "ai":
            return [
                {
                    "timestamp": m.timestamp,
                    "decision_count_24h": m.decision_count_24h,
                    "average_confidence": m.average_confidence,
                    "error_count": m.error_count
                }
                for m in self.ai_metrics_history
                if m.timestamp > cutoff_time
            ]
        else:
            return []
