#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理异常持仓工具

用于检测和清理测试环境中的异常持仓数据。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


def cleanup_abnormal_positions():
    """清理异常持仓"""
    print("=== 异常持仓清理工具 ===")
    print("⚠️  注意：这将检测并可选择性清理异常持仓")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    
    try:
        # 连接交易所
        exchange_client.connect()
        
        # 获取账户信息
        balance = exchange_client.fetch_balance()
        positions = exchange_client.fetch_positions()
        
        total_balance = sum(bal.total for bal in balance.values())
        
        print(f"\n📊 账户概览:")
        print(f"总余额: {total_balance:,.2f} USDT")
        print(f"持仓数量: {len(positions)} 个")
        
        # 分析持仓
        normal_positions = []
        abnormal_positions = []
        
        for pos in positions:
            position_exposure = abs(pos.amount) * pos.current_price
            exposure_ratio = position_exposure / total_balance if total_balance > 0 else 0
            
            print(f"\n📈 {pos.symbol}:")
            print(f"  方向: {pos.side.value}")
            print(f"  数量: {pos.amount}")
            print(f"  价格: {pos.current_price:,.4f}")
            print(f"  敞口: {position_exposure:,.2f} USDT")
            print(f"  敞口比例: {exposure_ratio:.1f}x")
            print(f"  保证金: {pos.margin_used:,.2f} USDT")
            
            if exposure_ratio > 100:  # 敞口超过总余额100倍
                print(f"  🚨 状态: 异常持仓 (敞口{exposure_ratio:.1f}x > 100x)")
                abnormal_positions.append(pos)
            elif exposure_ratio > 10:  # 敞口超过总余额10倍
                print(f"  ⚠️  状态: 高风险持仓 (敞口{exposure_ratio:.1f}x > 10x)")
                normal_positions.append(pos)
            else:
                print(f"  ✅ 状态: 正常持仓")
                normal_positions.append(pos)
        
        # 汇总结果
        print(f"\n📋 持仓分析结果:")
        print(f"正常持仓: {len(normal_positions)} 个")
        print(f"异常持仓: {len(abnormal_positions)} 个")
        
        if abnormal_positions:
            print(f"\n🚨 发现 {len(abnormal_positions)} 个异常持仓:")
            
            total_abnormal_exposure = 0
            for pos in abnormal_positions:
                exposure = abs(pos.amount) * pos.current_price
                total_abnormal_exposure += exposure
                print(f"  - {pos.symbol}: {pos.side.value} {pos.amount} (敞口: {exposure:,.2f} USDT)")
            
            print(f"\n异常持仓总敞口: {total_abnormal_exposure:,.2f} USDT")
            print(f"异常敞口占比: {total_abnormal_exposure/total_balance:.1f}x")
            
            # 询问是否清理
            print(f"\n❓ 是否要清理这些异常持仓？")
            print(f"   这些持仓可能是测试环境的异常数据")
            print(f"   清理后系统风险控制将更加准确")
            
            choice = input("\n请选择 (y/N): ").strip().lower()
            
            if choice == 'y':
                print(f"\n🧹 开始清理异常持仓...")
                
                success_count = 0
                for pos in abnormal_positions:
                    try:
                        print(f"  清理 {pos.symbol} {pos.side.value} {pos.amount}...")
                        
                        # 平仓
                        result = exchange_client.close_position(
                            symbol=pos.symbol,
                            side=pos.side.value
                        )
                        
                        print(f"  ✅ {pos.symbol} 清理成功")
                        success_count += 1
                        
                    except Exception as e:
                        print(f"  ❌ {pos.symbol} 清理失败: {e}")
                
                print(f"\n🎉 清理完成: {success_count}/{len(abnormal_positions)} 个异常持仓已清理")
                
                if success_count > 0:
                    print(f"\n建议重新运行风险检查以验证修复效果")
            
            else:
                print(f"\n⏭️  跳过清理，异常持仓将继续被风险管理系统自动过滤")
        
        else:
            print(f"\n✅ 未发现异常持仓，所有持仓数据正常")
        
        # 给出建议
        print(f"\n💡 建议:")
        if abnormal_positions:
            print(f"  1. 清理异常持仓以获得准确的风险评估")
            print(f"  2. 检查交易所API是否返回正确的持仓数据")
            print(f"  3. 确认测试环境配置是否正确")
        else:
            print(f"  1. 持仓数据正常，系统风险控制准确")
            print(f"  2. 可以继续进行正常的交易测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理工具运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print(f"\n🧹 连接已断开")


if __name__ == "__main__":
    success = cleanup_abnormal_positions()
    if success:
        print(f"\n✅ 异常持仓检查完成！")
    else:
        print(f"\n❌ 异常持仓检查失败！")
        sys.exit(1)
