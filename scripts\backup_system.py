#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统备份脚本

此脚本负责系统数据的备份，包括：
1. 数据库备份
2. 配置文件备份
3. 日志文件备份
4. 自动清理旧备份
5. 备份验证
"""

import os
import sys
import shutil
import sqlite3
import zipfile
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger

logger = get_logger(__name__)


class SystemBackup:
    """系统备份类"""
    
    def __init__(self, backup_dir: Optional[str] = None):
        """初始化备份系统
        
        Args:
            backup_dir: 备份目录路径
        """
        self.project_root = project_root
        self.backup_dir = Path(backup_dir) if backup_dir else self.project_root / "data" / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份配置
        self.max_backups = 30  # 保留最近30个备份
        self.compress = True   # 是否压缩备份
        
        logger.info(f"备份目录: {self.backup_dir}")
    
    def create_full_backup(self, include_logs: bool = True) -> str:
        """创建完整备份
        
        Args:
            include_logs: 是否包含日志文件
        
        Returns:
            str: 备份文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"system_backup_{timestamp}"
            
            if self.compress:
                backup_file = self.backup_dir / f"{backup_name}.zip"
                self._create_zip_backup(backup_file, include_logs)
            else:
                backup_file = self.backup_dir / backup_name
                backup_file.mkdir(exist_ok=True)
                self._create_folder_backup(backup_file, include_logs)
            
            logger.info(f"完整备份创建成功: {backup_file}")
            
            # 验证备份
            if self._verify_backup(backup_file):
                logger.info("备份验证通过")
            else:
                logger.warning("备份验证失败")
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"创建完整备份失败: {e}")
            raise
    
    def create_database_backup(self) -> str:
        """创建数据库备份
        
        Returns:
            str: 备份文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"database_backup_{timestamp}.db"
            
            # 数据库文件路径
            db_file = self.project_root / "data" / "trading_system.db"
            
            if not db_file.exists():
                logger.warning("数据库文件不存在，跳过数据库备份")
                return ""
            
            # 使用SQLite的备份API
            source_conn = sqlite3.connect(str(db_file))
            backup_conn = sqlite3.connect(str(backup_file))
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            logger.info(f"数据库备份创建成功: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"创建数据库备份失败: {e}")
            raise
    
    def create_config_backup(self) -> str:
        """创建配置备份
        
        Returns:
            str: 备份文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"config_backup_{timestamp}.zip"
            
            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 备份配置目录
                config_dir = self.project_root / "config"
                if config_dir.exists():
                    for file_path in config_dir.rglob("*"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(self.project_root)
                            zipf.write(file_path, arcname)
                
                # 备份环境变量文件
                env_file = self.project_root / ".env"
                if env_file.exists():
                    zipf.write(env_file, ".env")
                
                # 备份数据库文件
                db_file = self.project_root / "data" / "trading_system.db"
                if db_file.exists():
                    zipf.write(db_file, "data/trading_system.db")
            
            logger.info(f"配置备份创建成功: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")
            raise
    
    def restore_backup(self, backup_file: str, restore_type: str = "full") -> bool:
        """恢复备份
        
        Args:
            backup_file: 备份文件路径
            restore_type: 恢复类型 (full/database/config)
        
        Returns:
            bool: 是否恢复成功
        """
        try:
            backup_path = Path(backup_file)
            
            if not backup_path.exists():
                logger.error(f"备份文件不存在: {backup_file}")
                return False
            
            logger.info(f"开始恢复备份: {backup_file}")
            
            if restore_type == "full":
                return self._restore_full_backup(backup_path)
            elif restore_type == "database":
                return self._restore_database_backup(backup_path)
            elif restore_type == "config":
                return self._restore_config_backup(backup_path)
            else:
                logger.error(f"不支持的恢复类型: {restore_type}")
                return False
                
        except Exception as e:
            logger.error(f"恢复备份失败: {e}")
            return False
    
    def list_backups(self) -> List[dict]:
        """列出所有备份
        
        Returns:
            List[dict]: 备份列表
        """
        backups = []
        
        for backup_file in self.backup_dir.iterdir():
            if backup_file.is_file() and "backup" in backup_file.name:
                stat = backup_file.stat()
                backups.append({
                    "name": backup_file.name,
                    "path": str(backup_file),
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_ctime),
                    "type": self._get_backup_type(backup_file.name)
                })
        
        # 按创建时间排序
        backups.sort(key=lambda x: x["created"], reverse=True)
        
        return backups
    
    def _create_zip_backup(self, backup_file: Path, include_logs: bool):
        """创建ZIP格式备份"""
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 备份数据库
            db_file = self.project_root / "data" / "trading_system.db"
            if db_file.exists():
                zipf.write(db_file, "data/trading_system.db")
            
            # 备份配置文件
            config_dir = self.project_root / "config"
            if config_dir.exists():
                for file_path in config_dir.rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(self.project_root)
                        zipf.write(file_path, arcname)
            
            # 备份环境变量文件
            env_file = self.project_root / ".env"
            if env_file.exists():
                zipf.write(env_file, ".env")
            
            # 备份脚本
            scripts_dir = self.project_root / "scripts"
            if scripts_dir.exists():
                for file_path in scripts_dir.rglob("*.py"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(self.project_root)
                        zipf.write(file_path, arcname)
            
            # 备份日志文件（可选）
            if include_logs:
                logs_dir = self.project_root / "logs"
                if logs_dir.exists():
                    for file_path in logs_dir.rglob("*.log"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(self.project_root)
                            zipf.write(file_path, arcname)
    
    def _create_folder_backup(self, backup_dir: Path, include_logs: bool):
        """创建文件夹格式备份"""
        # 备份数据库
        db_file = self.project_root / "data" / "trading_system.db"
        if db_file.exists():
            data_dir = backup_dir / "data"
            data_dir.mkdir(exist_ok=True)
            shutil.copy2(db_file, data_dir / "trading_system.db")
        
        # 备份配置文件
        config_dir = self.project_root / "config"
        if config_dir.exists():
            shutil.copytree(config_dir, backup_dir / "config", dirs_exist_ok=True)
        
        # 备份环境变量文件
        env_file = self.project_root / ".env"
        if env_file.exists():
            shutil.copy2(env_file, backup_dir / ".env")
        
        # 备份脚本
        scripts_dir = self.project_root / "scripts"
        if scripts_dir.exists():
            backup_scripts_dir = backup_dir / "scripts"
            backup_scripts_dir.mkdir(exist_ok=True)
            for file_path in scripts_dir.rglob("*.py"):
                if file_path.is_file():
                    rel_path = file_path.relative_to(scripts_dir)
                    dest_path = backup_scripts_dir / rel_path
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(file_path, dest_path)
        
        # 备份日志文件（可选）
        if include_logs:
            logs_dir = self.project_root / "logs"
            if logs_dir.exists():
                shutil.copytree(logs_dir, backup_dir / "logs", dirs_exist_ok=True)
    
    def _verify_backup(self, backup_file: Path) -> bool:
        """验证备份文件"""
        try:
            if backup_file.suffix == ".zip":
                with zipfile.ZipFile(backup_file, 'r') as zipf:
                    # 检查ZIP文件完整性
                    zipf.testzip()
                    
                    # 检查必要文件是否存在
                    file_list = zipf.namelist()
                    required_files = ["data/trading_system.db"]
                    
                    for required_file in required_files:
                        if required_file not in file_list:
                            logger.warning(f"备份中缺少必要文件: {required_file}")
                            return False
            else:
                # 检查文件夹备份
                if not backup_file.is_dir():
                    return False
                
                required_files = [backup_file / "data" / "trading_system.db"]
                for required_file in required_files:
                    if not required_file.exists():
                        logger.warning(f"备份中缺少必要文件: {required_file}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证备份失败: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            backups = self.list_backups()
            
            if len(backups) <= self.max_backups:
                return
            
            # 删除超出数量的旧备份
            old_backups = backups[self.max_backups:]
            
            for backup in old_backups:
                backup_path = Path(backup["path"])
                if backup_path.exists():
                    if backup_path.is_file():
                        backup_path.unlink()
                    else:
                        shutil.rmtree(backup_path)
                    logger.info(f"删除旧备份: {backup['name']}")
            
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
    
    def _restore_full_backup(self, backup_path: Path) -> bool:
        """恢复完整备份"""
        try:
            if backup_path.suffix == ".zip":
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(self.project_root)
            else:
                # 恢复文件夹备份
                for item in backup_path.iterdir():
                    dest = self.project_root / item.name
                    if item.is_file():
                        shutil.copy2(item, dest)
                    else:
                        if dest.exists():
                            shutil.rmtree(dest)
                        shutil.copytree(item, dest)
            
            logger.info("完整备份恢复成功")
            return True
            
        except Exception as e:
            logger.error(f"恢复完整备份失败: {e}")
            return False
    
    def _restore_database_backup(self, backup_path: Path) -> bool:
        """恢复数据库备份"""
        try:
            db_file = self.project_root / "data" / "trading_system.db"
            
            # 备份当前数据库
            if db_file.exists():
                backup_current = db_file.with_suffix(".db.bak")
                shutil.copy2(db_file, backup_current)
            
            # 恢复数据库
            shutil.copy2(backup_path, db_file)
            
            logger.info("数据库备份恢复成功")
            return True
            
        except Exception as e:
            logger.error(f"恢复数据库备份失败: {e}")
            return False
    
    def _restore_config_backup(self, backup_path: Path) -> bool:
        """恢复配置备份"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # 只恢复配置相关文件
                for file_info in zipf.filelist:
                    if file_info.filename.startswith(("config/", ".env")):
                        zipf.extract(file_info, self.project_root)
            
            logger.info("配置备份恢复成功")
            return True
            
        except Exception as e:
            logger.error(f"恢复配置备份失败: {e}")
            return False
    
    def _get_backup_type(self, filename: str) -> str:
        """获取备份类型"""
        if "system_backup" in filename:
            return "full"
        elif "database_backup" in filename:
            return "database"
        elif "config_backup" in filename:
            return "config"
        else:
            return "unknown"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DeepSeek量化交易系统备份脚本")
    parser.add_argument("action", choices=["create", "restore", "list"], help="操作类型")
    parser.add_argument("--type", choices=["full", "database", "config"], default="full", help="备份类型")
    parser.add_argument("--file", help="备份文件路径（恢复时使用）")
    parser.add_argument("--backup-dir", help="备份目录")
    parser.add_argument("--include-logs", action="store_true", help="包含日志文件")
    
    args = parser.parse_args()
    
    # 创建备份系统
    backup_system = SystemBackup(args.backup_dir)
    
    try:
        if args.action == "create":
            if args.type == "full":
                backup_file = backup_system.create_full_backup(args.include_logs)
                print(f"完整备份创建成功: {backup_file}")
            elif args.type == "database":
                backup_file = backup_system.create_database_backup()
                print(f"数据库备份创建成功: {backup_file}")
            elif args.type == "config":
                backup_file = backup_system.create_config_backup()
                print(f"配置备份创建成功: {backup_file}")
        
        elif args.action == "restore":
            if not args.file:
                print("恢复操作需要指定备份文件路径")
                sys.exit(1)
            
            success = backup_system.restore_backup(args.file, args.type)
            if success:
                print("备份恢复成功")
            else:
                print("备份恢复失败")
                sys.exit(1)
        
        elif args.action == "list":
            backups = backup_system.list_backups()
            if not backups:
                print("没有找到备份文件")
            else:
                print(f"{'文件名':<30} {'类型':<10} {'大小':<10} {'创建时间'}")
                print("-" * 70)
                for backup in backups:
                    size_mb = backup["size"] / (1024 * 1024)
                    created = backup["created"].strftime("%Y-%m-%d %H:%M:%S")
                    print(f"{backup['name']:<30} {backup['type']:<10} {size_mb:.1f}MB {created}")
    
    except Exception as e:
        logger.error(f"操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
