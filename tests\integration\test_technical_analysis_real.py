#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术分析计算引擎真实数据测试

使用真实的市场数据测试技术指标计算功能。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.technical_analysis import TechnicalAnalysisEngine, IndicatorConfig
from src.data.models import ExchangeConfig


def test_technical_analysis_real():
    """测试技术分析计算引擎"""
    print("=== 技术分析计算引擎测试 ===")
    
    # 创建配置
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建交易所客户端
    exchange_client = ExchangeClient(config)
    
    try:
        # 连接交易所
        print("1. 连接交易所...")
        exchange_client.connect()
        print("✅ 交易所连接成功")
        
        # 创建技术分析引擎
        print("\n2. 创建技术分析引擎...")
        indicator_config = IndicatorConfig(
            sma_periods=[20, 50, 200],
            ema_periods=[12, 26, 50],
            rsi_period=14,
            bb_period=20,
            atr_period=14
        )
        
        ta_engine = TechnicalAnalysisEngine(indicator_config)
        print("✅ 技术分析引擎创建成功")
        
        # 获取测试数据
        print("\n3. 获取市场数据...")
        test_symbol = "BTC/USDT:USDT"
        timeframes = ["1m", "5m", "15m", "1h"]
        
        market_data = {}
        for timeframe in timeframes:
            ohlcv_data = exchange_client.fetch_ohlcv_sync(test_symbol, timeframe, 300)
            market_data[timeframe] = ohlcv_data
            print(f"  {timeframe}: {len(ohlcv_data)} 条K线数据")
        
        print("✅ 市场数据获取成功")
        
        # 测试单个时间周期的各类指标计算
        print("\n4. 测试各类技术指标计算...")
        test_data = market_data["1h"]  # 使用1小时数据测试
        
        # 测试趋势指标
        print("\n4.1 测试趋势指标...")
        trend_indicators = ta_engine.calculate_trend_indicators(test_data)
        
        print(f"计算出 {len(trend_indicators)} 个趋势指标:")
        for key, value in trend_indicators.items():
            if value is not None:
                print(f"  {key}: {value:.4f}")
        
        # 验证关键指标
        assert 'SMA_20' in trend_indicators
        assert 'EMA_12' in trend_indicators
        assert 'MACD' in trend_indicators
        assert 'ADX' in trend_indicators
        print("✅ 趋势指标计算成功")
        
        # 测试震荡指标
        print("\n4.2 测试震荡指标...")
        oscillator_indicators = ta_engine.calculate_oscillator_indicators(test_data)
        
        print(f"计算出 {len(oscillator_indicators)} 个震荡指标:")
        for key, value in oscillator_indicators.items():
            if value is not None:
                print(f"  {key}: {value:.2f}")
        
        # 验证关键指标
        assert 'RSI' in oscillator_indicators
        assert 'STOCH_K' in oscillator_indicators
        assert 'WILLIAMS_R' in oscillator_indicators
        assert 'CCI' in oscillator_indicators
        print("✅ 震荡指标计算成功")
        
        # 测试波动率指标
        print("\n4.3 测试波动率指标...")
        volatility_indicators = ta_engine.calculate_volatility_indicators(test_data)
        
        print(f"计算出 {len(volatility_indicators)} 个波动率指标:")
        for key, value in volatility_indicators.items():
            if value is not None:
                print(f"  {key}: {value:.4f}")
        
        # 验证关键指标
        assert 'BB_UPPER' in volatility_indicators
        assert 'BB_LOWER' in volatility_indicators
        assert 'ATR' in volatility_indicators
        assert 'KELTNER_UPPER' in volatility_indicators
        print("✅ 波动率指标计算成功")
        
        # 测试成交量指标
        print("\n4.4 测试成交量指标...")
        volume_indicators = ta_engine.calculate_volume_indicators(test_data)
        
        print(f"计算出 {len(volume_indicators)} 个成交量指标:")
        for key, value in volume_indicators.items():
            if value is not None:
                print(f"  {key}: {value:.2f}")
        
        # 验证关键指标
        assert 'OBV' in volume_indicators
        assert 'VOLUME_SMA' in volume_indicators
        assert 'VWAP' in volume_indicators
        print("✅ 成交量指标计算成功")
        
        # 测试支撑阻力
        print("\n4.5 测试支撑阻力计算...")
        support_resistance = ta_engine.calculate_support_resistance(test_data)
        
        print(f"计算出 {len(support_resistance)} 个支撑阻力位:")
        for key, value in support_resistance.items():
            if value is not None:
                print(f"  {key}: {value:.4f}")
        
        # 验证关键指标
        assert 'PIVOT_POINT' in support_resistance
        assert 'RESISTANCE_1' in support_resistance
        assert 'SUPPORT_1' in support_resistance
        assert 'FIB_50' in support_resistance
        print("✅ 支撑阻力计算成功")
        
        # 测试多时间周期分析
        print("\n5. 测试多时间周期分析...")
        multi_timeframe_indicators = ta_engine.analyze_multi_timeframe(market_data)
        
        print(f"成功分析 {len(multi_timeframe_indicators)} 个时间周期:")
        for timeframe, indicators in multi_timeframe_indicators.items():
            print(f"  {timeframe}: {indicators.symbol} - 计算时间 {indicators.calculated_at}")
            
            # 验证指标完整性
            assert indicators.trend_indicators
            assert indicators.oscillator_indicators
            assert indicators.volatility_indicators
            assert indicators.volume_indicators
            assert indicators.support_resistance
        
        print("✅ 多时间周期分析成功")
        
        # 测试AI格式化
        print("\n6. 测试AI格式化...")
        formatted_text = ta_engine.format_for_ai(multi_timeframe_indicators)
        
        print("AI格式化结果预览:")
        print(formatted_text[:500] + "..." if len(formatted_text) > 500 else formatted_text)
        
        # 验证格式化结果
        assert "时间周期技术分析" in formatted_text
        assert "趋势指标" in formatted_text
        assert "震荡指标" in formatted_text
        print("✅ AI格式化成功")
        
        # 测试信号摘要
        print("\n7. 测试信号摘要...")
        signal_summaries = ta_engine.get_signal_summary(multi_timeframe_indicators)
        
        print("技术指标信号摘要:")
        for timeframe, summary in signal_summaries.items():
            print(f"  {timeframe}: {summary}")
        
        # 验证信号摘要
        assert len(signal_summaries) == len(multi_timeframe_indicators)
        print("✅ 信号摘要生成成功")
        
        # 测试数据验证和错误处理
        print("\n8. 测试数据验证和错误处理...")
        
        # 测试数据不足的情况
        try:
            insufficient_data = test_data[:10]  # 只取10条数据
            ta_engine.calculate_trend_indicators(insufficient_data)
            print("⚠️ 数据不足时应该抛出异常")
        except Exception as e:
            print(f"✅ 正确处理数据不足异常: {type(e).__name__}")
        
        # 测试空数据
        empty_indicators = ta_engine.calculate_trend_indicators([])
        assert empty_indicators == {}
        print("✅ 正确处理空数据")
        
        print("✅ 错误处理测试完成")
        
        # 性能测试
        print("\n9. 性能测试...")
        import time
        
        start_time = time.time()
        for _ in range(10):
            ta_engine.analyze_multi_timeframe(market_data)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        print(f"平均计算时间: {avg_time:.3f}秒")
        print("✅ 性能测试完成")
        
        print("\n🎉 所有技术分析功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 技术分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = test_technical_analysis_real()
    if success:
        print("\n✅ 技术分析计算引擎测试全部通过！")
    else:
        print("\n❌ 技术分析计算引擎测试失败！")
        sys.exit(1)
