#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证测试

验证市场数据引擎和风险管理的修复。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.market_data_engine import MarketDataEngine
from src.core.risk_manager import RiskManager
from src.data.models import ExchangeConfig, RiskParameters


async def test_fixes_verification():
    """验证修复"""
    print("=== 修复验证测试 ===")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建风险参数
    risk_params = RiskParameters(
        max_leverage=10,
        max_position_ratio=0.5,
        min_balance_threshold=100.0,
        opening_confidence_threshold=70,
        position_confidence_threshold=60
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    
    try:
        # 1. 测试市场数据引擎修复
        print("\n1. 测试市场数据引擎修复...")
        
        exchange_client.connect()
        
        # 创建市场数据引擎
        symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT"]
        market_data_engine = MarketDataEngine(exchange_client, symbols)
        
        # 启动数据引擎
        await market_data_engine.start_data_polling()
        
        # 等待数据获取
        await asyncio.sleep(10)
        
        # 测试 get_symbol_data 方法
        btc_data = await market_data_engine.get_symbol_data("BTC/USDT:USDT")
        
        if btc_data:
            print(f"✅ 市场数据引擎修复成功")
            print(f"  BTC数据时间周期: {list(btc_data.keys())}")
            for timeframe, data in btc_data.items():
                print(f"  {timeframe}: {len(data)} 条K线")
        else:
            print("❌ 市场数据引擎仍有问题")
        
        # 停止数据引擎
        await market_data_engine.stop_data_polling()
        
        # 2. 测试风险管理修复
        print("\n2. 测试风险管理修复...")
        
        risk_manager = RiskManager(risk_params)
        
        # 获取真实数据
        balance = exchange_client.fetch_balance()
        positions = exchange_client.fetch_positions()
        
        print(f"账户余额: {len(balance)} 个币种")
        print(f"持仓数量: {len(positions)} 个")
        
        # 显示余额详情
        for currency, bal in balance.items():
            if bal.total > 0:
                print(f"  {currency}: 总计={bal.total:.2f}, 可用={bal.available:.2f}")
        
        # 显示持仓详情
        for pos in positions:
            exposure = abs(pos.amount) * pos.current_price
            print(f"  {pos.symbol}: {pos.side.value} {pos.amount} @ {pos.current_price:.4f}")
            print(f"    敞口: {exposure:.2f}, 保证金: {pos.margin_used:.2f}")
        
        # 测试投资组合风险检查
        portfolio_risk = risk_manager.check_portfolio_risk(positions, balance, exchange_client)
        
        print(f"\n投资组合风险检查结果:")
        print(f"  通过: {portfolio_risk.passed}")
        print(f"  风险等级: {portfolio_risk.risk_level}")
        print(f"  警告数量: {len(portfolio_risk.warnings)}")
        print(f"  错误数量: {len(portfolio_risk.errors)}")
        
        for warning in portfolio_risk.warnings:
            print(f"  警告: {warning}")
        for error in portfolio_risk.errors:
            print(f"  错误: {error}")
        
        # 测试风险摘要
        risk_summary = risk_manager.get_risk_summary(positions, balance, exchange_client)
        
        print(f"\n风险摘要:")
        print(f"  总余额: {risk_summary.get('total_balance', 0):.2f}")
        print(f"  总敞口: {risk_summary.get('total_exposure', 0):.2f}")
        print(f"  敞口比率: {risk_summary.get('exposure_ratio', 0):.2%}")
        print(f"  保证金使用率: {risk_summary.get('margin_usage_ratio', 0):.2%}")
        print(f"  紧急模式: {risk_summary.get('emergency_mode', False)}")
        
        # 验证敞口比率是否合理
        exposure_ratio = risk_summary.get('exposure_ratio', 0)
        if exposure_ratio > 100:  # 超过10000%
            print(f"❌ 敞口比率仍然异常: {exposure_ratio:.2%}")
        else:
            print(f"✅ 敞口比率正常: {exposure_ratio:.2%}")
        
        # 3. 测试紧急风控模式
        print("\n3. 测试紧急风控模式...")
        
        print(f"当前紧急模式状态: {risk_manager.emergency_mode}")
        
        # 如果当前处于紧急模式，测试是否能正确处理
        if risk_manager.emergency_mode:
            print("系统当前处于紧急风控模式")
            
            # 测试开仓是否被阻止
            test_risk_result = risk_manager.validate_opening_order(
                symbol="BTC/USDT:USDT",
                side="buy",
                amount=0.001,
                leverage=2,
                price=50000.0,
                balance=balance
            )
            
            print(f"紧急模式下开仓测试:")
            print(f"  通过: {test_risk_result.passed}")
            print(f"  错误: {test_risk_result.errors}")
            
            if not test_risk_result.passed and "紧急风控模式" in str(test_risk_result.errors):
                print("✅ 紧急风控模式正确阻止了开仓")
            else:
                print("❌ 紧急风控模式未正确工作")
        
        else:
            print("系统当前未处于紧急风控模式")
            
            # 手动启用紧急模式测试
            risk_manager.enable_emergency_mode("测试紧急模式")
            print(f"手动启用后状态: {risk_manager.emergency_mode}")
            
            # 测试开仓阻止
            test_risk_result = risk_manager.validate_opening_order(
                symbol="BTC/USDT:USDT",
                side="buy",
                amount=0.001,
                leverage=2,
                price=50000.0,
                balance=balance
            )
            
            print(f"紧急模式测试结果: {not test_risk_result.passed}")
            
            # 禁用紧急模式
            risk_manager.disable_emergency_mode()
            print(f"禁用后状态: {risk_manager.emergency_mode}")
        
        print("\n🎉 所有修复验证测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = asyncio.run(test_fixes_verification())
    if success:
        print("\n✅ 修复验证测试全部通过！")
    else:
        print("\n❌ 修复验证测试失败！")
        sys.exit(1)
