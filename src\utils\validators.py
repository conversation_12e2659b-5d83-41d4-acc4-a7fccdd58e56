#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统数据验证工具模块

此模块提供各种数据验证功能，包括：
1. 交易参数验证
2. 市场数据验证
3. 配置参数验证
4. API响应验证
"""

import re
from typing import Any, List, Dict, Optional, Union, Tuple
from decimal import Decimal, InvalidOperation
from datetime import datetime

from src.utils.exceptions import DataValidationError


class ValidationResult:
    """验证结果类"""
    
    def __init__(self, is_valid: bool = True, errors: Optional[List[str]] = None):
        """初始化验证结果
        
        Args:
            is_valid: 是否验证通过
            errors: 错误信息列表
        """
        self.is_valid = is_valid
        self.errors = errors or []
    
    def add_error(self, error: str):
        """添加错误信息"""
        self.errors.append(error)
        self.is_valid = False
    
    def merge(self, other: 'ValidationResult'):
        """合并其他验证结果"""
        if not other.is_valid:
            self.is_valid = False
            self.errors.extend(other.errors)


class BaseValidator:
    """基础验证器类"""
    
    @staticmethod
    def validate_required(value: Any, field_name: str) -> ValidationResult:
        """验证必需字段
        
        Args:
            value: 要验证的值
            field_name: 字段名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        if value is None or (isinstance(value, str) and not value.strip()):
            result.add_error(f"{field_name} 是必需的")
        return result
    
    @staticmethod
    def validate_type(value: Any, expected_type: type, field_name: str) -> ValidationResult:
        """验证数据类型
        
        Args:
            value: 要验证的值
            expected_type: 期望的类型
            field_name: 字段名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        if not isinstance(value, expected_type):
            result.add_error(f"{field_name} 必须是 {expected_type.__name__} 类型")
        return result
    
    @staticmethod
    def validate_range(value: Union[int, float], min_val: Optional[Union[int, float]], 
                      max_val: Optional[Union[int, float]], field_name: str) -> ValidationResult:
        """验证数值范围
        
        Args:
            value: 要验证的值
            min_val: 最小值
            max_val: 最大值
            field_name: 字段名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        if min_val is not None and value < min_val:
            result.add_error(f"{field_name} 不能小于 {min_val}")
        if max_val is not None and value > max_val:
            result.add_error(f"{field_name} 不能大于 {max_val}")
        return result
    
    @staticmethod
    def validate_choices(value: Any, choices: List[Any], field_name: str) -> ValidationResult:
        """验证选择项
        
        Args:
            value: 要验证的值
            choices: 可选择的值列表
            field_name: 字段名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        if value not in choices:
            result.add_error(f"{field_name} 必须是以下值之一: {choices}")
        return result


class TradingValidator(BaseValidator):
    """交易相关验证器"""
    
    @staticmethod
    def validate_symbol(symbol: str) -> ValidationResult:
        """验证交易对符号
        
        Args:
            symbol: 交易对符号
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查是否为空
        required_result = TradingValidator.validate_required(symbol, "交易对符号")
        result.merge(required_result)
        if not result.is_valid:
            return result
        
        # 检查格式（例如：BTC/USDT:USDT）
        pattern = r'^[A-Z0-9]+/[A-Z0-9]+(:([A-Z0-9]+))?$'
        if not re.match(pattern, symbol):
            result.add_error("交易对符号格式无效，应为 BASE/QUOTE 或 BASE/QUOTE:SETTLE 格式")
        
        return result
    
    @staticmethod
    def validate_side(side: str) -> ValidationResult:
        """验证交易方向
        
        Args:
            side: 交易方向
        
        Returns:
            ValidationResult: 验证结果
        """
        return TradingValidator.validate_choices(
            side.lower() if side else side,
            ["long", "short", "buy", "sell"],
            "交易方向"
        )
    
    @staticmethod
    def validate_amount(amount: Union[int, float]) -> ValidationResult:
        """验证交易数量
        
        Args:
            amount: 交易数量
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查类型
        type_result = TradingValidator.validate_type(amount, (int, float), "交易数量")
        result.merge(type_result)
        if not result.is_valid:
            return result
        
        # 检查范围
        range_result = TradingValidator.validate_range(amount, 0.000001, None, "交易数量")
        result.merge(range_result)
        
        return result
    
    @staticmethod
    def validate_price(price: Union[int, float]) -> ValidationResult:
        """验证价格
        
        Args:
            price: 价格
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查类型
        type_result = TradingValidator.validate_type(price, (int, float), "价格")
        result.merge(type_result)
        if not result.is_valid:
            return result
        
        # 检查范围
        range_result = TradingValidator.validate_range(price, 0.000001, None, "价格")
        result.merge(range_result)
        
        return result
    
    @staticmethod
    def validate_leverage(leverage: int) -> ValidationResult:
        """验证杠杆倍数
        
        Args:
            leverage: 杠杆倍数
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查类型
        type_result = TradingValidator.validate_type(leverage, int, "杠杆倍数")
        result.merge(type_result)
        if not result.is_valid:
            return result
        
        # 检查范围
        range_result = TradingValidator.validate_range(leverage, 1, 100, "杠杆倍数")
        result.merge(range_result)
        
        return result
    
    @staticmethod
    def validate_percentage(percentage: float, field_name: str) -> ValidationResult:
        """验证百分比
        
        Args:
            percentage: 百分比值
            field_name: 字段名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查类型
        type_result = TradingValidator.validate_type(percentage, (int, float), field_name)
        result.merge(type_result)
        if not result.is_valid:
            return result
        
        # 检查范围（0-1之间）
        range_result = TradingValidator.validate_range(percentage, 0.0, 1.0, field_name)
        result.merge(range_result)
        
        return result


class MarketDataValidator(BaseValidator):
    """市场数据验证器"""
    
    @staticmethod
    def validate_ohlcv_data(ohlcv: Dict[str, Any]) -> ValidationResult:
        """验证OHLCV数据
        
        Args:
            ohlcv: OHLCV数据字典
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查必需字段
        required_fields = ["timestamp", "open", "high", "low", "close", "volume"]
        for field in required_fields:
            if field not in ohlcv:
                result.add_error(f"缺少必需字段: {field}")
                continue
            
            # 验证数值类型
            if field == "timestamp":
                type_result = MarketDataValidator.validate_type(
                    ohlcv[field], (int, float), field
                )
            else:
                type_result = MarketDataValidator.validate_type(
                    ohlcv[field], (int, float), field
                )
            result.merge(type_result)
            
            # 验证数值范围
            if field != "timestamp":
                range_result = MarketDataValidator.validate_range(
                    ohlcv[field], 0, None, field
                )
                result.merge(range_result)
        
        # 验证价格逻辑关系
        if result.is_valid:
            high = ohlcv["high"]
            low = ohlcv["low"]
            open_price = ohlcv["open"]
            close = ohlcv["close"]
            
            if high < low:
                result.add_error("最高价不能低于最低价")
            if high < max(open_price, close):
                result.add_error("最高价不能低于开盘价或收盘价")
            if low > min(open_price, close):
                result.add_error("最低价不能高于开盘价或收盘价")
        
        return result
    
    @staticmethod
    def validate_timeframe(timeframe: str) -> ValidationResult:
        """验证时间周期
        
        Args:
            timeframe: 时间周期
        
        Returns:
            ValidationResult: 验证结果
        """
        valid_timeframes = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
        return MarketDataValidator.validate_choices(
            timeframe, valid_timeframes, "时间周期"
        )


class ConfigValidator(BaseValidator):
    """配置验证器"""
    
    @staticmethod
    def validate_api_key(api_key: str, key_name: str) -> ValidationResult:
        """验证API密钥
        
        Args:
            api_key: API密钥
            key_name: 密钥名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查是否为空
        required_result = ConfigValidator.validate_required(api_key, key_name)
        result.merge(required_result)
        if not result.is_valid:
            return result
        
        # 检查长度
        if len(api_key) < 10:
            result.add_error(f"{key_name} 长度过短")
        
        return result
    
    @staticmethod
    def validate_url(url: str, field_name: str) -> ValidationResult:
        """验证URL
        
        Args:
            url: URL地址
            field_name: 字段名称
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查是否为空
        required_result = ConfigValidator.validate_required(url, field_name)
        result.merge(required_result)
        if not result.is_valid:
            return result
        
        # 检查URL格式
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        if not re.match(url_pattern, url):
            result.add_error(f"{field_name} 格式无效")
        
        return result
    
    @staticmethod
    def validate_port(port: int) -> ValidationResult:
        """验证端口号
        
        Args:
            port: 端口号
        
        Returns:
            ValidationResult: 验证结果
        """
        return ConfigValidator.validate_range(port, 1024, 65535, "端口号")


class AIResponseValidator(BaseValidator):
    """AI响应验证器"""
    
    @staticmethod
    def validate_confidence(confidence: Union[int, float]) -> ValidationResult:
        """验证置信度
        
        Args:
            confidence: 置信度值
        
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult()
        
        # 检查类型
        type_result = AIResponseValidator.validate_type(
            confidence, (int, float), "置信度"
        )
        result.merge(type_result)
        if not result.is_valid:
            return result
        
        # 检查范围
        range_result = AIResponseValidator.validate_range(
            confidence, 0, 100, "置信度"
        )
        result.merge(range_result)
        
        return result
    
    @staticmethod
    def validate_ai_action(action: str) -> ValidationResult:
        """验证AI动作
        
        Args:
            action: AI动作
        
        Returns:
            ValidationResult: 验证结果
        """
        valid_actions = [
            "open_long", "open_short", "close_position", 
            "hold", "no_action", "adjust_stop_loss", "take_profit"
        ]
        return AIResponseValidator.validate_choices(
            action, valid_actions, "AI动作"
        )


def validate_trading_order(order_data: Dict[str, Any]) -> ValidationResult:
    """验证交易订单数据
    
    Args:
        order_data: 订单数据字典
    
    Returns:
        ValidationResult: 验证结果
    """
    result = ValidationResult()
    
    # 验证交易对
    if "symbol" in order_data:
        symbol_result = TradingValidator.validate_symbol(order_data["symbol"])
        result.merge(symbol_result)
    
    # 验证交易方向
    if "side" in order_data:
        side_result = TradingValidator.validate_side(order_data["side"])
        result.merge(side_result)
    
    # 验证交易数量
    if "amount" in order_data:
        amount_result = TradingValidator.validate_amount(order_data["amount"])
        result.merge(amount_result)
    
    # 验证价格（如果是限价单）
    if "price" in order_data:
        price_result = TradingValidator.validate_price(order_data["price"])
        result.merge(price_result)
    
    # 验证杠杆
    if "leverage" in order_data:
        leverage_result = TradingValidator.validate_leverage(order_data["leverage"])
        result.merge(leverage_result)
    
    return result


def validate_ai_decision(decision_data: Dict[str, Any]) -> ValidationResult:
    """验证AI决策数据
    
    Args:
        decision_data: AI决策数据字典
    
    Returns:
        ValidationResult: 验证结果
    """
    result = ValidationResult()
    
    # 验证动作
    if "action" in decision_data:
        action_result = AIResponseValidator.validate_ai_action(decision_data["action"])
        result.merge(action_result)
    
    # 验证置信度
    if "confidence" in decision_data:
        confidence_result = AIResponseValidator.validate_confidence(decision_data["confidence"])
        result.merge(confidence_result)
    
    # 验证推理说明
    if "reasoning" in decision_data:
        reasoning_result = BaseValidator.validate_required(decision_data["reasoning"], "推理说明")
        result.merge(reasoning_result)
    
    return result


def validate_positive_number(value: Any) -> bool:
    """验证正数

    Args:
        value: 要验证的值

    Returns:
        bool: 是否为正数
    """
    try:
        num = float(value)
        return num > 0
    except (ValueError, TypeError):
        return False


def validate_percentage(value: Any) -> bool:
    """验证百分比（0-1之间的数值）

    Args:
        value: 要验证的值

    Returns:
        bool: 是否为有效百分比
    """
    try:
        num = float(value)
        return 0 <= num <= 1
    except (ValueError, TypeError):
        return False
