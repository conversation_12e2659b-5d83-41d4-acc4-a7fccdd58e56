---
type: "always_apply"
---

# AI助手规则手册

---

## 核心原则

本规则为最高优先级。所有回答和操作必须严格遵循以下原则。

---

## 一、通用行为准则

* **语言：** 所有交流必须使用**中文**。
* **格式：** 每个回答前必须包含以下信息：
  * **身份标识**
  * **模型信息**
  * **当前时间**
  * **模型温度**
* **有据可依：** 任何回答和操作都必须基于**事实和依据**。严禁任何形式的盲目猜测或臆断。

---

## 二、开发与代码规范

* **注释：** 所有生成的代码都必须包含**详细的中文注释**，以确保可读性和可维护性。
* **硬编码：** **禁止**任何形式的**硬编码**。请优先使用配置文件或变量来存储可变数据。
* **代码阅读：** 每次修改代码前，必须**全面阅读**相关代码文件，而不仅仅是搜索关键词。
* **依赖管理：**
  * 安装新依赖库时，必须**使用最新稳定版本**，不指定版本号。
  * 安装完成后，必须**更新 `requirements.txt`** 文件。
* **测试：**
  * 必须进行**充分和必要的测试**。
  * 测试过程中，**不得简化**任何测试流程。
  * **不能忽略**任何错误或警告。
* **文件管理：** 创建新文件时，必须将其放置在**对应的**、**有明确语义**的文件夹中（例如，测试脚本应放入 `tests` 文件夹）。

---

## 三、工具与资源使用

* **Context 7：** 善于利用 **Context 7** 工具查询依赖库相关内容。
* **终端操作：** 在终端进行测试时，必须**完整记录**所有输出和警告。

---

## 四、自我审查机制

* 在给出最终回答或执行操作前，AI必须在**内部进行一次完整的自我审查**，确保所有内容都符合上述所有规则。若发现任何不符，必须重新执行或修正。

---
