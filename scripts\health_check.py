#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统健康检查脚本

此脚本负责系统健康状态检查，包括：
1. 系统资源检查
2. 服务状态检查
3. 数据库连接检查
4. API接口检查
5. 配置完整性检查
"""

import os
import sys
import time
import psutil
import sqlite3
import requests
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger

logger = get_logger(__name__)


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, api_url: str = "http://127.0.0.1:8000"):
        """初始化健康检查器
        
        Args:
            api_url: API服务地址
        """
        self.api_url = api_url
        self.project_root = project_root
        self.checks = []
        
        # 健康检查阈值
        self.thresholds = {
            "cpu_usage": 80.0,      # CPU使用率阈值
            "memory_usage": 85.0,   # 内存使用率阈值
            "disk_usage": 90.0,     # 磁盘使用率阈值
            "response_time": 5.0    # API响应时间阈值（秒）
        }
    
    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查
        
        Returns:
            Dict[str, Any]: 检查结果
        """
        logger.info("开始系统健康检查...")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {},
            "warnings": [],
            "errors": []
        }
        
        # 执行各项检查
        checks = [
            ("system_resources", self._check_system_resources),
            ("database", self._check_database),
            ("api_service", self._check_api_service),
            ("configuration", self._check_configuration),
            ("file_system", self._check_file_system),
            ("processes", self._check_processes)
        ]
        
        for check_name, check_func in checks:
            try:
                check_result = check_func()
                results["checks"][check_name] = check_result
                
                if check_result["status"] == "error":
                    results["errors"].append(f"{check_name}: {check_result['message']}")
                    results["overall_status"] = "unhealthy"
                elif check_result["status"] == "warning":
                    results["warnings"].append(f"{check_name}: {check_result['message']}")
                    if results["overall_status"] == "healthy":
                        results["overall_status"] = "warning"
                
            except Exception as e:
                error_msg = f"{check_name} 检查失败: {e}"
                logger.error(error_msg)
                results["checks"][check_name] = {
                    "status": "error",
                    "message": str(e)
                }
                results["errors"].append(error_msg)
                results["overall_status"] = "unhealthy"
        
        # 输出结果
        self._print_results(results)
        
        return results
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            
            # 网络连接数
            connections = len(psutil.net_connections())
            
            # 进程数
            process_count = len(psutil.pids())
            
            # 负载平均值（Linux/macOS）
            load_avg = None
            try:
                load_avg = os.getloadavg()
            except (OSError, AttributeError):
                pass  # Windows不支持
            
            details = {
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "disk_usage": disk_usage,
                "disk_total_gb": round(disk.total / (1024**3), 2),
                "disk_free_gb": round(disk.free / (1024**3), 2),
                "connections": connections,
                "processes": process_count,
                "load_avg": load_avg
            }
            
            # 检查阈值
            status = "healthy"
            messages = []
            
            if cpu_usage > self.thresholds["cpu_usage"]:
                status = "warning"
                messages.append(f"CPU使用率过高: {cpu_usage:.1f}%")
            
            if memory_usage > self.thresholds["memory_usage"]:
                status = "warning"
                messages.append(f"内存使用率过高: {memory_usage:.1f}%")
            
            if disk_usage > self.thresholds["disk_usage"]:
                status = "error"
                messages.append(f"磁盘使用率过高: {disk_usage:.1f}%")
            
            return {
                "status": status,
                "message": "; ".join(messages) if messages else "系统资源正常",
                "details": details
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"系统资源检查失败: {e}",
                "details": {}
            }
    
    def _check_database(self) -> Dict[str, Any]:
        """检查数据库"""
        try:
            db_file = self.project_root / "data" / "trading_system.db"
            
            if not db_file.exists():
                return {
                    "status": "error",
                    "message": "数据库文件不存在",
                    "details": {"db_file": str(db_file)}
                }
            
            # 检查数据库连接
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                "exchange_config",
                "trading_parameters",
                "selected_symbols",
                "system_logs"
            ]
            
            missing_tables = [table for table in expected_tables if table not in tables]
            
            # 检查数据库大小
            db_size = db_file.stat().st_size
            
            conn.close()
            
            details = {
                "db_file": str(db_file),
                "db_size_mb": round(db_size / (1024**2), 2),
                "tables": tables,
                "missing_tables": missing_tables
            }
            
            if missing_tables:
                return {
                    "status": "warning",
                    "message": f"缺少数据库表: {', '.join(missing_tables)}",
                    "details": details
                }
            
            return {
                "status": "healthy",
                "message": "数据库连接正常",
                "details": details
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"数据库检查失败: {e}",
                "details": {}
            }
    
    def _check_api_service(self) -> Dict[str, Any]:
        """检查API服务"""
        try:
            # 检查API服务是否响应
            start_time = time.time()
            
            try:
                response = requests.get(f"{self.api_url}/api/system/health", timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    health_data = response.json()
                    
                    details = {
                        "api_url": self.api_url,
                        "response_time": round(response_time, 3),
                        "status_code": response.status_code,
                        "health_data": health_data
                    }
                    
                    if response_time > self.thresholds["response_time"]:
                        return {
                            "status": "warning",
                            "message": f"API响应时间过长: {response_time:.3f}s",
                            "details": details
                        }
                    
                    return {
                        "status": "healthy",
                        "message": "API服务正常",
                        "details": details
                    }
                else:
                    return {
                        "status": "error",
                        "message": f"API服务返回错误状态码: {response.status_code}",
                        "details": {
                            "api_url": self.api_url,
                            "status_code": response.status_code,
                            "response_time": round(response_time, 3)
                        }
                    }
                    
            except requests.exceptions.ConnectionError:
                return {
                    "status": "error",
                    "message": "无法连接到API服务",
                    "details": {"api_url": self.api_url}
                }
            except requests.exceptions.Timeout:
                return {
                    "status": "error",
                    "message": "API服务响应超时",
                    "details": {"api_url": self.api_url}
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"API服务检查失败: {e}",
                "details": {}
            }
    
    def _check_configuration(self) -> Dict[str, Any]:
        """检查配置"""
        try:
            issues = []
            
            # 检查环境变量文件
            env_file = self.project_root / ".env"
            if not env_file.exists():
                issues.append("缺少.env环境变量文件")
            
            # 检查配置目录
            config_dir = self.project_root / "config"
            if not config_dir.exists():
                issues.append("缺少config配置目录")
            else:
                # 检查配置文件
                required_configs = [
                    "settings.py",
                    "logging_config.py",
                    "indicators_config.py"
                ]
                
                for config_file in required_configs:
                    if not (config_dir / config_file).exists():
                        issues.append(f"缺少配置文件: {config_file}")
            
            # 检查日志目录
            logs_dir = self.project_root / "logs"
            if not logs_dir.exists():
                issues.append("缺少logs日志目录")
            
            # 检查数据目录
            data_dir = self.project_root / "data"
            if not data_dir.exists():
                issues.append("缺少data数据目录")
            
            details = {
                "env_file_exists": env_file.exists(),
                "config_dir_exists": config_dir.exists(),
                "logs_dir_exists": logs_dir.exists(),
                "data_dir_exists": data_dir.exists(),
                "issues": issues
            }
            
            if issues:
                return {
                    "status": "warning",
                    "message": f"配置问题: {'; '.join(issues)}",
                    "details": details
                }
            
            return {
                "status": "healthy",
                "message": "配置检查正常",
                "details": details
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"配置检查失败: {e}",
                "details": {}
            }
    
    def _check_file_system(self) -> Dict[str, Any]:
        """检查文件系统"""
        try:
            # 检查关键目录的权限
            directories = [
                self.project_root / "data",
                self.project_root / "logs",
                self.project_root / "config"
            ]
            
            permission_issues = []
            
            for directory in directories:
                if directory.exists():
                    if not os.access(directory, os.R_OK):
                        permission_issues.append(f"{directory}: 无读权限")
                    if not os.access(directory, os.W_OK):
                        permission_issues.append(f"{directory}: 无写权限")
            
            # 检查磁盘空间
            disk = psutil.disk_usage(str(self.project_root))
            free_space_gb = disk.free / (1024**3)
            
            details = {
                "permission_issues": permission_issues,
                "free_space_gb": round(free_space_gb, 2),
                "total_space_gb": round(disk.total / (1024**3), 2),
                "used_space_gb": round(disk.used / (1024**3), 2)
            }
            
            if permission_issues:
                return {
                    "status": "error",
                    "message": f"权限问题: {'; '.join(permission_issues)}",
                    "details": details
                }
            
            if free_space_gb < 1.0:  # 少于1GB
                return {
                    "status": "warning",
                    "message": f"磁盘空间不足: {free_space_gb:.2f}GB",
                    "details": details
                }
            
            return {
                "status": "healthy",
                "message": "文件系统正常",
                "details": details
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"文件系统检查失败: {e}",
                "details": {}
            }
    
    def _check_processes(self) -> Dict[str, Any]:
        """检查进程"""
        try:
            # 查找相关进程
            trading_processes = []
            web_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    
                    if 'trading_coordinator' in cmdline or 'main.py' in cmdline:
                        trading_processes.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cmdline": cmdline
                        })
                    
                    if 'uvicorn' in cmdline and 'src.web.app' in cmdline:
                        web_processes.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cmdline": cmdline
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            details = {
                "trading_processes": trading_processes,
                "web_processes": web_processes,
                "total_processes": len(psutil.pids())
            }
            
            messages = []
            status = "healthy"
            
            if not web_processes:
                messages.append("未找到Web服务进程")
                status = "warning"
            
            if not trading_processes:
                messages.append("未找到交易系统进程")
                # 这可能是正常的，如果只启动了Web服务
            
            return {
                "status": status,
                "message": "; ".join(messages) if messages else "进程检查正常",
                "details": details
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"进程检查失败: {e}",
                "details": {}
            }
    
    def _print_results(self, results: Dict[str, Any]):
        """打印检查结果"""
        print("\n" + "="*60)
        print("DeepSeek量化交易系统健康检查报告")
        print("="*60)
        print(f"检查时间: {results['timestamp']}")
        print(f"总体状态: {results['overall_status'].upper()}")
        print()
        
        # 打印各项检查结果
        for check_name, check_result in results["checks"].items():
            status_symbol = {
                "healthy": "✓",
                "warning": "⚠",
                "error": "✗"
            }.get(check_result["status"], "?")
            
            print(f"{status_symbol} {check_name}: {check_result['message']}")
        
        # 打印警告和错误
        if results["warnings"]:
            print("\n警告:")
            for warning in results["warnings"]:
                print(f"  ⚠ {warning}")
        
        if results["errors"]:
            print("\n错误:")
            for error in results["errors"]:
                print(f"  ✗ {error}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DeepSeek量化交易系统健康检查")
    parser.add_argument("--api-url", default="http://127.0.0.1:8000", help="API服务地址")
    parser.add_argument("--json", action="store_true", help="输出JSON格式结果")
    parser.add_argument("--exit-code", action="store_true", help="根据健康状态设置退出码")
    
    args = parser.parse_args()
    
    # 创建健康检查器
    checker = HealthChecker(args.api_url)
    
    # 运行检查
    results = checker.run_all_checks()
    
    # 输出JSON格式结果
    if args.json:
        import json
        print(json.dumps(results, indent=2, ensure_ascii=False))
    
    # 设置退出码
    if args.exit_code:
        if results["overall_status"] == "unhealthy":
            sys.exit(1)
        elif results["overall_status"] == "warning":
            sys.exit(2)
        else:
            sys.exit(0)


if __name__ == "__main__":
    main()
