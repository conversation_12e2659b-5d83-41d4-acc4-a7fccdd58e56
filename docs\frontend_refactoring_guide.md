# 前端重构计划与开发规范

## 1. 引言

本文档旨在通过对现有前端代码进行全面分析，制定一套完整、统一的开发规范和详细的重构计划。目标是提高代码质量、可维护性和开发效率，为系统的长期迭代和功能扩展奠定坚实的基础。

本次重构专注于代码结构、规范和最佳实践的落地，**不涉及业务逻辑的重大变更**。

## 2. 代码分析报告

在审阅 `src/web/` 目录下的前端代码后，我们识别出以下主要问题：

*   **样式全局污染**: 所有 CSS 文件（`antd-theme.css`, `console.css`, `settings.css`）均在全局范围内定义样式，容易导致选择器冲突和样式覆盖问题，维护成本高。
*   **命名不一致**:
    *   CSS 类名混合使用了多种风格（如 `text-success`, `stats-grid`, `action-btn`），缺乏统一标准。
    *   JavaScript 函数和变量命名风格不统一，存在驼峰式和下划线式混用的情况。
*   **结构混乱**: HTML、CSS 和 JavaScript 文件按类型分目录存放，但未按功能或组件进行组织。导致开发和维护单个功能时，需要在不同目录间频繁切换。
*   **代码重复**:
    *   多个 CSS 文件中重复定义了颜色、字体、间距等变量。
    *   多个 JavaScript 文件中存在功能类似的函数，例如 `showNotification` 和 `apiRequest` 在 `utils.js` 和 `settings.js` 中都有实现。
    *   HTML 模板中存在大量重复的 UI 结构，如卡片、按钮等。
*   **缺乏模块化**: JavaScript 代码大量依赖全局变量和函数（如 `window.apiRequest`），耦合度高，不利于代码复用和测试。

## 3. 前端开发规范

为解决上述问题，我们制定以下开发规范：

### 3.1 命名约定

*   **CSS**:
    *   **BEM (Block, Element, Modifier)**: 所有 CSS 类名必须遵循 BEM 命名法。
        *   **Block**: 组件的根节点 (e.g., `.card`, `.nav-menu`)。
        *   **Element**: 组件的组成部分 (e.g., `.card__header`, `.nav-menu__item`)。
        *   **Modifier**: 组件的不同状态或变体 (e.g., `.card--featured`, `.nav-menu__item--active`)。
    *   **示例**:
        ```css
        /* Block */
        .stat-card { /* ... */ }
        /* Element */
        .stat-card__title { /* ... */ }
        .stat-card__value { /* ... */ }
        /* Modifier */
        .stat-card--positive { /* ... */ }
        ```

*   **JavaScript**:
    *   **变量/函数**: 使用小驼峰命名法 (`camelCase`)。 e.g., `const userData`, `function fetchUserData()`.
    *   **类/构造函数**: 使用大驼峰命名法 (`PascalCase`)。 e.g., `class DataManager`.
    *   **常量**: 使用全大写和下划线命名法 (`UPPER_CASE`)。 e.g., `const API_URL = '/api'`.

*   **文件**:
    *   **组件**: 使用大驼峰命名法 (`PascalCase`)。 e.g., `StatCard.js`, `StatCard.css`.
    *   **工具/服务**: 使用小驼峰命名法 (`camelCase`)。 e.g., `apiClient.js`.
    *   **页面**: 使用短横线命名法 (`kebab-case`)。 e.g., `dashboard-page.js`.

### 3.2 编码风格

*   **代码格式化**:
    *   统一使用 **Prettier** 进行代码格式化，确保代码风格一致。
    *   推荐配置 (`.prettierrc`):
        ```json
        {
          "semi": true,
          "singleQuote": true,
          "tabWidth": 2,
          "trailingComma": "es5",
          "printWidth": 80
        }
        ```

*   **注释**:
    *   对复杂函数、类和模块使用 **JSDoc** 风格的注释。
    *   对 CSS 中的 `z-index` 值和复杂的选择器进行简要说明。

*   **JavaScript 最佳实践**:
    *   **模块化**: 使用 ES6 模块 (`import`/`export`)，禁止使用全局变量传递状态。
    *   **变量声明**: 优先使用 `const`，仅在需要重新赋值时使用 `let`。禁止使用 `var`。
    *   **异步编程**: 统一使用 `async/await` 处理异步操作，提高代码可读性。
    *   **DOM 操作**: 封装 DOM 操作，避免在业务逻辑中直接混入大量 `document.getElementById` 等代码。

*   **CSS 最佳实践**:
    *   **CSS 变量**: 将颜色、字体、间距等设计令牌（Design Tokens）定义为 CSS 自定义属性，实现主题化和易维护性。
    *   **避免 ID 选择器**: 优先使用类选择器，降低样式特异性，避免覆盖问题。
    *   **移动优先**: 采用移动优先的原则进行响应式设计。

## 4. 重构计划

### 4.1 新的文件和目录结构

我们将采用按功能/组件组织文件的结构，以提升代码的内聚性和可维护性。

```mermaid
graph TD
    A[src/web/] --> B[components]
    A --> C[pages]
    A --> D[static]
    A --> E[services]
    A --> F[styles]
    A --> G[templates]

    B --> B1[StatCard/index.js]
    B --> B2[StatCard/style.css]
    B --> B3[...]

    C --> C1[dashboard/index.js]
    C --> C2[dashboard/style.css]
    C --> C3[...]

    D --> D1[assets/images]
    D --> D2[assets/fonts]

    E --> E1[apiClient.js]
    E --> E2[notificationService.js]

    F --> F1[_variables.css]
    F --> F2[global.css]
```

*   **`components/`**: 存放可重用的 UI 组件（如 `Card`, `Button`, `Modal`）。每个组件一个目录，包含其逻辑、样式和模板。
*   **`pages/`**: 存放特定页面的代码。每个页面一个目录，组织该页面的特定逻辑和样式。
*   **`services/`**: 存放应用级的服务，如 API 请求封装 (`apiClient.js`)、通知服务等。
*   **`static/assets/`**: 存放真正的静态资源，如图片、字体等。
*   **`styles/`**: 存放全局样式，如 CSS 变量定义 (`_variables.css`) 和基础样式重置 (`global.css`)。
*   **`templates/`**: 继续保留基础的 HTML 骨架 (`base.html`) 和各页面的入口模板。

### 4.2 组件化策略

将现有 UI 元素抽象为可重用的组件是本次重构的核心。以下是初步的组件列表：

| 组件名 | 描述 | 对应现有代码 |
| :--- | :--- | :--- |
| `StatCard` | 仪表板上的统计卡片 | `dashboard.html` 中的 `.stat-card` |
| `Button` | 通用按钮 | `.action-btn`, `.ant-btn` |
| `Modal` | 确认对话框和模态窗口 | `antd-components.js` 中的 `AntModal` |
| `Notification` | 全局通知提醒 | `antd-components.js` 中的 `AntNotification` |
| `Table` | 数据表格 | `.ant-table` |
| `Tabs` | 选项卡组件 | `settings.css` 中的 `.nav-tab` |
| `Input` | 表单输入框 | `.form-control` |
| `RiskIndicator`| 风险监控指示器 | `dashboard.html` 中的 `.risk-indicator` |

### 4.3 实施步骤

1.  **环境搭建**:
    *   引入 `prettier` 和 `eslint` 以强制执行代码规范。
    *   配置构建工具（如 Vite 或 Webpack）来处理模块化 JS 和 CSS。
2.  **基础重构**:
    *   创建新的目录结构。
    *   将现有 CSS 中的颜色、字体等提取到 `styles/_variables.css`。
    *   创建 `services/apiClient.js`，统一封装所有 `axios` API 请求。
3.  **组件化重构**:
    *   从 `StatCard` 开始，逐一将上表中的 UI 元素重构为独立的组件。
    *   为每个组件创建独立的 JS 和 CSS 文件，并遵循 BEM 命名规范。
4.  **页面重构**:
    *   重构 `dashboard` 页面，引入重构后的组件，并迁移页面特定的逻辑到 `pages/dashboard/index.js`。
    *   按顺序重构 `settings`, `positions` 等其他页面。
5.  **清理工作**:
    *   在所有页面重构完成后，删除旧的 CSS 和 JS 文件。
    *   更新 `base.html` 以引入新的构建产物。

## 5. 附录

### 5.1 推荐工具

*   **Prettier**: 用于代码格式化。
*   **ESLint**: 用于 JavaScript 代码质量检查。
*   **Stylelint**: 用于 CSS 代码质量检查和规范执行。
*   **Vite**: 推荐作为现代化的前端构建工具，提供快速的开发体验和高效的打包。

### 5.2 Prettier 配置示例 (`.prettierrc`)

```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "always"
}