#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统日志配置模块

此模块负责配置系统的日志记录，包括：
1. 日志格式设置
2. 日志级别配置
3. 日志文件轮转
4. 不同模块的日志分离
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger as loguru_logger

from config.settings import Settings


class LoggingConfig:
    """日志配置类
    
    负责配置和管理系统的日志记录。
    """
    
    def __init__(self, settings: Settings):
        """初始化日志配置
        
        Args:
            settings: 系统配置实例
        """
        self.settings = settings
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志文件路径
        self.system_log_file = self.log_dir / "system.log"
        self.trading_log_file = self.log_dir / "trading.log"
        self.ai_decisions_log_file = self.log_dir / "ai_decisions.log"
        self.error_log_file = self.log_dir / "error.log"
    
    def setup_logging(self) -> None:
        """设置日志系统"""
        # 移除默认的loguru处理器
        loguru_logger.remove()
        
        # 设置控制台日志
        self._setup_console_logging()
        
        # 设置文件日志
        self._setup_file_logging()
        
        # 设置Python标准日志库的集成
        self._setup_stdlib_integration()
    
    def _setup_console_logging(self) -> None:
        """设置控制台日志"""
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        # 根据环境设置不同的日志级别
        if self.settings.is_development:
            console_level = "DEBUG"
        elif self.settings.is_testing:
            console_level = "WARNING"
        else:
            console_level = self.settings.log_level
        
        loguru_logger.add(
            sys.stdout,
            format=console_format,
            level=console_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    def _setup_file_logging(self) -> None:
        """设置文件日志"""
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
        
        # 系统日志
        loguru_logger.add(
            self.system_log_file,
            format=file_format,
            level=self.settings.log_level,
            rotation=self.settings.log_file_max_size,
            retention=self.settings.log_file_backup_count,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        # 交易日志（只记录交易相关的信息）
        loguru_logger.add(
            self.trading_log_file,
            format=file_format,
            level="INFO",
            rotation=self.settings.log_file_max_size,
            retention=self.settings.log_file_backup_count,
            compression="zip",
            filter=lambda record: "trading" in record["name"].lower() or "exchange" in record["name"].lower(),
            encoding="utf-8"
        )
        
        # AI决策日志（只记录AI相关的信息）
        loguru_logger.add(
            self.ai_decisions_log_file,
            format=file_format,
            level="INFO",
            rotation=self.settings.log_file_max_size,
            retention=self.settings.log_file_backup_count,
            compression="zip",
            filter=lambda record: "ai" in record["name"].lower() or "deepseek" in record["name"].lower(),
            encoding="utf-8"
        )
        
        # 错误日志（只记录ERROR和CRITICAL级别）
        loguru_logger.add(
            self.error_log_file,
            format=file_format,
            level="ERROR",
            rotation=self.settings.log_file_max_size,
            retention=self.settings.log_file_backup_count * 2,  # 错误日志保留更久
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
    
    def _setup_stdlib_integration(self) -> None:
        """设置Python标准日志库的集成"""
        # 创建一个处理器，将标准日志库的日志转发到loguru
        class InterceptHandler(logging.Handler):
            def emit(self, record):
                # 获取对应的loguru级别
                try:
                    level = loguru_logger.level(record.levelname).name
                except ValueError:
                    level = record.levelno
                
                # 查找调用者
                frame, depth = logging.currentframe(), 2
                while frame.f_code.co_filename == logging.__file__:
                    frame = frame.f_back
                    depth += 1
                
                loguru_logger.opt(depth=depth, exception=record.exc_info).log(
                    level, record.getMessage()
                )
        
        # 配置标准日志库
        logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
        
        # 设置第三方库的日志级别
        self._configure_third_party_loggers()
    
    def _configure_third_party_loggers(self) -> None:
        """配置第三方库的日志级别"""
        # 设置第三方库的日志级别，避免过多的调试信息
        third_party_loggers = {
            "ccxt": "WARNING",
            "httpx": "WARNING",
            "urllib3": "WARNING",
            "requests": "WARNING",
            "asyncio": "WARNING",
            "uvicorn": "INFO",
            "fastapi": "INFO",
        }
        
        for logger_name, level in third_party_loggers.items():
            logging.getLogger(logger_name).setLevel(getattr(logging, level))
    
    def get_logger_config(self) -> Dict[str, Any]:
        """获取日志配置信息
        
        Returns:
            Dict[str, Any]: 日志配置信息
        """
        return {
            "log_level": self.settings.log_level,
            "log_dir": str(self.log_dir),
            "system_log_file": str(self.system_log_file),
            "trading_log_file": str(self.trading_log_file),
            "ai_decisions_log_file": str(self.ai_decisions_log_file),
            "error_log_file": str(self.error_log_file),
            "max_file_size": self.settings.log_file_max_size,
            "backup_count": self.settings.log_file_backup_count,
        }


def setup_logging(settings: Optional[Settings] = None) -> LoggingConfig:
    """设置日志系统
    
    Args:
        settings: 系统配置实例，如果为None则使用默认配置
    
    Returns:
        LoggingConfig: 日志配置实例
    """
    if settings is None:
        from config.settings import get_settings
        settings = get_settings()
    
    logging_config = LoggingConfig(settings)
    logging_config.setup_logging()
    
    # 记录日志系统启动信息
    loguru_logger.info("日志系统初始化完成")
    loguru_logger.info(f"日志级别: {settings.log_level}")
    loguru_logger.info(f"日志目录: {logging_config.log_dir}")
    
    return logging_config


def get_structured_logger(name: str) -> Any:
    """获取结构化日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        Any: loguru日志记录器实例
    """
    return loguru_logger.bind(name=name)


# 预定义的日志记录器
def get_trading_logger():
    """获取交易日志记录器"""
    return get_structured_logger("trading")


def get_ai_logger():
    """获取AI日志记录器"""
    return get_structured_logger("ai")


def get_system_logger():
    """获取系统日志记录器"""
    return get_structured_logger("system")


def get_error_logger():
    """获取错误日志记录器"""
    return get_structured_logger("error")


# 日志装饰器
def log_function_call(logger_func=None):
    """函数调用日志装饰器
    
    Args:
        logger_func: 日志记录器函数，默认使用系统日志记录器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = logger_func() if logger_func else get_system_logger()
            logger.debug(f"调用函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        
        return wrapper
    return decorator


def log_async_function_call(logger_func=None):
    """异步函数调用日志装饰器
    
    Args:
        logger_func: 日志记录器函数，默认使用系统日志记录器
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            logger = logger_func() if logger_func else get_system_logger()
            logger.debug(f"调用异步函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            
            try:
                result = await func(*args, **kwargs)
                logger.debug(f"异步函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
                raise
        
        return wrapper
    return decorator
