#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易协调器真实集成测试

测试完整的交易流程协调功能。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.trading_coordinator import TradingCoordinator, TradingConfig, SystemState
from src.ai.deepseek_client import DeepSeekConfig
from src.data.models import ExchangeConfig, RiskParameters


async def test_trading_coordinator_real():
    """测试交易协调器"""
    print("=== 交易协调器真实集成测试 ===")
    print("⚠️  注意：这将使用真实的API进行完整的交易流程测试")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建DeepSeek配置
    deepseek_config = DeepSeekConfig(
        api_key="***********************************",
        model="deepseek-chat",
        temperature=0.3,
        max_tokens=1000
    )
    
    # 创建风险参数
    risk_params = RiskParameters(
        max_leverage=10,
        max_position_ratio=0.3,  # 降低到30%以减少风险
        min_balance_threshold=100.0,
        opening_confidence_threshold=75,  # 提高置信度要求
        position_confidence_threshold=65
    )
    
    # 创建交易配置
    trading_config = TradingConfig(
        symbols=["BTC/USDT:USDT", "ETH/USDT:USDT"],  # 只测试2个主要交易对
        timeframes=["5m", "15m", "1h"],  # 使用较少的时间周期
        analysis_interval=120,  # 2分钟分析一次（测试用）
        max_positions=2,  # 最多2个持仓
        enable_opening=False,  # 测试时禁用开仓，只测试持仓管理
        enable_position_management=True
    )
    
    # 创建客户端和协调器
    exchange_client = ExchangeClient(exchange_config)
    coordinator = None
    
    try:
        # 1. 创建交易协调器
        print("\n1. 创建交易协调器...")
        coordinator = TradingCoordinator(
            exchange_client=exchange_client,
            deepseek_config=deepseek_config,
            risk_params=risk_params,
            trading_config=trading_config
        )
        
        print("✅ 交易协调器创建成功")
        
        # 2. 测试系统状态
        print("\n2. 测试系统状态...")
        initial_status = coordinator.get_system_status()
        print(f"初始状态: {initial_status.state.value}")
        print(f"运行时间: {initial_status.uptime:.2f}秒")
        print(f"总分析次数: {initial_status.total_analyses}")
        print(f"活跃持仓: {initial_status.active_positions}")
        
        assert initial_status.state == SystemState.STOPPED
        print("✅ 初始状态检查通过")
        
        # 3. 启动交易协调器
        print("\n3. 启动交易协调器...")
        await coordinator.start()
        
        # 检查启动后状态
        running_status = coordinator.get_system_status()
        print(f"启动后状态: {running_status.state.value}")
        print(f"活跃持仓: {running_status.active_positions}")
        
        assert running_status.state == SystemState.RUNNING
        print("✅ 交易协调器启动成功")
        
        # 4. 等待几个分析周期
        print("\n4. 等待分析周期执行...")
        print("等待5分钟，观察系统运行...")
        
        for i in range(5):  # 等待5分钟
            await asyncio.sleep(60)  # 每分钟检查一次
            
            status = coordinator.get_system_status()
            print(f"第{i+1}分钟状态:")
            print(f"  系统状态: {status.state.value}")
            print(f"  运行时间: {status.uptime:.0f}秒")
            print(f"  总分析次数: {status.total_analyses}")
            print(f"  成功分析: {status.successful_analyses}")
            print(f"  失败分析: {status.failed_analyses}")
            print(f"  活跃持仓: {status.active_positions}")
            
            if status.last_error:
                print(f"  最后错误: {status.last_error}")
            
            # 检查系统是否正常运行
            if status.state != SystemState.RUNNING:
                print(f"⚠️ 系统状态异常: {status.state.value}")
                break
        
        final_status = coordinator.get_system_status()
        print(f"\n最终状态:")
        print(f"  总分析次数: {final_status.total_analyses}")
        print(f"  成功率: {final_status.successful_analyses/final_status.total_analyses*100:.1f}%" if final_status.total_analyses > 0 else "  成功率: N/A")
        
        # 验证系统正常运行
        assert final_status.total_analyses > 0, "系统应该执行了至少一次分析"
        assert final_status.successful_analyses > 0, "应该有成功的分析"
        
        print("✅ 分析周期执行正常")
        
        # 5. 测试暂停和恢复
        print("\n5. 测试暂停和恢复...")
        
        await coordinator.pause()
        paused_status = coordinator.get_system_status()
        print(f"暂停后状态: {paused_status.state.value}")
        assert paused_status.state == SystemState.PAUSED
        
        await asyncio.sleep(2)  # 等待2秒
        
        await coordinator.resume()
        resumed_status = coordinator.get_system_status()
        print(f"恢复后状态: {resumed_status.state.value}")
        assert resumed_status.state == SystemState.RUNNING
        
        print("✅ 暂停和恢复功能正常")
        
        # 6. 测试配置更新
        print("\n6. 测试配置更新...")
        
        # 更新交易配置
        new_trading_config = TradingConfig(
            symbols=["BTC/USDT:USDT"],  # 减少到1个交易对
            timeframes=["15m", "1h"],
            analysis_interval=180,  # 3分钟
            max_positions=1,
            enable_opening=False,
            enable_position_management=True
        )
        
        coordinator.update_trading_config(new_trading_config)
        print("✅ 交易配置更新成功")
        
        # 更新风险参数
        new_risk_params = RiskParameters(
            max_leverage=5,  # 降低杠杆
            max_position_ratio=0.2,  # 降低仓位比例
            min_balance_threshold=200.0,
            opening_confidence_threshold=80,
            position_confidence_threshold=70
        )
        
        coordinator.update_risk_parameters(new_risk_params)
        print("✅ 风险参数更新成功")
        
        # 7. 测试市场数据质量
        print("\n7. 测试市场数据质量...")
        
        data_quality = coordinator.market_data_engine.get_data_quality_report()
        print(f"数据质量报告:")
        print(f"  总交易对: {data_quality.get('total_symbols', 0)}")
        print(f"  总时间周期: {data_quality.get('total_timeframes', 0)}")
        print(f"  数据充足率: {data_quality.get('sufficiency_ratio', 0):.2%}")
        print(f"  平均质量分数: {data_quality.get('average_quality_score', 0):.2f}")
        print(f"  更新成功率: {data_quality.get('update_stats', {}).get('success_rate', 0):.2%}")
        
        # 验证数据质量
        assert data_quality.get('sufficiency_ratio', 0) > 0.5, "数据充足率应该超过50%"
        assert data_quality.get('average_quality_score', 0) > 0.7, "平均质量分数应该超过0.7"
        
        print("✅ 市场数据质量检查通过")
        
        # 8. 测试风险管理集成
        print("\n8. 测试风险管理集成...")
        
        risk_summary = coordinator.risk_manager.get_risk_summary(
            coordinator.current_positions,
            coordinator.current_balance
        )
        
        print(f"风险摘要:")
        print(f"  总余额: {risk_summary.get('total_balance', 0):.2f}")
        print(f"  总敞口: {risk_summary.get('total_exposure', 0):.2f}")
        print(f"  敞口比率: {risk_summary.get('exposure_ratio', 0):.2%}")
        print(f"  投资组合风险等级: {risk_summary.get('portfolio_risk_level', 'unknown')}")
        print(f"  持仓数量: {risk_summary.get('position_count', 0)}")
        print(f"  高风险持仓: {risk_summary.get('high_risk_positions', 0)}")
        
        print("✅ 风险管理集成正常")
        
        print("\n🎉 所有交易协调器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 交易协调器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if coordinator:
            try:
                await coordinator.stop()
                print("\n🧹 交易协调器已停止")
            except Exception as e:
                print(f"停止协调器时出错: {e}")
        
        print("🧹 资源清理完成")


if __name__ == "__main__":
    success = asyncio.run(test_trading_coordinator_real())
    if success:
        print("\n✅ 交易协调器集成测试全部通过！")
    else:
        print("\n❌ 交易协调器集成测试失败！")
        sys.exit(1)
