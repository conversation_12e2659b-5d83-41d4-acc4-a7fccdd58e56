#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统风险管理系统

此模块负责进行基础的风险控制，确保不超过用户设置的参数限制，包括：
1. 开仓风险验证
2. 杠杆倍数控制
3. 仓位大小限制
4. 余额充足性检查
5. 风险参数验证
6. 紧急风控措施
"""

import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal, ROUND_DOWN

from src.data.models import Position, AccountBalance, RiskParameters
from src.utils.logger import get_logger, log_execution_time
from src.utils.exceptions import RiskViolationError, InsufficientBalanceError
from src.utils.validators import validate_positive_number, validate_percentage

logger = get_logger(__name__)


@dataclass
class RiskCheckResult:
    """风险检查结果"""
    passed: bool                    # 是否通过检查
    risk_level: str                # 风险等级: "low", "medium", "high", "critical"
    warnings: List[str]            # 警告信息
    errors: List[str]              # 错误信息
    suggested_adjustments: Dict[str, Any]  # 建议调整


@dataclass
class PositionRisk:
    """持仓风险信息"""
    symbol: str                    # 交易对
    current_exposure: float        # 当前敞口
    max_allowed_exposure: float    # 最大允许敞口
    risk_percentage: float         # 风险百分比
    margin_usage: float            # 保证金使用率
    leverage_risk: str             # 杠杆风险等级


class RiskManager:
    """风险管理系统类
    
    负责进行基础的风险控制，确保不超过用户设置的参数限制。
    """
    
    def __init__(self, risk_params: RiskParameters):
        """初始化风险管理系统
        
        Args:
            risk_params: 风险参数配置
        """
        self.risk_params = risk_params
        
        # 风险等级阈值
        self.risk_thresholds = {
            "low": 0.3,      # 30%以下为低风险
            "medium": 0.6,   # 30-60%为中等风险
            "high": 0.8,     # 60-80%为高风险
            "critical": 1.0  # 80%以上为严重风险
        }
        
        # 紧急风控标志
        self.emergency_mode = False
        self.last_risk_check = 0

        # 缓存合约规格信息
        self._contract_specs = {}
    
    def update_risk_parameters(self, new_params: RiskParameters):
        """更新风险参数
        
        Args:
            new_params: 新的风险参数
        """
        # 验证新参数
        validation_result = self.validate_risk_parameters(new_params.to_dict())
        if not validation_result[0]:
            raise ValueError(f"风险参数验证失败: {validation_result[1]}")
        
        self.risk_params = new_params
        logger.info("风险参数已更新")
    
    @log_execution_time("开仓风险验证")
    def validate_opening_order(self, symbol: str, side: str, amount: float, 
                             leverage: int, price: float, balance: Dict[str, AccountBalance]) -> RiskCheckResult:
        """验证开仓订单风险
        
        Args:
            symbol: 交易对符号
            side: 交易方向 (buy/sell)
            amount: 交易数量
            leverage: 杠杆倍数
            price: 交易价格
            balance: 账户余额
        
        Returns:
            RiskCheckResult: 风险检查结果
        """
        try:
            warnings = []
            errors = []
            suggested_adjustments = {}
            
            # 1. 验证杠杆倍数
            if not self.validate_leverage(leverage):
                errors.append(f"杠杆倍数 {leverage} 超过最大限制 {self.risk_params.max_leverage}")
                suggested_adjustments["leverage"] = self.risk_params.max_leverage
            
            # 2. 计算所需保证金
            position_value = amount * price
            required_margin = position_value / leverage
            
            # 3. 验证余额充足性
            base_currency = "USDT"  # 假设使用USDT作为保证金
            if base_currency not in balance:
                errors.append(f"账户中没有 {base_currency} 余额")
            else:
                available_balance = balance[base_currency].available
                if not self.validate_available_balance(required_margin, available_balance):
                    errors.append(f"余额不足：需要 {required_margin:.2f} {base_currency}，可用 {available_balance:.2f}")
                    max_amount = (available_balance * leverage) / price
                    suggested_adjustments["amount"] = max_amount * 0.9  # 留10%缓冲
            
            # 4. 验证仓位大小
            total_balance = balance[base_currency].total if base_currency in balance else 0
            if not self.validate_position_size(position_value, total_balance):
                max_position_value = total_balance * self.risk_params.max_position_ratio
                max_amount = max_position_value / price
                errors.append(f"仓位过大：当前 {position_value:.2f}，最大允许 {max_position_value:.2f}")
                suggested_adjustments["amount"] = max_amount
            
            # 5. 计算风险等级
            risk_ratio = position_value / total_balance if total_balance > 0 else 1.0
            risk_level = self._calculate_risk_level(risk_ratio)
            
            # 6. 风险警告
            if risk_level == "high":
                warnings.append("高风险交易，建议降低仓位或杠杆")
            elif risk_level == "critical":
                errors.append("风险过高，禁止开仓")
            
            # 7. 杠杆风险检查
            if leverage > 10:
                warnings.append(f"高杠杆交易 ({leverage}x)，请注意风险")
            if leverage > 20:
                errors.append("杠杆过高，超出安全范围")
            
            # 8. 市场条件检查
            if self.emergency_mode:
                errors.append("系统处于紧急风控模式，暂停开仓")
            
            passed = len(errors) == 0
            
            result = RiskCheckResult(
                passed=passed,
                risk_level=risk_level,
                warnings=warnings,
                errors=errors,
                suggested_adjustments=suggested_adjustments
            )
            
            logger.info(f"开仓风险检查完成: {symbol} - {'通过' if passed else '失败'} (风险等级: {risk_level})")
            return result
            
        except Exception as e:
            logger.error(f"开仓风险验证失败: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level="critical",
                warnings=[],
                errors=[f"风险验证异常: {e}"],
                suggested_adjustments={}
            )
    
    def validate_leverage(self, leverage: int) -> bool:
        """验证杠杆倍数
        
        Args:
            leverage: 杠杆倍数
        
        Returns:
            bool: 是否有效
        """
        return 1 <= leverage <= self.risk_params.max_leverage
    
    def validate_position_size(self, position_value: float, total_balance: float) -> bool:
        """验证仓位大小
        
        Args:
            position_value: 仓位价值
            total_balance: 总余额
        
        Returns:
            bool: 是否有效
        """
        if total_balance <= 0:
            return False
        
        position_ratio = position_value / total_balance
        return position_ratio <= self.risk_params.max_position_ratio
    
    def validate_available_balance(self, required_margin: float, available_balance: float) -> bool:
        """验证可用余额
        
        Args:
            required_margin: 所需保证金
            available_balance: 可用余额
        
        Returns:
            bool: 是否充足
        """
        # 保留一定的缓冲余额
        buffer_ratio = 0.05  # 5%缓冲
        required_with_buffer = required_margin * (1 + buffer_ratio)
        
        return available_balance >= required_with_buffer
    
    def calculate_max_position_size(self, price: float, leverage: int, 
                                  available_balance: float) -> float:
        """计算最大仓位大小
        
        Args:
            price: 交易价格
            leverage: 杠杆倍数
            available_balance: 可用余额
        
        Returns:
            float: 最大仓位数量
        """
        try:
            # 考虑最大仓位比例限制
            max_position_value = available_balance * self.risk_params.max_position_ratio * leverage
            max_amount = max_position_value / price
            
            # 保留安全缓冲
            safe_amount = max_amount * 0.95  # 95%安全系数
            
            return safe_amount
            
        except Exception as e:
            logger.error(f"计算最大仓位大小失败: {e}")
            return 0.0
    
    def calculate_position_risk(self, positions: List[Position],
                              balance: Dict[str, AccountBalance],
                              exchange_client) -> List[PositionRisk]:
        """计算持仓风险
        
        Args:
            positions: 持仓列表
            balance: 账户余额
        
        Returns:
            List[PositionRisk]: 持仓风险列表
        """
        try:
            position_risks = []
            total_balance = sum(bal.total for bal in balance.values())
            
            for position in positions:
                # 计算当前敞口（使用正确的方法）
                current_exposure = self._calculate_position_exposure(position, exchange_client)
                
                # 计算最大允许敞口
                max_allowed_exposure = total_balance * self.risk_params.max_position_ratio
                
                # 计算风险百分比
                risk_percentage = current_exposure / total_balance if total_balance > 0 else 0
                
                # 计算保证金使用率
                margin_usage = position.margin_used / total_balance if total_balance > 0 else 0
                
                # 评估杠杆风险
                leverage_risk = self._evaluate_leverage_risk(position.leverage)
                
                position_risk = PositionRisk(
                    symbol=position.symbol,
                    current_exposure=current_exposure,
                    max_allowed_exposure=max_allowed_exposure,
                    risk_percentage=risk_percentage,
                    margin_usage=margin_usage,
                    leverage_risk=leverage_risk
                )
                
                position_risks.append(position_risk)
            
            return position_risks
            
        except Exception as e:
            logger.error(f"计算持仓风险失败: {e}")
            return []
    
    def check_portfolio_risk(self, positions: List[Position],
                           balance: Dict[str, AccountBalance],
                           exchange_client) -> RiskCheckResult:
        """检查投资组合整体风险
        
        Args:
            positions: 持仓列表
            balance: 账户余额
        
        Returns:
            RiskCheckResult: 风险检查结果
        """
        try:
            warnings = []
            errors = []
            
            total_balance = sum(bal.total for bal in balance.values())

            # 计算总敞口，使用正确的永续合约敞口计算方法
            total_exposure = 0
            valid_positions = []

            for pos in positions:
                # 使用正确的敞口计算方法
                position_exposure = self._calculate_position_exposure(pos, exchange_client)

                total_exposure += position_exposure
                valid_positions.append(pos)

                # 记录较大的敞口
                if position_exposure > total_balance * 2:  # 敞口超过总余额2倍
                    logger.warning(f"检测到较大的持仓敞口: {pos.symbol} - {position_exposure:,.2f} (余额: {total_balance:,.2f})")

            # 使用验证后的持仓列表
            positions = valid_positions

            total_margin_used = sum(pos.margin_used for pos in positions)
            
            # 1. 总敞口检查
            exposure_ratio = total_exposure / total_balance if total_balance > 0 else 0
            if exposure_ratio > 0.8:
                errors.append(f"总敞口过高: {exposure_ratio:.2%}")
                # 如果敞口过高且未启用紧急模式，则启用
                if not self.emergency_mode:
                    self.enable_emergency_mode(f"总敞口过高: {exposure_ratio:.2%}")
            elif exposure_ratio > 0.6:
                warnings.append(f"总敞口较高: {exposure_ratio:.2%}")
            else:
                # 如果敞口正常且处于紧急模式，考虑退出紧急模式
                if self.emergency_mode and exposure_ratio < 0.5:
                    self.disable_emergency_mode()
                    logger.info(f"敞口恢复正常 ({exposure_ratio:.2%})，退出紧急风控模式")
            
            # 2. 保证金使用率检查
            margin_usage_ratio = total_margin_used / total_balance if total_balance > 0 else 0
            if margin_usage_ratio > 0.9:
                errors.append(f"保证金使用率过高: {margin_usage_ratio:.2%}")
            elif margin_usage_ratio > 0.7:
                warnings.append(f"保证金使用率较高: {margin_usage_ratio:.2%}")
            
            # 3. 持仓集中度检查
            concentration_risk = self._check_concentration_risk(positions, total_exposure)
            warnings.extend(concentration_risk)
            
            # 4. 计算整体风险等级
            risk_level = self._calculate_risk_level(max(exposure_ratio, margin_usage_ratio))
            
            passed = len(errors) == 0
            
            return RiskCheckResult(
                passed=passed,
                risk_level=risk_level,
                warnings=warnings,
                errors=errors,
                suggested_adjustments={}
            )
            
        except Exception as e:
            logger.error(f"投资组合风险检查失败: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level="critical",
                warnings=[],
                errors=[f"风险检查异常: {e}"],
                suggested_adjustments={}
            )
    
    def _calculate_risk_level(self, risk_ratio: float) -> str:
        """计算风险等级
        
        Args:
            risk_ratio: 风险比率
        
        Returns:
            str: 风险等级
        """
        if risk_ratio <= self.risk_thresholds["low"]:
            return "low"
        elif risk_ratio <= self.risk_thresholds["medium"]:
            return "medium"
        elif risk_ratio <= self.risk_thresholds["high"]:
            return "high"
        else:
            return "critical"
    
    def _evaluate_leverage_risk(self, leverage: int) -> str:
        """评估杠杆风险
        
        Args:
            leverage: 杠杆倍数
        
        Returns:
            str: 杠杆风险等级
        """
        if leverage <= 3:
            return "low"
        elif leverage <= 10:
            return "medium"
        elif leverage <= 20:
            return "high"
        else:
            return "critical"
    
    def _check_concentration_risk(self, positions: List[Position], total_exposure: float) -> List[str]:
        """检查持仓集中度风险
        
        Args:
            positions: 持仓列表
            total_exposure: 总敞口
        
        Returns:
            List[str]: 集中度风险警告
        """
        warnings = []
        
        if not positions or total_exposure == 0:
            return warnings
        
        # 按交易对分组计算敞口
        symbol_exposure = {}
        for position in positions:
            exposure = position.amount * position.current_price
            if position.symbol in symbol_exposure:
                symbol_exposure[position.symbol] += exposure
            else:
                symbol_exposure[position.symbol] = exposure
        
        # 检查单一交易对集中度
        for symbol, exposure in symbol_exposure.items():
            concentration = exposure / total_exposure
            if concentration > 0.5:
                warnings.append(f"{symbol} 持仓集中度过高: {concentration:.2%}")
            elif concentration > 0.3:
                warnings.append(f"{symbol} 持仓集中度较高: {concentration:.2%}")
        
        return warnings
    
    def validate_risk_parameters(self, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证风险参数
        
        Args:
            params: 风险参数字典
        
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            # 验证最大杠杆
            if "max_leverage" in params:
                if not isinstance(params["max_leverage"], int) or params["max_leverage"] < 1 or params["max_leverage"] > 100:
                    errors.append("最大杠杆必须是1-100之间的整数")
            
            # 验证最大仓位比例
            if "max_position_ratio" in params:
                if not validate_percentage(params["max_position_ratio"]):
                    errors.append("最大仓位比例必须是0-1之间的数值")
            
            # 验证最小余额阈值
            if "min_balance_threshold" in params:
                if not validate_positive_number(params["min_balance_threshold"]):
                    errors.append("最小余额阈值必须是正数")
            
            # 验证置信度阈值
            for key in ["opening_confidence_threshold", "position_confidence_threshold"]:
                if key in params:
                    value = params[key]
                    if not isinstance(value, int) or value < 0 or value > 100:
                        errors.append(f"{key} 必须是0-100之间的整数")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"验证风险参数失败: {e}")
            return False, [f"参数验证异常: {e}"]
    
    def enable_emergency_mode(self, reason: str):
        """启用紧急风控模式
        
        Args:
            reason: 启用原因
        """
        self.emergency_mode = True
        logger.warning(f"启用紧急风控模式: {reason}")
    
    def disable_emergency_mode(self):
        """禁用紧急风控模式"""
        self.emergency_mode = False
        logger.info("禁用紧急风控模式")
    
    def get_risk_summary(self, positions: List[Position],
                        balance: Dict[str, AccountBalance],
                        exchange_client) -> Dict[str, Any]:
        """获取风险摘要
        
        Args:
            positions: 持仓列表
            balance: 账户余额
        
        Returns:
            Dict[str, Any]: 风险摘要
        """
        try:
            total_balance = sum(bal.total for bal in balance.values())

            # 使用正确的敞口计算方法
            total_exposure = 0
            valid_positions = []

            for pos in positions:
                # 使用正确的敞口计算方法
                position_exposure = self._calculate_position_exposure(pos, exchange_client)

                total_exposure += position_exposure
                valid_positions.append(pos)

            total_margin_used = sum(pos.margin_used for pos in valid_positions)
            
            portfolio_risk = self.check_portfolio_risk(valid_positions, balance, exchange_client)
            position_risks = self.calculate_position_risk(valid_positions, balance, exchange_client)
            
            return {
                "total_balance": total_balance,
                "total_exposure": total_exposure,
                "total_margin_used": total_margin_used,
                "exposure_ratio": total_exposure / total_balance if total_balance > 0 else 0,
                "margin_usage_ratio": total_margin_used / total_balance if total_balance > 0 else 0,
                "portfolio_risk_level": portfolio_risk.risk_level,
                "portfolio_warnings": portfolio_risk.warnings,
                "portfolio_errors": portfolio_risk.errors,
                "position_count": len(valid_positions),
                "high_risk_positions": len([pr for pr in position_risks if pr.risk_percentage > 0.3]),
                "emergency_mode": self.emergency_mode,
                "last_check_time": self.last_risk_check
            }
            
        except Exception as e:
            logger.error(f"获取风险摘要失败: {e}")
            return {"error": str(e)}

    def _get_contract_spec(self, symbol: str, exchange_client) -> dict:
        """获取合约规格信息

        Args:
            symbol: 交易对符号
            exchange_client: 交易所客户端

        Returns:
            dict: 合约规格信息
        """
        try:
            # 检查缓存
            if symbol in self._contract_specs:
                return self._contract_specs[symbol]

            # 获取市场信息
            markets = exchange_client.fetch_markets()

            for market in markets:
                if market.get('symbol') == symbol:
                    contract_spec = {
                        'symbol': symbol,
                        'contractSize': market.get('contractSize', 1.0),
                        'contractValue': market.get('contractValue'),
                        'info': market.get('info', {})
                    }

                    # 缓存结果
                    self._contract_specs[symbol] = contract_spec
                    return contract_spec

            # 如果没找到，返回默认值
            default_spec = {
                'symbol': symbol,
                'contractSize': 1.0,
                'contractValue': None,
                'info': {}
            }
            self._contract_specs[symbol] = default_spec
            return default_spec

        except Exception as e:
            logger.error(f"获取合约规格失败 {symbol}: {e}")
            # 返回默认值
            default_spec = {
                'symbol': symbol,
                'contractSize': 1.0,
                'contractValue': None,
                'info': {}
            }
            return default_spec

    def _calculate_position_exposure(self, position, exchange_client) -> float:
        """计算持仓的正确敞口

        根据永续合约敞口计算公式：
        敞口 = 合约数量 × 合约面值 × 标记价格
        验证：敞口 = 保证金 × 杠杆倍数

        Args:
            position: 持仓对象
            exchange_client: 交易所客户端

        Returns:
            float: 持仓敞口
        """
        try:
            # 获取合约规格
            contract_spec = self._get_contract_spec(position.symbol, exchange_client)
            contract_size = contract_spec.get('contractSize', 1.0)

            # 正确的敞口计算：合约数量 × 合约面值 × 标记价格
            exposure = abs(position.amount) * contract_size * position.current_price

            # 验证：保证金 × 杠杆倍数
            margin_exposure = position.margin_used * position.leverage

            logger.debug(f"敞口计算 {position.symbol}:")
            logger.debug(f"  合约数量: {position.amount}")
            logger.debug(f"  合约面值: {contract_size}")
            logger.debug(f"  标记价格: {position.current_price}")
            logger.debug(f"  计算敞口: {exposure:,.2f}")
            logger.debug(f"  验证敞口: {margin_exposure:,.2f}")

            # 检查计算结果与验证结果的一致性
            if abs(exposure - margin_exposure) / max(exposure, margin_exposure) > 0.1:  # 10%误差
                logger.warning(f"敞口计算验证差异 {position.symbol}:")
                logger.warning(f"  计算结果: {exposure:,.2f} USDT")
                logger.warning(f"  验证结果: {margin_exposure:,.2f} USDT")
                logger.warning(f"  合约面值: {contract_size}")
                logger.warning(f"  差异: {abs(exposure - margin_exposure):,.2f} USDT")

            # 返回计算结果
            return exposure

        except Exception as e:
            logger.error(f"计算持仓敞口失败 {position.symbol}: {e}")
            # 备选方案：使用保证金×杠杆
            return position.margin_used * position.leverage


