#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统数据库管理模块

此模块负责管理SQLite数据库的所有操作，包括：
1. 数据库连接和初始化
2. 表结构创建和维护
3. 配置数据的CRUD操作
4. 数据加密存储
5. 数据库备份和恢复
"""

import sqlite3
import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import threading

from src.data.encryption import APIKeyManager, create_encryption_manager
from src.data.models import ExchangeConfig, TradingParameters, RiskParameters, AIConfig
from src.utils.exceptions import DatabaseError, DataValidationError
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器类
    
    负责管理SQLite数据库的所有操作。
    """
    
    def __init__(self, db_path: str = "data/trading_system.db", encryption_key: Optional[str] = None):
        """初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
            encryption_key: 加密密钥
        """
        self.db_path = Path(db_path)
        self.encryption_key = encryption_key
        self._connection = None
        self._lock = threading.Lock()
        self._initialized = False

        # 初始化加密管理器
        self.encryption_manager = create_encryption_manager(encryption_key)
        self.api_key_manager = APIKeyManager(self.encryption_manager)

        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 自动初始化数据库
        try:
            self.init_database()
        except Exception as e:
            logger.warning(f"数据库自动初始化失败: {e}")
            # 不抛出异常，允许后续手动初始化
    
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接
        """
        if self._connection is None:
            try:
                self._connection = sqlite3.connect(
                    str(self.db_path),
                    check_same_thread=False,
                    timeout=30.0
                )
                self._connection.row_factory = sqlite3.Row
                self._connection.execute("PRAGMA foreign_keys = ON")
                logger.info(f"数据库连接成功: {self.db_path}")
            except Exception as e:
                logger.error(f"数据库连接失败: {e}")
                raise DatabaseError("connect", str(self.db_path), str(e))
        
        return self._connection
    
    def close_connection(self):
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            logger.info("数据库连接已关闭")
    
    def init_database(self) -> None:
        """初始化数据库表结构"""
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # 创建交易所配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS exchange_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exchange_name TEXT NOT NULL UNIQUE,
                        api_key TEXT NOT NULL,
                        secret_key TEXT NOT NULL,
                        passphrase TEXT,
                        trading_password TEXT,
                        sandbox_mode BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建交易参数表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trading_parameters (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        max_leverage INTEGER DEFAULT 10,
                        max_position_ratio REAL DEFAULT 0.5,
                        opening_confidence_threshold INTEGER DEFAULT 70,
                        position_confidence_threshold INTEGER DEFAULT 60,
                        default_stop_loss_percentage REAL DEFAULT 0.05,
                        default_take_profit_percentage REAL DEFAULT 0.1,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建选择的交易对表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS selected_symbols (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL UNIQUE,
                        is_active BOOLEAN DEFAULT TRUE,
                        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 创建AI配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ai_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        deepseek_api_key TEXT NOT NULL,
                        analysis_interval INTEGER DEFAULT 300,
                        lookback_period INTEGER DEFAULT 24,
                        enable_opening_engine BOOLEAN DEFAULT TRUE,
                        enable_position_engine BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 创建系统日志表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level TEXT NOT NULL,
                        module TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建AI决策记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ai_decisions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        engine_type TEXT NOT NULL,
                        action TEXT NOT NULL,
                        confidence INTEGER NOT NULL,
                        reasoning TEXT NOT NULL,
                        additional_data TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建交易记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trading_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        amount REAL NOT NULL,
                        price REAL NOT NULL,
                        leverage INTEGER DEFAULT 1,
                        pnl REAL DEFAULT 0,
                        status TEXT NOT NULL,
                        order_id TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 插入默认交易参数（如果不存在）
                cursor.execute("SELECT COUNT(*) FROM trading_parameters")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO trading_parameters 
                        (max_leverage, max_position_ratio, opening_confidence_threshold, 
                         position_confidence_threshold, default_stop_loss_percentage, 
                         default_take_profit_percentage)
                        VALUES (10, 0.5, 70, 60, 0.05, 0.1)
                    """)
                
                conn.commit()
                self._initialized = True
                logger.info("数据库初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise DatabaseError("init", "database", str(e))
    
    def save_exchange_config(self, config: ExchangeConfig) -> bool:
        """保存交易所配置
        
        Args:
            config: 交易所配置对象
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                # 使用REPLACE语句实现插入或更新（明文存储）
                cursor.execute("""
                    REPLACE INTO exchange_config
                    (exchange_name, api_key, secret_key, passphrase, trading_password, sandbox_mode, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    config.exchange_name,
                    config.api_key,
                    config.secret_key,
                    config.passphrase,
                    config.trading_password,
                    config.sandbox_mode
                ))

                conn.commit()
                logger.info(f"交易所配置保存成功: {config.exchange_name}")
                return True
                
        except Exception as e:
            logger.error(f"保存交易所配置失败: {e}")
            raise DatabaseError("save", "exchange_config", str(e))
    
    def load_exchange_config(self, exchange_name: Optional[str] = None) -> Optional[ExchangeConfig]:
        """加载交易所配置
        
        Args:
            exchange_name: 交易所名称，如果为None则加载最新的配置
        
        Returns:
            Optional[ExchangeConfig]: 交易所配置对象
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                if exchange_name:
                    cursor.execute(
                        "SELECT * FROM exchange_config WHERE exchange_name = ?",
                        (exchange_name,)
                    )
                else:
                    cursor.execute(
                        "SELECT * FROM exchange_config ORDER BY updated_at DESC LIMIT 1"
                    )
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                # 构建配置字典（明文读取）
                config_dict = {
                    "exchange_name": row["exchange_name"],
                    "api_key": row["api_key"],
                    "secret_key": row["secret_key"],
                    "passphrase": row["passphrase"],
                    "trading_password": row["trading_password"] if "trading_password" in row.keys() else None,
                    "sandbox_mode": bool(row["sandbox_mode"])
                }

                logger.info(f"交易所配置加载成功: {config_dict['exchange_name']}")
                return ExchangeConfig.from_dict(config_dict)
                
        except Exception as e:
            logger.error(f"加载交易所配置失败: {e}")
            raise DatabaseError("load", "exchange_config", str(e))
    
    def save_trading_parameters(self, params: TradingParameters) -> bool:
        """保存交易参数
        
        Args:
            params: 交易参数对象
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # 更新交易参数（假设只有一条记录）
                cursor.execute("""
                    UPDATE trading_parameters SET
                        max_leverage = ?,
                        max_position_ratio = ?,
                        opening_confidence_threshold = ?,
                        position_confidence_threshold = ?,
                        default_stop_loss_percentage = ?,
                        default_take_profit_percentage = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = 1
                """, (
                    params.max_leverage,
                    params.max_position_ratio,
                    params.opening_confidence_threshold,
                    params.position_confidence_threshold,
                    params.default_stop_loss_percentage,
                    params.default_take_profit_percentage
                ))
                
                conn.commit()
                logger.info("交易参数保存成功")
                return True
                
        except Exception as e:
            logger.error(f"保存交易参数失败: {e}")
            raise DatabaseError("save", "trading_parameters", str(e))

    def load_trading_parameters(self) -> dict:
        """加载交易参数

        Returns:
            dict: 交易参数字典
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute("SELECT * FROM trading_parameters ORDER BY id DESC LIMIT 1")
                row = cursor.fetchone()

                if not row:
                    # 返回默认参数
                    logger.warning("未找到交易参数，返回默认值")
                    return {
                        "max_leverage": 10,
                        "max_position_ratio": 0.5,
                        "opening_confidence_threshold": 70,
                        "position_confidence_threshold": 60,
                        "default_stop_loss_percentage": 0.05,
                        "default_take_profit_percentage": 0.1
                    }

                params_dict = {
                    "max_leverage": row["max_leverage"],
                    "max_position_ratio": row["max_position_ratio"],
                    "opening_confidence_threshold": row["opening_confidence_threshold"],
                    "position_confidence_threshold": row["position_confidence_threshold"],
                    "default_stop_loss_percentage": row["default_stop_loss_percentage"],
                    "default_take_profit_percentage": row["default_take_profit_percentage"]
                }

                logger.info("交易参数加载成功")
                return params_dict

        except Exception as e:
            logger.error(f"加载交易参数失败: {e}")
            raise DatabaseError("load", "trading_parameters", str(e))

    def load_risk_parameters(self) -> dict:
        """加载风险参数

        Returns:
            dict: 风险参数字典
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                # 这里应该从risk_parameters表加载，但目前表可能不存在
                # 先返回默认值
                default_params = {
                    "max_leverage": 20,
                    "max_exposure_ratio": 80,
                    "min_balance_threshold": 100,
                    "max_drawdown": 20
                }

                logger.info("风险参数加载成功（使用默认值）")
                return default_params

        except Exception as e:
            logger.error(f"加载风险参数失败: {e}")
            # 返回默认值而不是抛出异常
            return {
                "max_leverage": 20,
                "max_exposure_ratio": 80,
                "min_balance_threshold": 100,
                "max_drawdown": 20
            }

    def save_selected_symbols(self, symbols: List[str]) -> bool:
        """保存选择的交易对

        Args:
            symbols: 交易对列表

        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                # 先清空现有的交易对
                cursor.execute("DELETE FROM selected_symbols")

                # 插入新的交易对
                for symbol in symbols:
                    cursor.execute(
                        "INSERT INTO selected_symbols (symbol) VALUES (?)",
                        (symbol,)
                    )

                conn.commit()
                logger.info(f"保存选择的交易对成功: {len(symbols)} 个")
                return True

        except Exception as e:
            logger.error(f"保存选择的交易对失败: {e}")
            raise DatabaseError("save", "selected_symbols", str(e))

    def load_selected_symbols(self) -> List[str]:
        """加载选择的交易对

        Returns:
            List[str]: 交易对列表
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute(
                    "SELECT symbol FROM selected_symbols WHERE is_active = TRUE ORDER BY added_at"
                )
                rows = cursor.fetchall()

                symbols = [row["symbol"] for row in rows]
                logger.info(f"加载选择的交易对成功: {len(symbols)} 个")
                return symbols

        except Exception as e:
            logger.error(f"加载选择的交易对失败: {e}")
            raise DatabaseError("load", "selected_symbols", str(e))

    def save_ai_decision(self, symbol: str, engine_type: str, action: str,
                        confidence: int, reasoning: str, additional_data: Optional[Dict] = None) -> bool:
        """保存AI决策记录

        Args:
            symbol: 交易对
            engine_type: 引擎类型
            action: AI动作
            confidence: 置信度
            reasoning: 推理说明
            additional_data: 额外数据

        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO ai_decisions
                    (symbol, engine_type, action, confidence, reasoning, additional_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    symbol,
                    engine_type,
                    action,
                    confidence,
                    reasoning,
                    json.dumps(additional_data) if additional_data else None
                ))

                conn.commit()
                logger.info(f"AI决策记录保存成功: {symbol} - {action}")
                return True

        except Exception as e:
            logger.error(f"保存AI决策记录失败: {e}")
            raise DatabaseError("save", "ai_decisions", str(e))

    def get_ai_decisions(self, symbol: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取AI决策记录

        Args:
            symbol: 交易对，如果为None则获取所有
            limit: 限制数量

        Returns:
            List[Dict[str, Any]]: AI决策记录列表
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                if symbol:
                    cursor.execute("""
                        SELECT * FROM ai_decisions
                        WHERE symbol = ?
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (symbol, limit))
                else:
                    cursor.execute("""
                        SELECT * FROM ai_decisions
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (limit,))

                rows = cursor.fetchall()
                decisions = []

                for row in rows:
                    decision = {
                        "id": row["id"],
                        "symbol": row["symbol"],
                        "engine_type": row["engine_type"],
                        "action": row["action"],
                        "confidence": row["confidence"],
                        "reasoning": row["reasoning"],
                        "additional_data": json.loads(row["additional_data"]) if row["additional_data"] else None,
                        "created_at": row["created_at"]
                    }
                    decisions.append(decision)

                logger.info(f"获取AI决策记录成功: {len(decisions)} 条")
                return decisions

        except Exception as e:
            logger.error(f"获取AI决策记录失败: {e}")
            raise DatabaseError("get", "ai_decisions", str(e))

    def save_system_log(self, level: str, module: str, message: str, details: Optional[str] = None) -> bool:
        """保存系统日志

        Args:
            level: 日志级别
            module: 模块名称
            message: 日志消息
            details: 详细信息

        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO system_logs (level, module, message, details)
                    VALUES (?, ?, ?, ?)
                """, (level, module, message, details))

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"保存系统日志失败: {e}")
            return False

    def get_system_logs(self, level: Optional[str] = None, module: Optional[str] = None,
                       limit: int = 1000) -> List[Dict[str, Any]]:
        """获取系统日志

        Args:
            level: 日志级别过滤
            module: 模块名称过滤
            limit: 限制数量

        Returns:
            List[Dict[str, Any]]: 系统日志列表
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                query = "SELECT * FROM system_logs WHERE 1=1"
                params = []

                if level:
                    query += " AND level = ?"
                    params.append(level)

                if module:
                    query += " AND module = ?"
                    params.append(module)

                query += " ORDER BY created_at DESC LIMIT ?"
                params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()

                logs = []
                for row in rows:
                    log = {
                        "id": row["id"],
                        "level": row["level"],
                        "module": row["module"],
                        "message": row["message"],
                        "details": row["details"],
                        "created_at": row["created_at"]
                    }
                    logs.append(log)

                return logs

        except Exception as e:
            logger.error(f"获取系统日志失败: {e}")
            raise DatabaseError("get", "system_logs", str(e))

    def cleanup_old_logs(self, days: int = 30) -> int:
        """清理旧日志

        Args:
            days: 保留天数

        Returns:
            int: 删除的记录数
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute("""
                    DELETE FROM system_logs
                    WHERE created_at < datetime('now', '-{} days')
                """.format(days))

                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"清理旧日志完成: 删除 {deleted_count} 条记录")
                return deleted_count

        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            raise DatabaseError("cleanup", "system_logs", str(e))

    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """备份数据库

        Args:
            backup_path: 备份文件路径，如果为None则自动生成

        Returns:
            str: 备份文件路径
        """
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"data/backups/trading_system_backup_{timestamp}.db"

            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)

            with self._lock:
                conn = self._get_connection()

                # 使用SQLite的backup API
                backup_conn = sqlite3.connect(str(backup_file))
                conn.backup(backup_conn)
                backup_conn.close()

            logger.info(f"数据库备份成功: {backup_path}")
            return str(backup_path)

        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise DatabaseError("backup", str(self.db_path), str(e))

    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库

        Args:
            backup_path: 备份文件路径

        Returns:
            bool: 恢复是否成功
        """
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")

            # 关闭当前连接
            self.close_connection()

            # 备份当前数据库（重新连接以创建备份）
            temp_manager = DatabaseManager(str(self.db_path), self.encryption_key)
            current_backup = temp_manager.backup_database()
            temp_manager.close_connection()
            logger.info(f"当前数据库已备份到: {current_backup}")

            # 复制备份文件到当前数据库位置
            import shutil
            shutil.copy2(backup_file, self.db_path)

            # 重置连接状态，强制重新连接
            self._connection = None

            logger.info(f"数据库恢复成功: {backup_path}")
            return True

        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            raise DatabaseError("restore", backup_path, str(e))

    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息

        Returns:
            Dict[str, Any]: 数据库信息
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                # 获取表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # 获取每个表的记录数
                table_counts = {}
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    table_counts[table] = cursor.fetchone()[0]

                # 获取数据库文件大小
                db_size = self.db_path.stat().st_size if self.db_path.exists() else 0

                info = {
                    "database_path": str(self.db_path),
                    "database_size": db_size,
                    "tables": tables,
                    "table_counts": table_counts,
                    "encryption_enabled": self.encryption_key is not None
                }

                return info

        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            raise DatabaseError("info", str(self.db_path), str(e))

    def save_ai_config(self, config: AIConfig) -> bool:
        """保存AI配置

        Args:
            config: AI配置对象

        Returns:
            bool: 保存是否成功
        """
        try:
            # 验证配置
            if not config.deepseek_api_key:
                raise DataValidationError("deepseek_api_key", "", "DeepSeek API密钥不能为空")

            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                # 使用REPLACE语句实现插入或更新（AI配置只有一条记录，明文存储）
                cursor.execute("""
                    REPLACE INTO ai_config
                    (id, deepseek_api_key, analysis_interval, lookback_period,
                     enable_opening_engine, enable_position_engine, updated_at)
                    VALUES (1, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    config.deepseek_api_key,
                    config.analysis_interval,
                    config.lookback_period,
                    config.enable_opening_engine,
                    config.enable_position_engine
                ))

                conn.commit()
                logger.info("AI配置保存成功")
                return True

        except Exception as e:
            logger.error(f"保存AI配置失败: {e}")
            raise DatabaseError("save", "ai_config", str(e))

    def load_ai_config(self) -> Optional[AIConfig]:
        """加载AI配置

        Returns:
            Optional[AIConfig]: AI配置对象
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute("SELECT * FROM ai_config WHERE id = 1")
                row = cursor.fetchone()

                if not row:
                    return None

                # 构建配置字典（明文读取）
                config_dict = {
                    "deepseek_api_key": row["deepseek_api_key"],
                    "analysis_interval": row["analysis_interval"],
                    "lookback_period": row["lookback_period"],
                    "enable_opening_engine": bool(row["enable_opening_engine"]),
                    "enable_position_engine": bool(row["enable_position_engine"])
                }

                # 创建AI配置对象
                ai_config = AIConfig.from_dict(config_dict)

                logger.info("AI配置加载成功")
                return ai_config

        except Exception as e:
            logger.error(f"加载AI配置失败: {e}")
            raise DatabaseError("load", "ai_config", str(e))

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_connection()
