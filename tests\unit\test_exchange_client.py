#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易所客户端单元测试

测试交易所客户端的基础功能和逻辑。
"""

import pytest
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig, OHLCVData
from src.utils.exceptions import ExchangeConnectionError


class TestExchangeClient:
    """交易所客户端测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟交易所配置"""
        return ExchangeConfig(
            exchange_name="okx",
            api_key="test_api_key",
            secret_key="test_secret_key",
            passphrase="test_passphrase",
            sandbox_mode=True
        )
    
    @pytest.fixture
    def exchange_client(self, mock_config):
        """创建交易所客户端实例"""
        return ExchangeClient(mock_config)
    
    def test_init(self, exchange_client, mock_config):
        """测试初始化"""
        assert exchange_client.config == mock_config
        assert exchange_client.exchange is None
        assert exchange_client.async_exchange is None
        assert exchange_client.is_connected is False
        assert 'okx' in exchange_client.supported_exchanges
    
    @patch('src.core.exchange_client.ccxt_async.okx')
    @patch('src.core.exchange_client.ccxt.okx')
    def test_connect_success(self, mock_okx, mock_async_okx, exchange_client):
        """测试成功连接"""
        # 模拟交易所实例
        mock_exchange = Mock()
        mock_exchange.load_markets.return_value = {'BTC/USDT': {}}
        mock_okx.return_value = mock_exchange

        mock_async_exchange = AsyncMock()
        mock_async_okx.return_value = mock_async_exchange

        # 测试连接
        result = exchange_client.connect()

        assert result is True
        assert exchange_client.is_connected is True
        assert exchange_client.exchange is not None
        assert exchange_client.async_exchange is not None

        # 验证API配置
        mock_okx.assert_called_once()
        call_args = mock_okx.call_args[0][0]
        assert call_args['apiKey'] == 'test_api_key'
        assert call_args['secret'] == 'test_secret_key'
        assert call_args['password'] == 'test_passphrase'
        assert call_args['sandbox'] is True
    
    def test_connect_unsupported_exchange(self):
        """测试连接不支持的交易所"""
        config = ExchangeConfig(
            exchange_name="unsupported_exchange",
            api_key="test_key",
            secret_key="test_secret"
        )
        client = ExchangeClient(config)
        
        with pytest.raises(ExchangeConnectionError) as exc_info:
            client.connect()
        
        assert "不支持的交易所" in str(exc_info.value)
    
    @patch('src.core.exchange_client.ccxt_async.okx')
    @patch('src.core.exchange_client.ccxt.okx')
    def test_connect_authentication_error(self, mock_okx, mock_async_okx, exchange_client):
        """测试认证失败"""
        import ccxt

        mock_exchange = Mock()
        mock_exchange.load_markets.side_effect = ccxt.AuthenticationError("Invalid API key")
        mock_okx.return_value = mock_exchange

        mock_async_exchange = AsyncMock()
        mock_async_okx.return_value = mock_async_exchange

        with pytest.raises(ExchangeAuthenticationError):
            exchange_client.connect()
    
    def test_ensure_connected_not_connected(self, exchange_client):
        """测试未连接时的检查"""
        with pytest.raises(ExchangeConnectionError) as exc_info:
            exchange_client._ensure_connected()
        
        assert "未连接到交易所" in str(exc_info.value)
    
    def test_fetch_ohlcv_sync_validation_error(self, exchange_client):
        """测试同步获取K线数据参数验证错误"""
        exchange_client.is_connected = True
        exchange_client.exchange = Mock()
        
        with pytest.raises(ValueError) as exc_info:
            exchange_client.fetch_ohlcv_sync("invalid_symbol", "1m")
        
        assert "无效的交易对符号" in str(exc_info.value)
    
    @patch('src.core.exchange_client.ccxt_async.okx')
    @patch('src.core.exchange_client.ccxt.okx')
    def test_fetch_ohlcv_sync_success(self, mock_okx, mock_async_okx, exchange_client):
        """测试同步获取K线数据成功"""
        # 模拟连接
        mock_exchange = Mock()
        mock_exchange.load_markets.return_value = {'BTC/USDT': {}}
        mock_exchange.fetch_ohlcv.return_value = [
            [1640995200000, 47000.0, 47500.0, 46800.0, 47200.0, 1000.0],
            [1640995260000, 47200.0, 47600.0, 47000.0, 47400.0, 1200.0]
        ]
        mock_okx.return_value = mock_exchange

        mock_async_exchange = AsyncMock()
        mock_async_okx.return_value = mock_async_exchange

        exchange_client.connect()

        # 测试获取数据
        result = exchange_client.fetch_ohlcv_sync("BTC/USDT:USDT", "1m", 2)

        assert len(result) == 2
        assert isinstance(result[0], OHLCVData)
        assert result[0].timestamp == 1640995200000
        assert result[0].open == 47000.0
        assert result[0].close == 47200.0
        assert result[1].volume == 1200.0
    
    @patch('src.core.exchange_client.ccxt_async.okx')
    @patch('src.core.exchange_client.ccxt.okx')
    def test_fetch_markets_success(self, mock_okx, mock_async_okx, exchange_client):
        """测试获取交易对信息成功"""
        # 模拟连接
        mock_exchange = Mock()
        mock_exchange.load_markets.return_value = {'BTC/USDT': {}}
        mock_exchange.fetch_markets.return_value = [
            {'symbol': 'BTC/USDT', 'type': 'swap', 'contract': True},
            {'symbol': 'ETH/USDT', 'type': 'spot', 'contract': False},
            {'symbol': 'BNB/USDT', 'type': 'swap', 'contract': True}
        ]
        mock_okx.return_value = mock_exchange

        mock_async_exchange = AsyncMock()
        mock_async_okx.return_value = mock_async_exchange

        exchange_client.connect()

        # 测试获取交易对
        result = exchange_client.fetch_markets()

        # 应该只返回永续合约
        assert len(result) == 2
        assert all(market['type'] == 'swap' or market['contract'] for market in result)
    
    @patch('src.core.exchange_client.ccxt_async.okx')
    @patch('src.core.exchange_client.ccxt.okx')
    def test_fetch_balance_success(self, mock_okx, mock_async_okx, exchange_client):
        """测试获取账户余额成功"""
        # 模拟连接
        mock_exchange = Mock()
        mock_exchange.load_markets.return_value = {'BTC/USDT': {}}
        mock_exchange.fetch_balance.return_value = {
            'USDT': {'total': 1000.0, 'free': 800.0, 'used': 200.0},
            'BTC': {'total': 0.1, 'free': 0.05, 'used': 0.05},
            'info': {},
            'free': {},
            'used': {},
            'total': {}
        }
        mock_okx.return_value = mock_exchange

        mock_async_exchange = AsyncMock()
        mock_async_okx.return_value = mock_async_exchange

        exchange_client.connect()

        # 测试获取余额
        result = exchange_client.fetch_balance()

        assert len(result) == 2
        assert 'USDT' in result
        assert 'BTC' in result
        assert isinstance(result['USDT'], AccountBalance)
        assert result['USDT'].total == 1000.0
        assert result['USDT'].available == 800.0
        assert result['USDT'].used == 200.0
    
    @patch('src.core.exchange_client.ccxt_async.okx')
    @patch('src.core.exchange_client.ccxt.okx')
    def test_fetch_positions_success(self, mock_okx, mock_async_okx, exchange_client):
        """测试获取持仓信息成功"""
        # 模拟连接
        mock_exchange = Mock()
        mock_exchange.load_markets.return_value = {'BTC/USDT': {}}
        mock_exchange.fetch_positions.return_value = [
            {
                'symbol': 'BTC/USDT:USDT',
                'side': 'long',
                'contracts': 0.1,
                'entryPrice': 50000.0,
                'markPrice': 52000.0,
                'leverage': 10,
                'unrealizedPnl': 200.0,
                'percentage': 4.0,
                'initialMargin': 500.0
            },
            {
                'symbol': 'ETH/USDT:USDT',
                'side': 'short',
                'contracts': 0.0,  # 无持仓
                'entryPrice': 0,
                'markPrice': 3000.0,
                'leverage': 5,
                'unrealizedPnl': 0,
                'percentage': 0,
                'initialMargin': 0
            }
        ]
        mock_okx.return_value = mock_exchange

        mock_async_exchange = AsyncMock()
        mock_async_okx.return_value = mock_async_exchange

        exchange_client.connect()

        # 测试获取持仓
        result = exchange_client.fetch_positions()

        # 应该只返回有持仓的
        assert len(result) == 1
        assert isinstance(result[0], Position)
        assert result[0].symbol == 'BTC/USDT:USDT'
        assert result[0].side == 'long'
        assert result[0].amount == 0.1
        assert result[0].entry_price == 50000.0
        assert result[0].current_price == 52000.0
        assert result[0].leverage == 10
    
    def test_context_manager(self, mock_config):
        """测试上下文管理器"""
        with patch('src.core.exchange_client.ccxt_async.okx') as mock_async_okx:
            with patch('src.core.exchange_client.ccxt.okx') as mock_okx:
                mock_exchange = Mock()
                mock_exchange.load_markets.return_value = {'BTC/USDT': {}}
                mock_okx.return_value = mock_exchange

                mock_async_exchange = AsyncMock()
                mock_async_okx.return_value = mock_async_exchange

                with ExchangeClient(mock_config) as client:
                    assert client.is_connected is True

                # 上下文退出后应该断开连接
                assert client.is_connected is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
