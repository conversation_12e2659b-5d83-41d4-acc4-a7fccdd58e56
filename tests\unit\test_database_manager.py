#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器单元测试

测试数据库管理器的所有CRUD操作功能。
"""

import pytest
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.data.database_manager import DatabaseManager
from src.data.models import ExchangeConfig, TradingParameters
from src.data.encryption import generate_encryption_key
from src.utils.exceptions import DatabaseError


class TestDatabaseManager:
    """数据库管理器测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库文件路径"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_path = f.name
        yield temp_path
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    @pytest.fixture
    def encryption_key(self):
        """生成测试用加密密钥"""
        return generate_encryption_key()
    
    @pytest.fixture
    def db_manager(self, temp_db_path, encryption_key):
        """创建数据库管理器实例"""
        manager = DatabaseManager(temp_db_path, encryption_key)
        manager.init_database()
        yield manager
        manager.close_connection()
    
    def test_database_initialization(self, temp_db_path, encryption_key):
        """测试数据库初始化"""
        manager = DatabaseManager(temp_db_path, encryption_key)
        
        # 测试数据库初始化
        manager.init_database()
        
        # 验证数据库文件是否创建
        assert Path(temp_db_path).exists()
        
        # 验证表是否创建
        db_info = manager.get_database_info()
        required_tables = [
            "exchange_config",
            "trading_parameters", 
            "selected_symbols",
            "system_logs",
            "ai_decisions",
            "trading_records"
        ]
        
        for table in required_tables:
            assert table in db_info["tables"]
        
        manager.close_connection()
    
    def test_exchange_config_crud(self, db_manager):
        """测试交易所配置的CRUD操作"""
        # 创建测试配置
        test_config = ExchangeConfig(
            exchange_name="test_exchange",
            api_key="test_api_key_12345",
            secret_key="test_secret_key_67890",
            passphrase="test_passphrase",
            sandbox_mode=True
        )
        
        # 测试保存
        result = db_manager.save_exchange_config(test_config)
        assert result is True
        
        # 测试加载
        loaded_config = db_manager.load_exchange_config("test_exchange")
        assert loaded_config is not None
        assert loaded_config.exchange_name == test_config.exchange_name
        assert loaded_config.api_key == test_config.api_key
        assert loaded_config.secret_key == test_config.secret_key
        assert loaded_config.passphrase == test_config.passphrase
        assert loaded_config.sandbox_mode == test_config.sandbox_mode
        
        # 测试更新
        test_config.sandbox_mode = False
        db_manager.save_exchange_config(test_config)
        updated_config = db_manager.load_exchange_config("test_exchange")
        assert updated_config.sandbox_mode is False
        
        # 测试加载不存在的配置
        non_existent = db_manager.load_exchange_config("non_existent")
        assert non_existent is None
    
    def test_trading_parameters_crud(self, db_manager):
        """测试交易参数的CRUD操作"""
        # 创建测试参数
        test_params = TradingParameters(
            max_leverage=20,
            max_position_ratio=0.8,
            opening_confidence_threshold=80,
            position_confidence_threshold=70,
            default_stop_loss_percentage=0.03,
            default_take_profit_percentage=0.15
        )
        
        # 测试保存
        result = db_manager.save_trading_parameters(test_params)
        assert result is True
        
        # 测试加载
        loaded_params = db_manager.load_trading_parameters()
        assert loaded_params is not None
        assert loaded_params.max_leverage == test_params.max_leverage
        assert loaded_params.max_position_ratio == test_params.max_position_ratio
        assert loaded_params.opening_confidence_threshold == test_params.opening_confidence_threshold
        assert loaded_params.position_confidence_threshold == test_params.position_confidence_threshold
        assert loaded_params.default_stop_loss_percentage == test_params.default_stop_loss_percentage
        assert loaded_params.default_take_profit_percentage == test_params.default_take_profit_percentage
    
    def test_selected_symbols_crud(self, db_manager):
        """测试选择交易对的CRUD操作"""
        # 创建测试交易对列表
        test_symbols = [
            "BTC/USDT:USDT",
            "ETH/USDT:USDT",
            "BNB/USDT:USDT",
            "ADA/USDT:USDT"
        ]
        
        # 测试保存
        result = db_manager.save_selected_symbols(test_symbols)
        assert result is True
        
        # 测试加载
        loaded_symbols = db_manager.load_selected_symbols()
        assert loaded_symbols == test_symbols
        
        # 测试更新
        updated_symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT"]
        db_manager.save_selected_symbols(updated_symbols)
        loaded_updated = db_manager.load_selected_symbols()
        assert loaded_updated == updated_symbols
        assert len(loaded_updated) == 2
    
    def test_ai_decisions_crud(self, db_manager):
        """测试AI决策记录的CRUD操作"""
        # 测试保存AI决策
        result = db_manager.save_ai_decision(
            symbol="BTC/USDT:USDT",
            engine_type="opening",
            action="open_long",
            confidence=85,
            reasoning="技术指标显示强烈的看涨信号",
            additional_data={"leverage": 10, "risk_level": "medium"}
        )
        assert result is True
        
        # 保存更多决策记录
        db_manager.save_ai_decision(
            symbol="ETH/USDT:USDT",
            engine_type="position",
            action="hold",
            confidence=75,
            reasoning="当前趋势保持稳定"
        )
        
        # 测试获取所有决策
        all_decisions = db_manager.get_ai_decisions()
        assert len(all_decisions) == 2
        
        # 测试获取特定交易对的决策
        btc_decisions = db_manager.get_ai_decisions(symbol="BTC/USDT:USDT")
        assert len(btc_decisions) == 1
        assert btc_decisions[0]["symbol"] == "BTC/USDT:USDT"
        assert btc_decisions[0]["action"] == "open_long"
        assert btc_decisions[0]["confidence"] == 85
        assert btc_decisions[0]["additional_data"]["leverage"] == 10
        
        # 测试限制数量
        limited_decisions = db_manager.get_ai_decisions(limit=1)
        assert len(limited_decisions) == 1
    
    def test_system_logs_crud(self, db_manager):
        """测试系统日志的CRUD操作"""
        # 测试保存系统日志
        result = db_manager.save_system_log(
            level="INFO",
            module="test_module",
            message="测试日志消息",
            details="详细信息"
        )
        assert result is True
        
        # 保存更多日志
        db_manager.save_system_log("ERROR", "test_module", "错误消息")
        db_manager.save_system_log("WARNING", "other_module", "警告消息")
        
        # 测试获取所有日志
        all_logs = db_manager.get_system_logs()
        assert len(all_logs) == 3
        
        # 测试按级别过滤
        error_logs = db_manager.get_system_logs(level="ERROR")
        assert len(error_logs) == 1
        assert error_logs[0]["level"] == "ERROR"
        
        # 测试按模块过滤
        test_module_logs = db_manager.get_system_logs(module="test_module")
        assert len(test_module_logs) == 2
        
        # 测试限制数量
        limited_logs = db_manager.get_system_logs(limit=2)
        assert len(limited_logs) == 2
    
    def test_database_backup_restore(self, db_manager, temp_db_path):
        """测试数据库备份功能"""
        # 添加一些测试数据
        test_config = ExchangeConfig(
            exchange_name="backup_test",
            api_key="backup_api_key",
            secret_key="backup_secret_key"
        )
        db_manager.save_exchange_config(test_config)

        # 创建备份
        backup_path = db_manager.backup_database()
        assert Path(backup_path).exists()

        # 验证备份文件不为空
        backup_size = Path(backup_path).stat().st_size
        assert backup_size > 0

        # 清理备份文件
        Path(backup_path).unlink()
    
    def test_database_info(self, db_manager):
        """测试获取数据库信息"""
        db_info = db_manager.get_database_info()
        
        assert "database_path" in db_info
        assert "database_size" in db_info
        assert "tables" in db_info
        assert "table_counts" in db_info
        assert "encryption_enabled" in db_info
        
        assert db_info["encryption_enabled"] is True
        assert len(db_info["tables"]) >= 6  # 至少6个表
    
    def test_cleanup_old_logs(self, db_manager):
        """测试清理旧日志"""
        # 添加一些日志
        for i in range(5):
            db_manager.save_system_log("INFO", "test", f"日志 {i}")
        
        # 清理日志（由于是新创建的，应该不会删除任何记录）
        deleted_count = db_manager.cleanup_old_logs(days=30)
        assert deleted_count == 0
        
        # 验证日志仍然存在
        logs = db_manager.get_system_logs()
        assert len(logs) == 5


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
