{% extends "base.html" %}

{% block title %}AI决策 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<li style="display: flex; align-items: center; gap: 4px;">
    <span>/</span>
    <span>🧠</span>
    <span>AI决策</span>
</li>
{% endblock %}

{% block extra_css %}
<style>
    /* AI决策页面特定样式 */
    .ai-dashboard {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-bottom: 24px;
    }
    
    .ai-engine-card {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        padding: 24px;
        box-shadow: var(--ant-box-shadow);
    }
    
    .engine-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }
    
    .engine-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 18px;
        font-weight: 600;
        color: var(--ant-color-text);
    }
    
    .engine-status {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .engine-status.active {
        background: rgba(82, 196, 26, 0.1);
        color: var(--ant-color-success);
    }
    
    .engine-status.inactive {
        background: rgba(255, 77, 79, 0.1);
        color: var(--ant-color-error);
    }
    
    .engine-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;
    }
    
    .stat-box {
        text-align: center;
        padding: 12px;
        background: var(--ant-color-fill-secondary);
        border-radius: var(--ant-border-radius);
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: var(--ant-color-primary);
        margin-bottom: 4px;
    }
    
    .stat-label {
        font-size: 12px;
        color: var(--ant-color-text-secondary);
    }
    
    .recent-decision {
        padding: 16px;
        background: var(--ant-color-fill);
        border-radius: var(--ant-border-radius);
        border-left: 4px solid var(--ant-color-primary);
    }
    
    .decision-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .decision-action {
        font-weight: 600;
        color: var(--ant-color-text);
    }
    
    .decision-confidence {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background: var(--ant-color-primary);
        color: white;
    }
    
    .decision-reasoning {
        font-size: 14px;
        color: var(--ant-color-text-secondary);
        line-height: 1.5;
    }
    
    .decisions-log {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        box-shadow: var(--ant-box-shadow);
    }
    
    .log-header {
        padding: 16px 24px;
        border-bottom: 1px solid var(--ant-color-border-secondary);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--ant-color-fill-secondary);
    }
    
    .log-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .log-filters {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .filter-select {
        padding: 4px 8px;
        border: 1px solid var(--ant-color-border);
        border-radius: 4px;
        background: var(--ant-color-bg-container);
        color: var(--ant-color-text);
        font-size: 12px;
    }
    
    .log-content {
        max-height: 600px;
        overflow-y: auto;
    }
    
    .decision-item {
        padding: 16px 24px;
        border-bottom: 1px solid var(--ant-color-border-secondary);
        transition: background-color 0.3s;
    }
    
    .decision-item:hover {
        background: var(--ant-color-fill);
    }
    
    .decision-item:last-child {
        border-bottom: none;
    }
    
    .decision-meta {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
    }
    
    .decision-info {
        flex: 1;
    }
    
    .decision-symbol {
        font-weight: 600;
        color: var(--ant-color-text);
        margin-bottom: 4px;
    }
    
    .decision-time {
        font-size: 12px;
        color: var(--ant-color-text-tertiary);
    }
    
    .decision-badges {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .decision-type {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .decision-type.opening {
        background: rgba(22, 119, 255, 0.1);
        color: var(--ant-color-info);
    }
    
    .decision-type.position {
        background: rgba(250, 173, 20, 0.1);
        color: var(--ant-color-warning);
    }
    
    .decision-action-badge {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .decision-action-badge.open_long {
        background: rgba(82, 196, 26, 0.1);
        color: var(--ant-color-success);
    }
    
    .decision-action-badge.open_short {
        background: rgba(255, 77, 79, 0.1);
        color: var(--ant-color-error);
    }
    
    .decision-action-badge.hold {
        background: rgba(0, 0, 0, 0.1);
        color: var(--ant-color-text-secondary);
    }
    
    .decision-action-badge.close {
        background: rgba(250, 173, 20, 0.1);
        color: var(--ant-color-warning);
    }
    
    .decision-details {
        margin-top: 12px;
    }
    
    .decision-reasoning-full {
        font-size: 14px;
        color: var(--ant-color-text);
        line-height: 1.6;
        background: var(--ant-color-fill-secondary);
        padding: 12px;
        border-radius: var(--ant-border-radius);
        margin-bottom: 8px;
    }
    
    .decision-metrics {
        display: flex;
        gap: 16px;
        font-size: 12px;
        color: var(--ant-color-text-secondary);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--ant-color-text-secondary);
    }
    
    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }
    
    .loading-state {
        text-align: center;
        padding: 40px 20px;
        color: var(--ant-color-text-secondary);
    }
    
    .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid var(--ant-color-border);
        border-top-color: var(--ant-color-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 12px;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .ai-dashboard {
            grid-template-columns: 1fr;
        }
        
        .engine-stats {
            grid-template-columns: 1fr;
        }
        
        .decision-meta {
            flex-direction: column;
            gap: 8px;
        }
        
        .decision-badges {
            align-self: flex-start;
        }
        
        .decision-metrics {
            flex-direction: column;
            gap: 4px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div style="margin-bottom: 24px;">
    <h1 style="margin: 0 0 8px 0; font-size: 24px; font-weight: 600; color: var(--ant-color-text);">
        🧠 AI决策中心
    </h1>
    <p style="margin: 0; color: var(--ant-color-text-secondary);">
        监控AI开仓引擎和持仓引擎的决策过程和结果
    </p>
</div>

<!-- AI引擎状态面板 -->
<div class="ai-dashboard">
    <!-- AI开仓引擎 -->
    <div class="ai-engine-card">
        <div class="engine-header">
            <div class="engine-title">
                <span>🎯</span>
                <span>AI开仓引擎</span>
            </div>
            <div class="engine-status active" id="openingEngineStatus">运行中</div>
        </div>
        
        <div class="engine-stats">
            <div class="stat-box">
                <div class="stat-number" id="openingDecisionsToday">--</div>
                <div class="stat-label">今日决策</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" id="openingSuccessRate">--</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
        
        <div class="recent-decision" id="recentOpeningDecision">
            <div class="decision-header">
                <div class="decision-action">暂无最近决策</div>
                <div class="decision-confidence">--</div>
            </div>
            <div class="decision-reasoning">等待AI分析市场数据...</div>
        </div>
    </div>
    
    <!-- AI持仓引擎 -->
    <div class="ai-engine-card">
        <div class="engine-header">
            <div class="engine-title">
                <span>📊</span>
                <span>AI持仓引擎</span>
            </div>
            <div class="engine-status active" id="positionEngineStatus">运行中</div>
        </div>
        
        <div class="engine-stats">
            <div class="stat-box">
                <div class="stat-number" id="positionDecisionsToday">--</div>
                <div class="stat-label">今日决策</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" id="positionSuccessRate">--</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
        
        <div class="recent-decision" id="recentPositionDecision">
            <div class="decision-header">
                <div class="decision-action">暂无最近决策</div>
                <div class="decision-confidence">--</div>
            </div>
            <div class="decision-reasoning">等待持仓分析...</div>
        </div>
    </div>
</div>

<!-- AI决策日志 -->
<div class="decisions-log">
    <div class="log-header">
        <div class="log-title">
            <span>📋</span>
            <span>AI决策日志</span>
        </div>
        <div class="log-filters">
            <select class="filter-select" id="engineFilter">
                <option value="all">所有引擎</option>
                <option value="opening">开仓引擎</option>
                <option value="position">持仓引擎</option>
            </select>
            <select class="filter-select" id="actionFilter">
                <option value="all">所有动作</option>
                <option value="open_long">开多</option>
                <option value="open_short">开空</option>
                <option value="hold">持有</option>
                <option value="close">平仓</option>
            </select>
            <button class="ant-btn" id="refreshDecisions">
                <span>🔄</span>
                <span>刷新</span>
            </button>
        </div>
    </div>
    
    <div class="log-content" id="decisionsLogContent">
        <div class="loading-state">
            <div class="loading-spinner"></div>
            <div>加载AI决策日志...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // AI决策管理器
    class AIDecisionsManager {
        constructor() {
            this.decisions = [];
            this.isLoading = false;
            this.refreshInterval = null;
            this.currentFilters = {
                engine: 'all',
                action: 'all'
            };
            this.init();
        }
        
        init() {
            this.loadAIDecisions();
            this.loadEngineStats();
            this.startAutoRefresh();
            this.bindEvents();
        }
        
        bindEvents() {
            // 刷新按钮
            document.getElementById('refreshDecisions').addEventListener('click', () => {
                this.loadAIDecisions();
                this.loadEngineStats();
            });
            
            // 过滤器
            document.getElementById('engineFilter').addEventListener('change', (e) => {
                this.currentFilters.engine = e.target.value;
                this.filterDecisions();
            });
            
            document.getElementById('actionFilter').addEventListener('change', (e) => {
                this.currentFilters.action = e.target.value;
                this.filterDecisions();
            });
        }
        
        async loadAIDecisions() {
            if (this.isLoading) return;
            
            this.isLoading = true;
            this.showLoading();
            
            try {
                const response = await axios.get('/api/trading/ai-decisions');
                this.decisions = response.data.decisions || [];
                this.renderDecisions();
                
            } catch (error) {
                console.error('加载AI决策失败:', error);
                this.showError('加载AI决策失败，请稍后重试');
            } finally {
                this.isLoading = false;
            }
        }
        
        async loadEngineStats() {
            try {
                const response = await axios.get('/api/trading/ai-decisions/stats');
                const stats = response.data;
                
                this.updateEngineStats(stats);
                this.updateRecentDecisions(stats.recentDecisions);
                
            } catch (error) {
                console.error('加载引擎统计失败:', error);
            }
        }
        
        updateEngineStats(stats) {
            // 开仓引擎统计
            document.getElementById('openingDecisionsToday').textContent = stats.opening?.decisionsToday || 0;
            document.getElementById('openingSuccessRate').textContent = `${(stats.opening?.successRate || 0).toFixed(1)}%`;
            
            // 持仓引擎统计
            document.getElementById('positionDecisionsToday').textContent = stats.position?.decisionsToday || 0;
            document.getElementById('positionSuccessRate').textContent = `${(stats.position?.successRate || 0).toFixed(1)}%`;
            
            // 引擎状态
            this.updateEngineStatus('openingEngineStatus', stats.opening?.status);
            this.updateEngineStatus('positionEngineStatus', stats.position?.status);
        }
        
        updateEngineStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (status === 'active') {
                element.className = 'engine-status active';
                element.textContent = '运行中';
            } else {
                element.className = 'engine-status inactive';
                element.textContent = '已停止';
            }
        }
        
        updateRecentDecisions(recentDecisions) {
            // 更新最近的开仓决策
            if (recentDecisions?.opening) {
                this.updateRecentDecision('recentOpeningDecision', recentDecisions.opening);
            }
            
            // 更新最近的持仓决策
            if (recentDecisions?.position) {
                this.updateRecentDecision('recentPositionDecision', recentDecisions.position);
            }
        }
        
        updateRecentDecision(elementId, decision) {
            const element = document.getElementById(elementId);
            const actionText = this.getActionText(decision.action);
            
            element.innerHTML = `
                <div class="decision-header">
                    <div class="decision-action">${decision.symbol} - ${actionText}</div>
                    <div class="decision-confidence">${decision.confidence}%</div>
                </div>
                <div class="decision-reasoning">${decision.reasoning}</div>
            `;
        }
        
        renderDecisions() {
            const container = document.getElementById('decisionsLogContent');
            
            if (this.decisions.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🤖</div>
                        <div>暂无AI决策记录</div>
                    </div>
                `;
                return;
            }
            
            const decisionsHtml = this.decisions.map(decision => this.renderDecisionItem(decision)).join('');
            container.innerHTML = decisionsHtml;
        }
        
        renderDecisionItem(decision) {
            const actionText = this.getActionText(decision.action);
            const engineText = decision.engineType === 'opening' ? '开仓引擎' : '持仓引擎';
            
            return `
                <div class="decision-item">
                    <div class="decision-meta">
                        <div class="decision-info">
                            <div class="decision-symbol">${decision.symbol}</div>
                            <div class="decision-time">${this.formatTime(decision.timestamp)}</div>
                        </div>
                        <div class="decision-badges">
                            <div class="decision-type ${decision.engineType}">${engineText}</div>
                            <div class="decision-action-badge ${decision.action}">${actionText}</div>
                            <div class="decision-confidence">${decision.confidence}%</div>
                        </div>
                    </div>
                    <div class="decision-details">
                        <div class="decision-reasoning-full">${decision.reasoning}</div>
                        <div class="decision-metrics">
                            <span>执行时间: ${this.formatTime(decision.timestamp)}</span>
                            <span>置信度: ${decision.confidence}%</span>
                            ${decision.executionResult ? `<span>执行结果: ${decision.executionResult}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }
        
        filterDecisions() {
            let filteredDecisions = this.decisions;
            
            if (this.currentFilters.engine !== 'all') {
                filteredDecisions = filteredDecisions.filter(d => d.engineType === this.currentFilters.engine);
            }
            
            if (this.currentFilters.action !== 'all') {
                filteredDecisions = filteredDecisions.filter(d => d.action === this.currentFilters.action);
            }
            
            const container = document.getElementById('decisionsLogContent');
            
            if (filteredDecisions.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🔍</div>
                        <div>没有符合条件的决策记录</div>
                    </div>
                `;
                return;
            }
            
            const decisionsHtml = filteredDecisions.map(decision => this.renderDecisionItem(decision)).join('');
            container.innerHTML = decisionsHtml;
        }
        
        getActionText(action) {
            const actionMap = {
                'open_long': '开多',
                'open_short': '开空',
                'hold': '持有',
                'close': '平仓',
                'no_action': '无操作'
            };
            return actionMap[action] || action;
        }
        
        formatTime(timestamp) {
            return new Date(timestamp).toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
        
        showLoading() {
            const container = document.getElementById('decisionsLogContent');
            container.innerHTML = `
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <div>加载AI决策日志...</div>
                </div>
            `;
        }
        
        showError(message) {
            const container = document.getElementById('decisionsLogContent');
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">❌</div>
                    <div>${message}</div>
                </div>
            `;
        }
        
        startAutoRefresh() {
            // 每20秒自动刷新数据
            this.refreshInterval = setInterval(() => {
                this.loadAIDecisions();
                this.loadEngineStats();
            }, 20000);
        }
        
        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        }
    }
    
    // 页面加载完成后初始化AI决策管理器
    document.addEventListener('DOMContentLoaded', () => {
        window.aiDecisionsManager = new AIDecisionsManager();
    });
    
    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', () => {
        if (window.aiDecisionsManager) {
            window.aiDecisionsManager.stopAutoRefresh();
        }
    });
</script>
{% endblock %}
