#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表板API路由

此模块提供仪表板相关的API接口，包括：
1. 系统状态概览
2. 账户余额信息
3. 持仓概览
4. 实时盈亏统计
5. AI决策历史
"""

import time
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/overview")
async def get_dashboard_overview():
    """获取仪表板概览数据"""
    try:
        # 这里应该从交易系统获取实际数据
        # 目前返回模拟数据
        overview_data = {
            "system_status": {
                "is_running": False,
                "uptime": "00:00:00",
                "last_update": time.time()
            },
            "account_summary": {
                "total_balance": 0.0,
                "available_balance": 0.0,
                "used_margin": 0.0,
                "unrealized_pnl": 0.0,
                "daily_pnl": 0.0
            },
            "positions_summary": {
                "total_positions": 0,
                "long_positions": 0,
                "short_positions": 0,
                "total_exposure": 0.0
            },
            "ai_decisions": {
                "total_decisions": 0,
                "successful_decisions": 0,
                "success_rate": 0.0,
                "last_decision_time": None
            }
        }
        
        return JSONResponse(content=overview_data)
        
    except Exception as e:
        logger.error(f"获取仪表板概览失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/account")
async def get_account_info():
    """获取账户信息"""
    try:
        logger.info("开始获取账户信息...")

        # 导入必要的模块
        from src.data.database_manager import DatabaseManager
        from src.core.exchange_client import ExchangeClient

        # 创建数据库管理器
        logger.info("创建数据库管理器...")
        db_manager = DatabaseManager()

        # 加载交易所配置
        logger.info("加载交易所配置...")
        exchange_config = db_manager.load_exchange_config()

        if not exchange_config:
            logger.warning("未找到交易所配置")
            return JSONResponse(content={
                "balances": {},
                "total_balance_usdt": 0.0,
                "available_balance_usdt": 0.0,
                "used_margin_usdt": 0.0,
                "margin_ratio": 0.0,
                "last_update": time.time(),
                "exchange_connected": False,
                "message": "交易所未配置"
            })

        logger.info(f"找到交易所配置: {exchange_config.exchange_name}")

        # 创建交易所客户端
        logger.info("创建交易所客户端...")
        exchange_client = ExchangeClient(exchange_config)

        # 连接交易所
        logger.info("连接交易所...")
        connected = exchange_client.connect()

        if not connected:
            logger.warning("交易所连接失败")
            return JSONResponse(content={
                "balances": {},
                "total_balance_usdt": 0.0,
                "available_balance_usdt": 0.0,
                "used_margin_usdt": 0.0,
                "margin_ratio": 0.0,
                "last_update": time.time(),
                "exchange_connected": False,
                "message": "交易所连接失败"
            })

        logger.info("交易所连接成功，获取账户余额...")

        # 获取账户余额
        balance_data = exchange_client.fetch_balance()
        logger.info(f"获取到余额数据: {len(balance_data)} 个币种")

        # 计算总资产和可用余额
        total_balance = 0.0
        available_balance = 0.0
        balances = {}

        for currency, account_balance in balance_data.items():
            if account_balance.total > 0:
                balances[currency] = {
                    'total': account_balance.total,
                    'available': account_balance.available,
                    'used': account_balance.used
                }

                # USDT为基准货币
                if currency == 'USDT':
                    total_balance += account_balance.total
                    available_balance += account_balance.available
                    logger.info(f"USDT余额: 总量={account_balance.total}, 可用={account_balance.available}")

        # 断开连接
        exchange_client.disconnect()
        logger.info("交易所连接已断开")

        # 构建响应数据
        account_data = {
            "balances": balances,
            "total_balance_usdt": total_balance,
            "available_balance_usdt": available_balance,
            "used_margin_usdt": total_balance - available_balance,
            "margin_ratio": 0.0,
            "last_update": time.time(),
            "exchange_connected": True
        }

        logger.info(f"🎉 账户数据获取成功: 总资产={total_balance} USDT, 可用余额={available_balance} USDT (热重载功能正常工作!)")
        return JSONResponse(content=account_data)

    except Exception as e:
        logger.error(f"获取账户信息失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")

        # 返回错误响应
        return JSONResponse(content={
            "balances": {},
            "total_balance_usdt": 0.0,
            "available_balance_usdt": 0.0,
            "used_margin_usdt": 0.0,
            "margin_ratio": 0.0,
            "last_update": time.time(),
            "exchange_connected": False,
            "message": f"获取账户信息失败: {str(e)}"
        }, status_code=500)


@router.get("/positions")
async def get_positions_summary():
    """获取持仓概览"""
    try:
        # 这里应该从交易系统获取实际持仓数据
        positions_data = {
            "positions": [],
            "total_positions": 0,
            "total_exposure": 0.0,
            "total_unrealized_pnl": 0.0,
            "exposure_ratio": 0.0,
            "last_update": time.time()
        }
        
        return JSONResponse(content=positions_data)
        
    except Exception as e:
        logger.error(f"获取持仓概览失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance")
async def get_performance_stats():
    """获取交易表现统计"""
    try:
        # 这里应该从交易系统获取实际表现数据
        performance_data = {
            "daily_stats": {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "daily_pnl": 0.0,
                "daily_return": 0.0
            },
            "weekly_stats": {
                "total_trades": 0,
                "weekly_pnl": 0.0,
                "weekly_return": 0.0,
                "max_drawdown": 0.0
            },
            "monthly_stats": {
                "total_trades": 0,
                "monthly_pnl": 0.0,
                "monthly_return": 0.0,
                "sharpe_ratio": 0.0
            },
            "last_update": time.time()
        }
        
        return JSONResponse(content=performance_data)
        
    except Exception as e:
        logger.error(f"获取交易表现统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ai-decisions")
async def get_ai_decisions(
    engine_type: Optional[str] = Query(None, description="引擎类型: opening/position"),
    symbol: Optional[str] = Query(None, description="交易对筛选"),
    action: Optional[str] = Query(None, description="决策动作筛选"),
    confidence_min: int = Query(0, description="最小置信度"),
    confidence_max: int = Query(100, description="最大置信度"),
    time_range: str = Query("24h", description="时间范围"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取AI决策数据"""
    try:
        # 模拟AI引擎状态
        engine_status = {
            "opening_engine": {
                "is_running": True,
                "today_decisions": 15,
                "execution_rate": 85.5,
                "avg_confidence": 78.2
            },
            "position_engine": {
                "is_running": True,
                "today_decisions": 8,
                "execution_rate": 92.3,
                "avg_confidence": 82.1
            }
        }

        # 模拟AI决策数据
        mock_decisions = [
            {
                "id": "dec_001",
                "timestamp": time.time() - 3600,
                "engine_type": "opening",
                "symbol": "BTC/USDT:USDT",
                "action": "open_long",
                "confidence": 85,
                "reasoning": "技术指标显示强烈看涨信号，RSI超卖反弹，MACD金叉形成",
                "status": "executed"
            },
            {
                "id": "dec_002",
                "timestamp": time.time() - 1800,
                "engine_type": "position",
                "symbol": "ETH/USDT:USDT",
                "action": "hold",
                "confidence": 72,
                "reasoning": "当前持仓盈利状态良好，市场趋势保持稳定",
                "status": "executed"
            },
            {
                "id": "dec_003",
                "timestamp": time.time() - 900,
                "engine_type": "opening",
                "symbol": "SOL/USDT:USDT",
                "action": "no_action",
                "confidence": 45,
                "reasoning": "市场信号混乱，置信度不足，暂不开仓",
                "status": "pending"
            }
        ]

        # 应用筛选
        filtered_decisions = mock_decisions
        if engine_type:
            filtered_decisions = [d for d in filtered_decisions if d["engine_type"] == engine_type]
        if symbol:
            filtered_decisions = [d for d in filtered_decisions if d["symbol"] == symbol]
        if action:
            filtered_decisions = [d for d in filtered_decisions if d["action"] == action]

        # 置信度筛选
        filtered_decisions = [d for d in filtered_decisions
                            if confidence_min <= d["confidence"] <= confidence_max]

        # 应用限制
        filtered_decisions = filtered_decisions[:limit]

        # 统计信息
        statistics = {
            "total": len(mock_decisions),
            "executed": len([d for d in mock_decisions if d["status"] == "executed"]),
            "success_rate": 88.5
        }

        # 可用交易对
        available_symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "BNB/USDT:USDT"]

        decisions_data = {
            "decisions": filtered_decisions,
            "engine_status": engine_status,
            "statistics": statistics,
            "available_symbols": available_symbols,
            "filters": {
                "engine_type": engine_type,
                "symbol": symbol,
                "action": action,
                "confidence_min": confidence_min,
                "confidence_max": confidence_max,
                "time_range": time_range,
                "limit": limit
            },
            "last_update": time.time()
        }

        return JSONResponse(content=decisions_data)

    except Exception as e:
        logger.error(f"获取AI决策数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ai-decisions/recent")
async def get_recent_ai_decisions(limit: int = 10):
    """获取最近的AI决策"""
    try:
        # 这里应该从交易系统获取实际AI决策数据
        decisions_data = {
            "decisions": [],
            "total_count": 0,
            "last_update": time.time()
        }

        return JSONResponse(content=decisions_data)

    except Exception as e:
        logger.error(f"获取AI决策历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-data/{symbol}")
async def get_market_data(symbol: str, timeframe: str = "1h"):
    """获取市场数据"""
    try:
        # 这里应该从市场数据引擎获取实际数据
        market_data = {
            "symbol": symbol,
            "timeframe": timeframe,
            "current_price": 0.0,
            "price_change_24h": 0.0,
            "price_change_percent_24h": 0.0,
            "volume_24h": 0.0,
            "ohlcv_data": [],
            "last_update": time.time()
        }
        
        return JSONResponse(content=market_data)
        
    except Exception as e:
        logger.error(f"获取市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/risk-summary")
async def get_risk_summary():
    """获取风险摘要"""
    try:
        # 这里应该从风险管理系统获取实际数据
        risk_data = {
            "total_exposure": 0.0,
            "exposure_ratio": 0.0,
            "margin_usage": 0.0,
            "risk_level": "low",
            "emergency_mode": False,
            "risk_warnings": [],
            "last_update": time.time()
        }
        
        return JSONResponse(content=risk_data)
        
    except Exception as e:
        logger.error(f"获取风险摘要失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system-status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 这里应该从系统监控获取实际状态
        system_status = {
            "aiEngineStatus": "active",
            "exchangeConnection": "connected",
            "latency": 45,
            "dataUpdateStatus": "实时",
            "riskManagementStatus": "正常",
            "uptime": 3600
        }

        logger.info("系统状态获取成功")
        return system_status

    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/activity-logs")
async def get_activity_logs():
    """获取活动日志"""
    try:
        import time
        current_time = int(time.time() * 1000)

        # 这里应该从日志系统获取实际日志
        logs = [
            {
                "timestamp": current_time - 300000,
                "level": "success",
                "message": "BTC/USDT 开多仓位，价格: $45,200"
            },
            {
                "timestamp": current_time - 600000,
                "level": "info",
                "message": "AI引擎分析完成，置信度: 85%"
            },
            {
                "timestamp": current_time - 900000,
                "level": "warning",
                "message": "ETH/USDT 价格波动较大，建议谨慎操作"
            }
        ]

        logger.info("活动日志获取成功")
        return logs

    except Exception as e:
        logger.error(f"获取活动日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
