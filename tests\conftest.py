#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pytest配置文件

定义测试的全局配置和fixture。
"""

import pytest
import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
os.environ["TESTING"] = "true"
os.environ["LOG_LEVEL"] = "WARNING"


@pytest.fixture(scope="session")
def project_root_path():
    """项目根目录路径"""
    return Path(__file__).parent.parent


@pytest.fixture(scope="session")
def temp_dir():
    """创建临时目录"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_ohlcv_data():
    """示例OHLCV数据"""
    return [
        {
            "timestamp": 1640995200000,  # 2022-01-01 00:00:00
            "open": 47000.0,
            "high": 47500.0,
            "low": 46800.0,
            "close": 47200.0,
            "volume": 1000.0
        },
        {
            "timestamp": 1640995260000,  # 2022-01-01 00:01:00
            "open": 47200.0,
            "high": 47600.0,
            "low": 47000.0,
            "close": 47400.0,
            "volume": 1200.0
        },
        {
            "timestamp": 1640995320000,  # 2022-01-01 00:02:00
            "open": 47400.0,
            "high": 47800.0,
            "low": 47300.0,
            "close": 47700.0,
            "volume": 800.0
        }
    ]


@pytest.fixture
def sample_exchange_config():
    """示例交易所配置"""
    return {
        "exchange_name": "test_exchange",
        "api_key": "test_api_key_12345",
        "secret_key": "test_secret_key_67890",
        "passphrase": "test_passphrase",
        "sandbox_mode": True
    }


@pytest.fixture
def sample_trading_parameters():
    """示例交易参数"""
    return {
        "max_leverage": 10,
        "max_position_ratio": 0.5,
        "opening_confidence_threshold": 70,
        "position_confidence_threshold": 60,
        "default_stop_loss_percentage": 0.05,
        "default_take_profit_percentage": 0.1
    }


@pytest.fixture
def sample_ai_decision():
    """示例AI决策"""
    return {
        "action": "open_long",
        "confidence": 85,
        "reasoning": "基于技术指标分析，RSI显示超卖，MACD金叉，建议开多头仓位",
        "symbol": "BTC/USDT:USDT",
        "engine_type": "opening",
        "suggested_leverage": 10,
        "risk_level": "medium"
    }


@pytest.fixture
def sample_position_data():
    """示例持仓数据"""
    return {
        "symbol": "BTC/USDT:USDT",
        "side": "long",
        "amount": 0.1,
        "entry_price": 50000.0,
        "current_price": 52000.0,
        "leverage": 10,
        "unrealized_pnl": 200.0,
        "unrealized_pnl_percentage": 4.0,
        "margin_used": 500.0
    }


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 标记单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 标记慢速测试"
    )
    config.addinivalue_line(
        "markers", "database: 标记数据库相关测试"
    )
    config.addinivalue_line(
        "markers", "encryption: 标记加密相关测试"
    )
    config.addinivalue_line(
        "markers", "ai: 标记AI相关测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加unit标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# 测试报告钩子
def pytest_runtest_makereport(item, call):
    """生成测试报告"""
    if "incremental" in item.keywords:
        if call.excinfo is not None:
            parent = item.parent
            parent._previousfailed = item


def pytest_runtest_setup(item):
    """测试设置钩子"""
    if "incremental" in item.keywords:
        previousfailed = getattr(item.parent, "_previousfailed", None)
        if previousfailed is not None:
            pytest.skip(f"previous test failed ({previousfailed.name})")


# 清理钩子
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """自动清理测试文件"""
    yield
    # 测试后清理
    test_files = [
        "test_*.db",
        "test_*.log",
        "*.tmp"
    ]
    
    for pattern in test_files:
        for file_path in Path(".").glob(pattern):
            try:
                file_path.unlink()
            except:
                pass
