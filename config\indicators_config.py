#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统技术指标配置模块

此模块定义了系统中使用的所有技术指标的配置参数，包括：
1. 趋势指标配置
2. 震荡指标配置
3. 波动率指标配置
4. 成交量指标配置
5. 支撑阻力指标配置
"""

from typing import Dict, List, Any, Union
from dataclasses import dataclass, field
from enum import Enum


class IndicatorCategory(Enum):
    """技术指标分类枚举"""
    TREND = "trend"                    # 趋势指标
    OSCILLATOR = "oscillator"          # 震荡指标
    VOLATILITY = "volatility"          # 波动率指标
    VOLUME = "volume"                  # 成交量指标
    SUPPORT_RESISTANCE = "support_resistance"  # 支撑阻力指标


class TimeFrame(Enum):
    """时间周期枚举"""
    M1 = "1m"      # 1分钟
    M5 = "5m"      # 5分钟
    M15 = "15m"    # 15分钟
    H1 = "1h"      # 1小时
    H4 = "4h"      # 4小时
    D1 = "1d"      # 1天


@dataclass
class IndicatorConfig:
    """技术指标配置类"""
    name: str                          # 指标名称
    category: IndicatorCategory        # 指标分类
    enabled: bool = True               # 是否启用
    parameters: Dict[str, Any] = field(default_factory=dict)  # 指标参数
    timeframes: List[TimeFrame] = field(default_factory=list)  # 适用时间周期
    description: str = ""              # 指标描述
    weight: float = 1.0               # 指标权重（用于AI分析）


class IndicatorsConfig:
    """技术指标配置管理类"""
    
    def __init__(self):
        """初始化技术指标配置"""
        self.indicators = self._initialize_indicators()
        self.default_timeframes = [TimeFrame.M1, TimeFrame.M5, TimeFrame.M15, TimeFrame.H1]
    
    def _initialize_indicators(self) -> Dict[str, IndicatorConfig]:
        """初始化所有技术指标配置
        
        Returns:
            Dict[str, IndicatorConfig]: 指标配置字典
        """
        indicators = {}
        
        # =============================================================================
        # 趋势指标配置
        # =============================================================================
        
        # 简单移动平均线
        indicators["SMA_20"] = IndicatorConfig(
            name="SMA_20",
            category=IndicatorCategory.TREND,
            parameters={"timeperiod": 20},
            timeframes=self.default_timeframes,
            description="20周期简单移动平均线",
            weight=1.2
        )
        
        indicators["SMA_50"] = IndicatorConfig(
            name="SMA_50",
            category=IndicatorCategory.TREND,
            parameters={"timeperiod": 50},
            timeframes=self.default_timeframes,
            description="50周期简单移动平均线",
            weight=1.5
        )
        
        # 指数移动平均线
        indicators["EMA_12"] = IndicatorConfig(
            name="EMA_12",
            category=IndicatorCategory.TREND,
            parameters={"timeperiod": 12},
            timeframes=self.default_timeframes,
            description="12周期指数移动平均线",
            weight=1.3
        )
        
        indicators["EMA_26"] = IndicatorConfig(
            name="EMA_26",
            category=IndicatorCategory.TREND,
            parameters={"timeperiod": 26},
            timeframes=self.default_timeframes,
            description="26周期指数移动平均线",
            weight=1.3
        )
        
        # MACD指标
        indicators["MACD"] = IndicatorConfig(
            name="MACD",
            category=IndicatorCategory.TREND,
            parameters={
                "fastperiod": 12,
                "slowperiod": 26,
                "signalperiod": 9
            },
            timeframes=self.default_timeframes,
            description="MACD异同移动平均线",
            weight=1.8
        )
        
        # ADX平均趋向指数
        indicators["ADX"] = IndicatorConfig(
            name="ADX",
            category=IndicatorCategory.TREND,
            parameters={"timeperiod": 14},
            timeframes=self.default_timeframes,
            description="平均趋向指数",
            weight=1.4
        )
        
        # =============================================================================
        # 震荡指标配置
        # =============================================================================
        
        # RSI相对强弱指数
        indicators["RSI_14"] = IndicatorConfig(
            name="RSI_14",
            category=IndicatorCategory.OSCILLATOR,
            parameters={"timeperiod": 14},
            timeframes=self.default_timeframes,
            description="14周期相对强弱指数",
            weight=1.6
        )
        
        # 随机指标
        indicators["STOCH_K"] = IndicatorConfig(
            name="STOCH_K",
            category=IndicatorCategory.OSCILLATOR,
            parameters={
                "fastk_period": 14,
                "slowk_period": 3,
                "slowd_period": 3
            },
            timeframes=self.default_timeframes,
            description="随机指标K值",
            weight=1.2
        )
        
        indicators["STOCH_D"] = IndicatorConfig(
            name="STOCH_D",
            category=IndicatorCategory.OSCILLATOR,
            parameters={
                "fastk_period": 14,
                "slowk_period": 3,
                "slowd_period": 3
            },
            timeframes=self.default_timeframes,
            description="随机指标D值",
            weight=1.2
        )
        
        # Williams %R
        indicators["WILLIAMS_R"] = IndicatorConfig(
            name="WILLIAMS_R",
            category=IndicatorCategory.OSCILLATOR,
            parameters={"timeperiod": 14},
            timeframes=self.default_timeframes,
            description="威廉指标",
            weight=1.1
        )
        
        # CCI商品通道指数
        indicators["CCI"] = IndicatorConfig(
            name="CCI",
            category=IndicatorCategory.OSCILLATOR,
            parameters={"timeperiod": 14},
            timeframes=self.default_timeframes,
            description="商品通道指数",
            weight=1.3
        )
        
        # =============================================================================
        # 波动率指标配置
        # =============================================================================
        
        # 布林带
        indicators["BB_UPPER"] = IndicatorConfig(
            name="BB_UPPER",
            category=IndicatorCategory.VOLATILITY,
            parameters={
                "timeperiod": 20,
                "nbdevup": 2,
                "nbdevdn": 2
            },
            timeframes=self.default_timeframes,
            description="布林带上轨",
            weight=1.4
        )
        
        indicators["BB_LOWER"] = IndicatorConfig(
            name="BB_LOWER",
            category=IndicatorCategory.VOLATILITY,
            parameters={
                "timeperiod": 20,
                "nbdevup": 2,
                "nbdevdn": 2
            },
            timeframes=self.default_timeframes,
            description="布林带下轨",
            weight=1.4
        )
        
        # ATR真实波动幅度
        indicators["ATR"] = IndicatorConfig(
            name="ATR",
            category=IndicatorCategory.VOLATILITY,
            parameters={"timeperiod": 14},
            timeframes=self.default_timeframes,
            description="真实波动幅度",
            weight=1.3
        )
        
        # 肯特纳通道
        indicators["KELTNER_UPPER"] = IndicatorConfig(
            name="KELTNER_UPPER",
            category=IndicatorCategory.VOLATILITY,
            parameters={
                "timeperiod": 20,
                "multiplier": 2.0
            },
            timeframes=self.default_timeframes,
            description="肯特纳通道上轨",
            weight=1.2
        )
        
        indicators["KELTNER_LOWER"] = IndicatorConfig(
            name="KELTNER_LOWER",
            category=IndicatorCategory.VOLATILITY,
            parameters={
                "timeperiod": 20,
                "multiplier": 2.0
            },
            timeframes=self.default_timeframes,
            description="肯特纳通道下轨",
            weight=1.2
        )
        
        # =============================================================================
        # 成交量指标配置
        # =============================================================================
        
        # OBV能量潮
        indicators["OBV"] = IndicatorConfig(
            name="OBV",
            category=IndicatorCategory.VOLUME,
            parameters={},
            timeframes=self.default_timeframes,
            description="能量潮指标",
            weight=1.1
        )
        
        # 成交量移动平均
        indicators["VOLUME_SMA"] = IndicatorConfig(
            name="VOLUME_SMA",
            category=IndicatorCategory.VOLUME,
            parameters={"timeperiod": 20},
            timeframes=self.default_timeframes,
            description="成交量简单移动平均",
            weight=1.0
        )
        
        # VWAP成交量加权平均价
        indicators["VWAP"] = IndicatorConfig(
            name="VWAP",
            category=IndicatorCategory.VOLUME,
            parameters={},
            timeframes=[TimeFrame.M5, TimeFrame.M15, TimeFrame.H1],  # VWAP通常用于较长周期
            description="成交量加权平均价",
            weight=1.5
        )
        
        # =============================================================================
        # 支撑阻力指标配置
        # =============================================================================
        
        # 枢轴点
        indicators["PIVOT_POINT"] = IndicatorConfig(
            name="PIVOT_POINT",
            category=IndicatorCategory.SUPPORT_RESISTANCE,
            parameters={},
            timeframes=[TimeFrame.H1, TimeFrame.D1],  # 枢轴点通常用于日内交易
            description="枢轴点支撑阻力",
            weight=1.3
        )
        
        # 斐波那契回调
        indicators["FIBONACCI_RETRACEMENT"] = IndicatorConfig(
            name="FIBONACCI_RETRACEMENT",
            category=IndicatorCategory.SUPPORT_RESISTANCE,
            parameters={
                "levels": [0.236, 0.382, 0.5, 0.618, 0.786]
            },
            timeframes=self.default_timeframes,
            description="斐波那契回调位",
            weight=1.2
        )
        
        return indicators
    
    def get_indicators_by_category(self, category: IndicatorCategory) -> Dict[str, IndicatorConfig]:
        """根据分类获取指标配置
        
        Args:
            category: 指标分类
        
        Returns:
            Dict[str, IndicatorConfig]: 指定分类的指标配置
        """
        return {
            name: config for name, config in self.indicators.items()
            if config.category == category and config.enabled
        }
    
    def get_indicators_by_timeframe(self, timeframe: TimeFrame) -> Dict[str, IndicatorConfig]:
        """根据时间周期获取指标配置
        
        Args:
            timeframe: 时间周期
        
        Returns:
            Dict[str, IndicatorConfig]: 指定时间周期的指标配置
        """
        return {
            name: config for name, config in self.indicators.items()
            if timeframe in config.timeframes and config.enabled
        }
    
    def get_enabled_indicators(self) -> Dict[str, IndicatorConfig]:
        """获取所有启用的指标配置
        
        Returns:
            Dict[str, IndicatorConfig]: 启用的指标配置
        """
        return {
            name: config for name, config in self.indicators.items()
            if config.enabled
        }
    
    def enable_indicator(self, indicator_name: str) -> bool:
        """启用指标
        
        Args:
            indicator_name: 指标名称
        
        Returns:
            bool: 是否成功启用
        """
        if indicator_name in self.indicators:
            self.indicators[indicator_name].enabled = True
            return True
        return False
    
    def disable_indicator(self, indicator_name: str) -> bool:
        """禁用指标
        
        Args:
            indicator_name: 指标名称
        
        Returns:
            bool: 是否成功禁用
        """
        if indicator_name in self.indicators:
            self.indicators[indicator_name].enabled = False
            return True
        return False
    
    def update_indicator_parameters(self, indicator_name: str, parameters: Dict[str, Any]) -> bool:
        """更新指标参数
        
        Args:
            indicator_name: 指标名称
            parameters: 新的参数
        
        Returns:
            bool: 是否成功更新
        """
        if indicator_name in self.indicators:
            self.indicators[indicator_name].parameters.update(parameters)
            return True
        return False
    
    def get_indicator_config_summary(self) -> Dict[str, Any]:
        """获取指标配置摘要
        
        Returns:
            Dict[str, Any]: 指标配置摘要
        """
        summary = {
            "total_indicators": len(self.indicators),
            "enabled_indicators": len(self.get_enabled_indicators()),
            "categories": {}
        }
        
        for category in IndicatorCategory:
            category_indicators = self.get_indicators_by_category(category)
            summary["categories"][category.value] = {
                "count": len(category_indicators),
                "indicators": list(category_indicators.keys())
            }
        
        return summary


# 全局指标配置实例
indicators_config = IndicatorsConfig()


def get_indicators_config() -> IndicatorsConfig:
    """获取技术指标配置实例
    
    Returns:
        IndicatorsConfig: 技术指标配置实例
    """
    return indicators_config


# 预定义的指标组合
TREND_INDICATORS = [
    "SMA_20", "SMA_50", "EMA_12", "EMA_26", "MACD", "ADX"
]

OSCILLATOR_INDICATORS = [
    "RSI_14", "STOCH_K", "STOCH_D", "WILLIAMS_R", "CCI"
]

VOLATILITY_INDICATORS = [
    "BB_UPPER", "BB_LOWER", "ATR", "KELTNER_UPPER", "KELTNER_LOWER"
]

VOLUME_INDICATORS = [
    "OBV", "VOLUME_SMA", "VWAP"
]

SUPPORT_RESISTANCE_INDICATORS = [
    "PIVOT_POINT", "FIBONACCI_RETRACEMENT"
]

# 所有指标列表
ALL_INDICATORS = (
    TREND_INDICATORS + 
    OSCILLATOR_INDICATORS + 
    VOLATILITY_INDICATORS + 
    VOLUME_INDICATORS + 
    SUPPORT_RESISTANCE_INDICATORS
)
