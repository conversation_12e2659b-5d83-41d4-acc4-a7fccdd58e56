#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统技术分析计算引擎

此模块负责计算各种技术指标，包括：
1. 趋势指标：SMA、EMA、MACD、ADX
2. 震荡指标：RSI、Stochastic、Williams %R、CCI
3. 波动率指标：Bollinger Bands、ATR、Keltner Channel
4. 成交量指标：OBV、Volume SMA、VWAP
5. 支撑阻力：Pivot Points、Fibonacci回调位
"""

import numpy as np
import talib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd

from src.data.models import OHLCVData, TechnicalIndicators
from src.utils.logger import get_logger, log_execution_time
from src.utils.exceptions import DataInsufficientError

logger = get_logger(__name__)


@dataclass
class IndicatorConfig:
    """技术指标配置"""
    # 趋势指标参数
    sma_periods: List[int] = None
    ema_periods: List[int] = None
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    adx_period: int = 14
    
    # 震荡指标参数
    rsi_period: int = 14
    stoch_k_period: int = 14
    stoch_d_period: int = 3
    williams_period: int = 14
    cci_period: int = 14
    
    # 波动率指标参数
    bb_period: int = 20
    bb_std: float = 2.0
    atr_period: int = 14
    keltner_period: int = 20
    keltner_multiplier: float = 2.0
    
    # 成交量指标参数
    volume_sma_period: int = 20
    
    def __post_init__(self):
        """初始化默认值"""
        if self.sma_periods is None:
            self.sma_periods = [20, 50, 200]
        if self.ema_periods is None:
            self.ema_periods = [12, 26, 50]


class TechnicalAnalysisEngine:
    """技术分析计算引擎类
    
    负责计算各种技术指标并格式化为AI可理解的数据。
    """
    
    def __init__(self, config: Optional[IndicatorConfig] = None):
        """初始化技术分析引擎
        
        Args:
            config: 指标配置，如果为None则使用默认配置
        """
        self.config = config or IndicatorConfig()
        
        # 最小数据量要求
        self.min_data_requirements = {
            'sma': max(self.config.sma_periods),
            'ema': max(self.config.ema_periods),
            'macd': self.config.macd_slow + self.config.macd_signal,
            'adx': self.config.adx_period * 2,
            'rsi': self.config.rsi_period * 2,
            'stoch': self.config.stoch_k_period + self.config.stoch_d_period,
            'williams': self.config.williams_period,
            'cci': self.config.cci_period,
            'bb': self.config.bb_period,
            'atr': self.config.atr_period,
            'keltner': self.config.keltner_period,
            'volume': self.config.volume_sma_period
        }
    
    def _validate_data(self, ohlcv_data: List[OHLCVData], min_periods: int, data_type: str = "K线") -> List[OHLCVData]:
        """验证和转换数据

        Args:
            ohlcv_data: OHLCV数据列表
            min_periods: 最小周期数
            data_type: 数据类型描述

        Returns:
            List[OHLCVData]: 验证后的数据

        Raises:
            DataInsufficientError: 数据不足时抛出异常
        """
        if len(ohlcv_data) < min_periods:
            raise DataInsufficientError(
                required_count=min_periods,
                available_count=len(ohlcv_data),
                data_type=data_type
            )

        return ohlcv_data
    
    def _extract_price_arrays(self, ohlcv_data: List[OHLCVData]) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """提取价格数组
        
        Args:
            ohlcv_data: OHLCV数据列表
        
        Returns:
            Tuple[np.ndarray, ...]: (open, high, low, close, volume)
        """
        opens = np.array([candle.open for candle in ohlcv_data], dtype=np.float64)
        highs = np.array([candle.high for candle in ohlcv_data], dtype=np.float64)
        lows = np.array([candle.low for candle in ohlcv_data], dtype=np.float64)
        closes = np.array([candle.close for candle in ohlcv_data], dtype=np.float64)
        volumes = np.array([candle.volume for candle in ohlcv_data], dtype=np.float64)
        
        return opens, highs, lows, closes, volumes
    
    @log_execution_time("计算趋势指标")
    def calculate_trend_indicators(self, ohlcv_data: List[OHLCVData]) -> Dict[str, Any]:
        """计算趋势指标
        
        Args:
            ohlcv_data: OHLCV数据列表
        
        Returns:
            Dict[str, Any]: 趋势指标结果
        """
        try:
            # 验证数据
            min_periods = max(self.min_data_requirements['sma'], 
                            self.min_data_requirements['ema'],
                            self.min_data_requirements['macd'],
                            self.min_data_requirements['adx'])
            
            self._validate_data(ohlcv_data, min_periods, "趋势指标")
            opens, highs, lows, closes, volumes = self._extract_price_arrays(ohlcv_data)
            
            indicators = {}
            
            # 简单移动平均线 (SMA)
            for period in self.config.sma_periods:
                sma = talib.SMA(closes, timeperiod=period)
                indicators[f'SMA_{period}'] = float(sma[-1]) if not np.isnan(sma[-1]) else None
            
            # 指数移动平均线 (EMA)
            for period in self.config.ema_periods:
                ema = talib.EMA(closes, timeperiod=period)
                indicators[f'EMA_{period}'] = float(ema[-1]) if not np.isnan(ema[-1]) else None
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(closes, 
                                                     fastperiod=self.config.macd_fast,
                                                     slowperiod=self.config.macd_slow,
                                                     signalperiod=self.config.macd_signal)
            
            indicators['MACD'] = float(macd[-1]) if not np.isnan(macd[-1]) else None
            indicators['MACD_SIGNAL'] = float(macd_signal[-1]) if not np.isnan(macd_signal[-1]) else None
            indicators['MACD_HIST'] = float(macd_hist[-1]) if not np.isnan(macd_hist[-1]) else None
            
            # ADX (平均趋向指数)
            adx = talib.ADX(highs, lows, closes, timeperiod=self.config.adx_period)
            indicators['ADX'] = float(adx[-1]) if not np.isnan(adx[-1]) else None
            
            # +DI 和 -DI
            plus_di = talib.PLUS_DI(highs, lows, closes, timeperiod=self.config.adx_period)
            minus_di = talib.MINUS_DI(highs, lows, closes, timeperiod=self.config.adx_period)
            indicators['PLUS_DI'] = float(plus_di[-1]) if not np.isnan(plus_di[-1]) else None
            indicators['MINUS_DI'] = float(minus_di[-1]) if not np.isnan(minus_di[-1]) else None
            
            logger.debug(f"趋势指标计算完成，包含 {len(indicators)} 个指标")
            return indicators
            
        except Exception as e:
            logger.error(f"计算趋势指标失败: {e}")
            return {}
    
    @log_execution_time("计算震荡指标")
    def calculate_oscillator_indicators(self, ohlcv_data: List[OHLCVData]) -> Dict[str, Any]:
        """计算震荡指标
        
        Args:
            ohlcv_data: OHLCV数据列表
        
        Returns:
            Dict[str, Any]: 震荡指标结果
        """
        try:
            # 验证数据
            min_periods = max(self.min_data_requirements['rsi'],
                            self.min_data_requirements['stoch'],
                            self.min_data_requirements['williams'],
                            self.min_data_requirements['cci'])
            
            self._validate_data(ohlcv_data, min_periods, "震荡指标")
            opens, highs, lows, closes, volumes = self._extract_price_arrays(ohlcv_data)
            
            indicators = {}
            
            # RSI (相对强弱指数)
            rsi = talib.RSI(closes, timeperiod=self.config.rsi_period)
            indicators['RSI'] = float(rsi[-1]) if not np.isnan(rsi[-1]) else None
            
            # Stochastic (随机指标)
            stoch_k, stoch_d = talib.STOCH(highs, lows, closes,
                                          fastk_period=self.config.stoch_k_period,
                                          slowk_period=self.config.stoch_d_period,
                                          slowd_period=self.config.stoch_d_period)
            
            indicators['STOCH_K'] = float(stoch_k[-1]) if not np.isnan(stoch_k[-1]) else None
            indicators['STOCH_D'] = float(stoch_d[-1]) if not np.isnan(stoch_d[-1]) else None
            
            # Williams %R
            williams_r = talib.WILLR(highs, lows, closes, timeperiod=self.config.williams_period)
            indicators['WILLIAMS_R'] = float(williams_r[-1]) if not np.isnan(williams_r[-1]) else None
            
            # CCI (商品通道指数)
            cci = talib.CCI(highs, lows, closes, timeperiod=self.config.cci_period)
            indicators['CCI'] = float(cci[-1]) if not np.isnan(cci[-1]) else None
            
            logger.debug(f"震荡指标计算完成，包含 {len(indicators)} 个指标")
            return indicators
            
        except Exception as e:
            logger.error(f"计算震荡指标失败: {e}")
            return {}
    
    @log_execution_time("计算波动率指标")
    def calculate_volatility_indicators(self, ohlcv_data: List[OHLCVData]) -> Dict[str, Any]:
        """计算波动率指标
        
        Args:
            ohlcv_data: OHLCV数据列表
        
        Returns:
            Dict[str, Any]: 波动率指标结果
        """
        try:
            # 验证数据
            min_periods = max(self.min_data_requirements['bb'],
                            self.min_data_requirements['atr'],
                            self.min_data_requirements['keltner'])
            
            self._validate_data(ohlcv_data, min_periods, "波动率指标")
            opens, highs, lows, closes, volumes = self._extract_price_arrays(ohlcv_data)
            
            indicators = {}
            
            # Bollinger Bands (布林带)
            bb_upper, bb_middle, bb_lower = talib.BBANDS(closes,
                                                        timeperiod=self.config.bb_period,
                                                        nbdevup=self.config.bb_std,
                                                        nbdevdn=self.config.bb_std)
            
            indicators['BB_UPPER'] = float(bb_upper[-1]) if not np.isnan(bb_upper[-1]) else None
            indicators['BB_MIDDLE'] = float(bb_middle[-1]) if not np.isnan(bb_middle[-1]) else None
            indicators['BB_LOWER'] = float(bb_lower[-1]) if not np.isnan(bb_lower[-1]) else None
            
            # 计算布林带宽度和位置
            if indicators['BB_UPPER'] and indicators['BB_LOWER']:
                bb_width = (indicators['BB_UPPER'] - indicators['BB_LOWER']) / indicators['BB_MIDDLE']
                indicators['BB_WIDTH'] = bb_width
                
                current_price = float(closes[-1])
                bb_position = (current_price - indicators['BB_LOWER']) / (indicators['BB_UPPER'] - indicators['BB_LOWER'])
                indicators['BB_POSITION'] = bb_position
            
            # ATR (真实波动幅度)
            atr = talib.ATR(highs, lows, closes, timeperiod=self.config.atr_period)
            indicators['ATR'] = float(atr[-1]) if not np.isnan(atr[-1]) else None
            
            # Keltner Channel (肯特纳通道)
            ema_keltner = talib.EMA(closes, timeperiod=self.config.keltner_period)
            atr_keltner = talib.ATR(highs, lows, closes, timeperiod=self.config.keltner_period)
            
            if not np.isnan(ema_keltner[-1]) and not np.isnan(atr_keltner[-1]):
                keltner_middle = float(ema_keltner[-1])
                keltner_offset = float(atr_keltner[-1]) * self.config.keltner_multiplier
                
                indicators['KELTNER_UPPER'] = keltner_middle + keltner_offset
                indicators['KELTNER_MIDDLE'] = keltner_middle
                indicators['KELTNER_LOWER'] = keltner_middle - keltner_offset
            
            logger.debug(f"波动率指标计算完成，包含 {len(indicators)} 个指标")
            return indicators
            
        except Exception as e:
            logger.error(f"计算波动率指标失败: {e}")
            return {}

    @log_execution_time("计算成交量指标")
    def calculate_volume_indicators(self, ohlcv_data: List[OHLCVData]) -> Dict[str, Any]:
        """计算成交量指标

        Args:
            ohlcv_data: OHLCV数据列表

        Returns:
            Dict[str, Any]: 成交量指标结果
        """
        try:
            # 验证数据
            min_periods = self.min_data_requirements['volume']
            self._validate_data(ohlcv_data, min_periods, "成交量指标")
            opens, highs, lows, closes, volumes = self._extract_price_arrays(ohlcv_data)

            indicators = {}

            # OBV (能量潮)
            obv = talib.OBV(closes, volumes)
            indicators['OBV'] = float(obv[-1]) if not np.isnan(obv[-1]) else None

            # Volume SMA (成交量移动平均)
            volume_sma = talib.SMA(volumes, timeperiod=self.config.volume_sma_period)
            indicators['VOLUME_SMA'] = float(volume_sma[-1]) if not np.isnan(volume_sma[-1]) else None

            # 当前成交量
            indicators['VOLUME'] = float(volumes[-1])

            # 成交量比率
            if indicators['VOLUME_SMA'] and indicators['VOLUME_SMA'] > 0:
                indicators['VOLUME_RATIO'] = indicators['VOLUME'] / indicators['VOLUME_SMA']

            # VWAP (成交量加权平均价) - 简化版本
            if len(ohlcv_data) >= 20:  # 使用最近20个周期计算VWAP
                recent_data = ohlcv_data[-20:]
                total_volume = sum(candle.volume for candle in recent_data)
                if total_volume > 0:
                    vwap = sum(candle.close * candle.volume for candle in recent_data) / total_volume
                    indicators['VWAP'] = float(vwap)

            logger.debug(f"成交量指标计算完成，包含 {len(indicators)} 个指标")
            return indicators

        except Exception as e:
            logger.error(f"计算成交量指标失败: {e}")
            return {}

    @log_execution_time("计算支撑阻力")
    def calculate_support_resistance(self, ohlcv_data: List[OHLCVData]) -> Dict[str, Any]:
        """计算支撑阻力位

        Args:
            ohlcv_data: OHLCV数据列表

        Returns:
            Dict[str, Any]: 支撑阻力位结果
        """
        try:
            if len(ohlcv_data) < 3:
                return {}

            # 获取最新的价格数据
            latest = ohlcv_data[-1]
            high = latest.high
            low = latest.low
            close = latest.close

            indicators = {}

            # Pivot Points (枢轴点)
            pivot = (high + low + close) / 3
            indicators['PIVOT_POINT'] = float(pivot)

            # 支撑位和阻力位
            indicators['RESISTANCE_1'] = float(2 * pivot - low)
            indicators['SUPPORT_1'] = float(2 * pivot - high)
            indicators['RESISTANCE_2'] = float(pivot + (high - low))
            indicators['SUPPORT_2'] = float(pivot - (high - low))
            indicators['RESISTANCE_3'] = float(high + 2 * (pivot - low))
            indicators['SUPPORT_3'] = float(low - 2 * (high - pivot))

            # Fibonacci回调位 (基于最近的高低点)
            if len(ohlcv_data) >= 20:
                recent_data = ohlcv_data[-20:]
                recent_high = max(candle.high for candle in recent_data)
                recent_low = min(candle.low for candle in recent_data)

                fib_range = recent_high - recent_low

                indicators['FIB_0'] = float(recent_high)
                indicators['FIB_23_6'] = float(recent_high - fib_range * 0.236)
                indicators['FIB_38_2'] = float(recent_high - fib_range * 0.382)
                indicators['FIB_50'] = float(recent_high - fib_range * 0.5)
                indicators['FIB_61_8'] = float(recent_high - fib_range * 0.618)
                indicators['FIB_100'] = float(recent_low)

            logger.debug(f"支撑阻力计算完成，包含 {len(indicators)} 个指标")
            return indicators

        except Exception as e:
            logger.error(f"计算支撑阻力失败: {e}")
            return {}

    @log_execution_time("多时间周期分析")
    def analyze_multi_timeframe(self, market_data: Dict[str, List[OHLCVData]]) -> Dict[str, TechnicalIndicators]:
        """分析多时间周期技术指标

        Args:
            market_data: 多时间周期市场数据

        Returns:
            Dict[str, TechnicalIndicators]: 各时间周期的技术指标
        """
        try:
            results = {}

            for timeframe, ohlcv_data in market_data.items():
                if not ohlcv_data:
                    logger.warning(f"时间周期 {timeframe} 数据为空，跳过分析")
                    continue

                try:
                    # 计算各类指标
                    trend_indicators = self.calculate_trend_indicators(ohlcv_data)
                    oscillator_indicators = self.calculate_oscillator_indicators(ohlcv_data)
                    volatility_indicators = self.calculate_volatility_indicators(ohlcv_data)
                    volume_indicators = self.calculate_volume_indicators(ohlcv_data)
                    support_resistance = self.calculate_support_resistance(ohlcv_data)

                    # 创建技术指标对象
                    technical_indicators = TechnicalIndicators(
                        symbol=market_data.get('symbol', 'UNKNOWN'),
                        timeframe=timeframe,
                        trend_indicators=trend_indicators,
                        oscillator_indicators=oscillator_indicators,
                        volatility_indicators=volatility_indicators,
                        volume_indicators=volume_indicators,
                        support_resistance=support_resistance,
                        calculated_at=int(ohlcv_data[-1].timestamp / 1000) if ohlcv_data else 0
                    )

                    results[timeframe] = technical_indicators
                    logger.info(f"完成 {timeframe} 时间周期技术分析")

                except Exception as e:
                    logger.error(f"分析 {timeframe} 时间周期失败: {e}")
                    continue

            logger.info(f"多时间周期分析完成，成功分析 {len(results)} 个时间周期")
            return results

        except Exception as e:
            logger.error(f"多时间周期分析失败: {e}")
            return {}

    def format_for_ai(self, indicators: Dict[str, TechnicalIndicators]) -> str:
        """格式化技术指标为AI可理解的文本

        Args:
            indicators: 技术指标字典

        Returns:
            str: 格式化后的文本
        """
        try:
            if not indicators:
                return "无技术指标数据"

            formatted_text = []

            for timeframe, tech_indicators in indicators.items():
                formatted_text.append(f"\n=== {timeframe} 时间周期技术分析 ===")

                # 趋势指标
                if tech_indicators.trend_indicators:
                    formatted_text.append("\n【趋势指标】")
                    for key, value in tech_indicators.trend_indicators.items():
                        if value is not None:
                            formatted_text.append(f"{key}: {value:.4f}")

                # 震荡指标
                if tech_indicators.oscillator_indicators:
                    formatted_text.append("\n【震荡指标】")
                    for key, value in tech_indicators.oscillator_indicators.items():
                        if value is not None:
                            formatted_text.append(f"{key}: {value:.2f}")

                # 波动率指标
                if tech_indicators.volatility_indicators:
                    formatted_text.append("\n【波动率指标】")
                    for key, value in tech_indicators.volatility_indicators.items():
                        if value is not None:
                            formatted_text.append(f"{key}: {value:.4f}")

                # 成交量指标
                if tech_indicators.volume_indicators:
                    formatted_text.append("\n【成交量指标】")
                    for key, value in tech_indicators.volume_indicators.items():
                        if value is not None:
                            formatted_text.append(f"{key}: {value:.2f}")

                # 支撑阻力
                if tech_indicators.support_resistance:
                    formatted_text.append("\n【支撑阻力位】")
                    for key, value in tech_indicators.support_resistance.items():
                        if value is not None:
                            formatted_text.append(f"{key}: {value:.4f}")

            return "\n".join(formatted_text)

        except Exception as e:
            logger.error(f"格式化技术指标失败: {e}")
            return "技术指标格式化失败"

    def get_signal_summary(self, indicators: Dict[str, TechnicalIndicators]) -> Dict[str, str]:
        """获取技术指标信号摘要

        Args:
            indicators: 技术指标字典

        Returns:
            Dict[str, str]: 各时间周期的信号摘要
        """
        try:
            summaries = {}

            for timeframe, tech_indicators in indicators.items():
                signals = []

                # 分析趋势信号
                trend = tech_indicators.trend_indicators
                if trend:
                    # MACD信号
                    if trend.get('MACD') and trend.get('MACD_SIGNAL'):
                        if trend['MACD'] > trend['MACD_SIGNAL']:
                            signals.append("MACD看涨")
                        else:
                            signals.append("MACD看跌")

                    # ADX信号
                    if trend.get('ADX'):
                        if trend['ADX'] > 25:
                            signals.append("趋势强劲")
                        elif trend['ADX'] < 20:
                            signals.append("趋势疲弱")

                # 分析震荡信号
                osc = tech_indicators.oscillator_indicators
                if osc:
                    # RSI信号
                    if osc.get('RSI'):
                        if osc['RSI'] > 70:
                            signals.append("RSI超买")
                        elif osc['RSI'] < 30:
                            signals.append("RSI超卖")

                    # Stochastic信号
                    if osc.get('STOCH_K') and osc.get('STOCH_D'):
                        if osc['STOCH_K'] > 80:
                            signals.append("随机指标超买")
                        elif osc['STOCH_K'] < 20:
                            signals.append("随机指标超卖")

                summaries[timeframe] = "; ".join(signals) if signals else "无明显信号"

            return summaries

        except Exception as e:
            logger.error(f"获取信号摘要失败: {e}")
            return {}
