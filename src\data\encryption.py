#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统数据加密工具模块

此模块提供数据加密和解密功能，主要用于：
1. API密钥加密存储
2. 敏感配置数据加密
3. 数据库敏感字段加密
4. 密钥管理和验证
"""

import os
import base64
import hashlib
from typing import Dict, Any, Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from src.utils.exceptions import DataValidationError
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EncryptionManager:
    """加密管理器类
    
    负责管理系统中的所有加密和解密操作。
    """
    
    def __init__(self, encryption_key: Optional[str] = None):
        """初始化加密管理器
        
        Args:
            encryption_key: 加密密钥，如果为None则生成新密钥
        """
        self.encryption_key = encryption_key
        self._fernet = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """初始化加密组件"""
        try:
            if self.encryption_key:
                # 使用提供的密钥
                if isinstance(self.encryption_key, str):
                    if len(self.encryption_key) == 32:
                        # 如果是32字符的字符串，直接编码为字节
                        key = self.encryption_key.encode('utf-8')
                    else:
                        # 尝试作为base64解码
                        try:
                            key = base64.urlsafe_b64decode(self.encryption_key.encode())
                            if len(key) != 32:
                                raise ValueError("解码后的密钥长度不是32字节")
                        except:
                            raise DataValidationError(
                                "encryption_key",
                                self.encryption_key,
                                "密钥格式无效，必须是32字符字符串或有效的base64编码"
                            )
                else:
                    key = self.encryption_key
            else:
                # 生成新密钥
                key = self._generate_key()
                self.encryption_key = base64.urlsafe_b64encode(key).decode()

            # 确保密钥是32字节
            if len(key) != 32:
                raise DataValidationError(
                    "encryption_key",
                    str(key),
                    f"密钥长度必须为32字节，当前为{len(key)}字节"
                )

            # 创建Fernet实例
            fernet_key = base64.urlsafe_b64encode(key)
            self._fernet = Fernet(fernet_key)

            logger.info("加密管理器初始化成功")

        except Exception as e:
            logger.error(f"加密管理器初始化失败: {e}")
            raise
    
    def _generate_key(self) -> bytes:
        """生成32字节的加密密钥

        Returns:
            bytes: 32字节的密钥
        """
        return os.urandom(32)
    
    def generate_key_from_password(self, password: str, salt: Optional[bytes] = None) -> str:
        """从密码生成加密密钥

        Args:
            password: 密码
            salt: 盐值，如果为None则生成新盐值

        Returns:
            str: 生成的密钥（base64编码）
        """
        if salt is None:
            salt = os.urandom(16)

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = kdf.derive(password.encode())
        return base64.urlsafe_b64encode(key).decode()
    
    def encrypt_string(self, plaintext: str) -> str:
        """加密字符串
        
        Args:
            plaintext: 明文字符串
        
        Returns:
            str: 加密后的字符串（Base64编码）
        """
        try:
            if not plaintext:
                return ""
            
            encrypted_data = self._fernet.encrypt(plaintext.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
            
        except Exception as e:
            logger.error(f"字符串加密失败: {e}")
            raise DataValidationError("plaintext", plaintext, f"加密失败: {e}")
    
    def decrypt_string(self, ciphertext: str) -> str:
        """解密字符串
        
        Args:
            ciphertext: 密文字符串（Base64编码）
        
        Returns:
            str: 解密后的明文字符串
        """
        try:
            if not ciphertext:
                return ""
            
            encrypted_data = base64.urlsafe_b64decode(ciphertext.encode())
            decrypted_data = self._fernet.decrypt(encrypted_data)
            return decrypted_data.decode()
            
        except Exception as e:
            logger.error(f"字符串解密失败: {e}")
            raise DataValidationError("ciphertext", ciphertext, f"解密失败: {e}")
    
    def encrypt_dict(self, data: Dict[str, Any], fields_to_encrypt: list) -> Dict[str, Any]:
        """加密字典中的指定字段
        
        Args:
            data: 要加密的字典
            fields_to_encrypt: 需要加密的字段列表
        
        Returns:
            Dict[str, Any]: 加密后的字典
        """
        try:
            encrypted_data = data.copy()
            
            for field in fields_to_encrypt:
                if field in encrypted_data and encrypted_data[field]:
                    encrypted_data[field] = self.encrypt_string(str(encrypted_data[field]))
            
            return encrypted_data
            
        except Exception as e:
            logger.error(f"字典加密失败: {e}")
            raise DataValidationError("data", str(data), f"字典加密失败: {e}")
    
    def decrypt_dict(self, encrypted_data: Dict[str, Any], fields_to_decrypt: list) -> Dict[str, Any]:
        """解密字典中的指定字段
        
        Args:
            encrypted_data: 加密的字典
            fields_to_decrypt: 需要解密的字段列表
        
        Returns:
            Dict[str, Any]: 解密后的字典
        """
        try:
            decrypted_data = encrypted_data.copy()
            
            for field in fields_to_decrypt:
                if field in decrypted_data and decrypted_data[field]:
                    decrypted_data[field] = self.decrypt_string(decrypted_data[field])
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"字典解密失败: {e}")
            raise DataValidationError("encrypted_data", str(encrypted_data), f"字典解密失败: {e}")
    
    def hash_string(self, text: str, salt: Optional[str] = None) -> str:
        """对字符串进行哈希
        
        Args:
            text: 要哈希的文本
            salt: 盐值
        
        Returns:
            str: 哈希值
        """
        try:
            if salt:
                text_with_salt = text + salt
            else:
                text_with_salt = text
            
            hash_object = hashlib.sha256(text_with_salt.encode())
            return hash_object.hexdigest()
            
        except Exception as e:
            logger.error(f"字符串哈希失败: {e}")
            raise DataValidationError("text", text, f"哈希失败: {e}")
    
    def verify_hash(self, text: str, hash_value: str, salt: Optional[str] = None) -> bool:
        """验证哈希值
        
        Args:
            text: 原始文本
            hash_value: 哈希值
            salt: 盐值
        
        Returns:
            bool: 验证是否通过
        """
        try:
            computed_hash = self.hash_string(text, salt)
            return computed_hash == hash_value
            
        except Exception as e:
            logger.error(f"哈希验证失败: {e}")
            return False
    
    def get_encryption_key(self) -> str:
        """获取当前加密密钥
        
        Returns:
            str: 加密密钥
        """
        return self.encryption_key
    
    def is_encrypted(self, text: str) -> bool:
        """检查文本是否已加密
        
        Args:
            text: 要检查的文本
        
        Returns:
            bool: 是否已加密
        """
        try:
            # 尝试解密，如果成功则说明是加密的
            self.decrypt_string(text)
            return True
        except:
            return False


class APIKeyManager:
    """API密钥管理器
    
    专门用于管理交易所API密钥的加密存储。
    """
    
    def __init__(self, encryption_manager: EncryptionManager):
        """初始化API密钥管理器
        
        Args:
            encryption_manager: 加密管理器实例
        """
        self.encryption_manager = encryption_manager
        self.sensitive_fields = ["api_key", "secret_key", "passphrase", "deepseek_api_key"]
    
    def encrypt_api_config(self, api_config: Dict[str, Any]) -> Dict[str, Any]:
        """加密API配置
        
        Args:
            api_config: API配置字典
        
        Returns:
            Dict[str, Any]: 加密后的API配置
        """
        try:
            encrypted_config = self.encryption_manager.encrypt_dict(
                api_config, 
                self.sensitive_fields
            )
            
            # 添加加密标记
            encrypted_config["_encrypted"] = True
            encrypted_config["_encryption_version"] = "1.0"
            
            # 确定配置类型用于日志
            config_type = api_config.get('exchange_name', api_config.get('deepseek_api_key', 'unknown'))
            if config_type.startswith('sk-'):
                config_type = 'ai_config'
            logger.info(f"API配置加密成功: {config_type}")
            return encrypted_config
            
        except Exception as e:
            logger.error(f"API配置加密失败: {e}")
            raise
    
    def decrypt_api_config(self, encrypted_config: Dict[str, Any]) -> Dict[str, Any]:
        """解密API配置
        
        Args:
            encrypted_config: 加密的API配置字典
        
        Returns:
            Dict[str, Any]: 解密后的API配置
        """
        try:
            # 检查是否已加密
            if not encrypted_config.get("_encrypted", False):
                logger.warning("API配置未加密，直接返回")
                return encrypted_config
            
            decrypted_config = self.encryption_manager.decrypt_dict(
                encrypted_config, 
                self.sensitive_fields
            )
            
            # 移除加密标记
            decrypted_config.pop("_encrypted", None)
            decrypted_config.pop("_encryption_version", None)
            
            # 确定配置类型用于日志
            config_type = decrypted_config.get('exchange_name', decrypted_config.get('deepseek_api_key', 'unknown'))
            if config_type.startswith('sk-'):
                config_type = 'ai_config'
            logger.info(f"API配置解密成功: {config_type}")
            return decrypted_config
            
        except Exception as e:
            logger.error(f"API配置解密失败: {e}")
            raise
    
    def validate_api_config(self, api_config: Dict[str, Any]) -> bool:
        """验证API配置的完整性

        Args:
            api_config: API配置字典

        Returns:
            bool: 验证是否通过
        """
        try:
            # 根据配置类型确定必需字段
            if "exchange_name" in api_config:
                # 交易所配置
                required_fields = ["exchange_name", "api_key", "secret_key"]
            elif "deepseek_api_key" in api_config:
                # AI配置
                required_fields = ["deepseek_api_key"]
            else:
                logger.error("无法识别的API配置类型")
                return False

            for field in required_fields:
                if field not in api_config or not api_config[field]:
                    logger.error(f"API配置缺少必需字段: {field}")
                    return False
            
            # 检查密钥长度
            if len(api_config["api_key"]) < 10:
                logger.error("API密钥长度过短")
                return False
            
            if len(api_config["secret_key"]) < 10:
                logger.error("Secret密钥长度过短")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"API配置验证失败: {e}")
            return False


# =============================================================================
# 便捷函数
# =============================================================================

def create_encryption_manager(encryption_key: Optional[str] = None) -> EncryptionManager:
    """创建加密管理器实例
    
    Args:
        encryption_key: 加密密钥
    
    Returns:
        EncryptionManager: 加密管理器实例
    """
    return EncryptionManager(encryption_key)


def create_api_key_manager(encryption_key: Optional[str] = None) -> APIKeyManager:
    """创建API密钥管理器实例
    
    Args:
        encryption_key: 加密密钥
    
    Returns:
        APIKeyManager: API密钥管理器实例
    """
    encryption_manager = create_encryption_manager(encryption_key)
    return APIKeyManager(encryption_manager)


def generate_encryption_key() -> str:
    """生成新的加密密钥

    Returns:
        str: base64编码的加密密钥
    """
    key = os.urandom(32)
    return base64.urlsafe_b64encode(key).decode()


def test_encryption(encryption_key: Optional[str] = None) -> bool:
    """测试加密功能
    
    Args:
        encryption_key: 加密密钥
    
    Returns:
        bool: 测试是否通过
    """
    try:
        manager = create_encryption_manager(encryption_key)
        
        # 测试字符串加密解密
        test_text = "test_api_key_12345"
        encrypted = manager.encrypt_string(test_text)
        decrypted = manager.decrypt_string(encrypted)
        
        if test_text != decrypted:
            logger.error("字符串加密解密测试失败")
            return False
        
        # 测试字典加密解密
        test_dict = {
            "api_key": "test_key",
            "secret_key": "test_secret",
            "exchange_name": "test_exchange"
        }
        
        encrypted_dict = manager.encrypt_dict(test_dict, ["api_key", "secret_key"])
        decrypted_dict = manager.decrypt_dict(encrypted_dict, ["api_key", "secret_key"])
        
        if test_dict != decrypted_dict:
            logger.error("字典加密解密测试失败")
            return False
        
        logger.info("加密功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"加密功能测试失败: {e}")
        return False
