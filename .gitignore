# DeepSeek量化交易系统 Git忽略文件

# =============================================================================
# Python相关
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# 交易系统特定文件
# =============================================================================
# 敏感配置文件
.env
*.key
*.pem
*.p12
*.pfx

# 数据库文件
*.db
*.sqlite
*.sqlite3
data/trading_system.db
data/backups/*.db

# 日志文件
logs/
*.log
*.log.*

# 临时文件
data/temp/
*.tmp
*.temp

# 备份文件
data/backups/
*.bak
*.backup

# 交易数据和缓存
cache/
*.cache
market_data_cache/

# API密钥和认证文件
api_keys/
credentials/
secrets/

# =============================================================================
# 开发工具相关
# =============================================================================
# IDE配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows相关
*.lnk

# =============================================================================
# 测试和性能分析
# =============================================================================
# 测试输出
test_output/
test_results/
.coverage
htmlcov/

# 性能分析文件
*.prof
*.profile

# =============================================================================
# 部署相关
# =============================================================================
# Docker相关
.dockerignore
docker-compose.override.yml

# 部署配置
deploy/
production.env
staging.env

# SSL证书
*.crt
*.csr
*.key
ssl/

# =============================================================================
# 其他
# =============================================================================
# 压缩文件
*.zip
*.tar.gz
*.rar

# 文档生成
docs/build/
docs/_build/

# 本地配置覆盖
local_config.py
local_settings.py
