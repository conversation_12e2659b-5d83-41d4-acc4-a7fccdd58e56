# DeepSeek量化交易系统 API 文档

## 概述

DeepSeek量化交易系统提供了完整的RESTful API接口，支持系统配置、交易管理、监控和控制等功能。

**基础URL**: `http://localhost:8000/api`

**认证方式**: 暂无（内部系统）

**数据格式**: JSON

## API 接口分类

### 1. 系统控制 API (`/api/system`)

#### 1.1 获取系统状态
```http
GET /api/system/status
```

**响应示例**:
```json
{
  "is_running": true,
  "uptime": 3600.5,
  "uptime_formatted": "1小时0分",
  "start_time": **********.0,
  "last_update": **********.0,
  "components": {
    "trading_engine": "running",
    "market_data_engine": "running",
    "ai_opening_engine": "running",
    "ai_position_engine": "running",
    "risk_manager": "running"
  },
  "emergency_mode": false,
  "health": "healthy"
}
```

#### 1.2 启动系统
```http
POST /api/system/start
```

**响应示例**:
```json
{
  "success": true,
  "message": "交易系统启动成功",
  "start_time": **********.0,
  "components": {
    "trading_engine": "running",
    "market_data_engine": "running"
  }
}
```

#### 1.3 停止系统
```http
POST /api/system/stop
```

#### 1.4 重启系统
```http
POST /api/system/restart
```

#### 1.5 紧急停止
```http
POST /api/system/emergency-stop
```

#### 1.6 系统健康检查
```http
GET /api/system/health
```

#### 1.7 获取系统性能
```http
GET /api/system/performance
```

#### 1.8 获取系统日志
```http
GET /api/system/logs?level=INFO&limit=100&module=trading_engine
```

**查询参数**:
- `level`: 日志级别 (DEBUG/INFO/WARNING/ERROR)
- `limit`: 返回数量限制 (1-1000)
- `module`: 模块筛选

### 2. 仪表板 API (`/api/dashboard`)

#### 2.1 获取仪表板概览
```http
GET /api/dashboard/overview
```

**响应示例**:
```json
{
  "system_status": {
    "is_running": true,
    "uptime": "1小时30分",
    "last_update": **********.0
  },
  "account_summary": {
    "total_balance": 50000.0,
    "available_balance": 45000.0,
    "used_margin": 5000.0,
    "unrealized_pnl": 1250.0,
    "daily_pnl": 850.0
  },
  "positions_summary": {
    "total_positions": 3,
    "long_positions": 2,
    "short_positions": 1,
    "total_exposure": 25000.0
  },
  "ai_decisions": {
    "total_decisions": 45,
    "successful_decisions": 32,
    "success_rate": 71.1,
    "last_decision_time": **********.0
  }
}
```

#### 2.2 获取账户信息
```http
GET /api/dashboard/account
```

#### 2.3 获取持仓概览
```http
GET /api/dashboard/positions
```

#### 2.4 获取交易表现
```http
GET /api/dashboard/performance
```

#### 2.5 获取AI决策历史
```http
GET /api/dashboard/ai-decisions/recent?limit=10
```

#### 2.6 获取市场数据
```http
GET /api/dashboard/market-data/{symbol}?timeframe=1h
```

#### 2.7 获取风险摘要
```http
GET /api/dashboard/risk-summary
```

### 3. 配置管理 API (`/api/config`)

#### 3.1 交易所配置

##### 获取交易所配置
```http
GET /api/config/exchange
```

##### 保存交易所配置
```http
POST /api/config/exchange
```

**请求体**:
```json
{
  "exchange_name": "okx",
  "api_key": "your_api_key",
  "secret_key": "your_secret_key",
  "passphrase": "your_passphrase",
  "sandbox_mode": true
}
```

##### 测试交易所连接
```http
POST /api/config/test-connection
```

#### 3.2 交易参数配置

##### 获取交易参数
```http
GET /api/config/trading-parameters
```

##### 保存交易参数
```http
POST /api/config/trading-parameters
```

**请求体**:
```json
{
  "max_leverage": 10,
  "max_position_ratio": 0.5,
  "opening_confidence_threshold": 70,
  "position_confidence_threshold": 60,
  "default_stop_loss_percentage": 0.05,
  "default_take_profit_percentage": 0.1
}
```

#### 3.3 风险参数配置

##### 获取风险参数
```http
GET /api/config/risk-parameters
```

##### 保存风险参数
```http
POST /api/config/risk-parameters
```

#### 3.4 交易对配置

##### 获取选择的交易对
```http
GET /api/config/symbols
```

##### 保存选择的交易对
```http
POST /api/config/symbols
```

**请求体**:
```json
{
  "symbols": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"]
}
```

##### 获取可用交易对
```http
GET /api/config/available-symbols/{exchange}
```

#### 3.5 验证规则
```http
GET /api/config/validation-rules
```

### 4. 交易管理 API (`/api/trading`)

#### 4.1 持仓管理

##### 获取当前持仓
```http
GET /api/trading/positions
```

**响应示例**:
```json
{
  "positions": [
    {
      "symbol": "BTC/USDT:USDT",
      "side": "long",
      "amount": 0.1,
      "entry_price": 50000.0,
      "current_price": 52000.0,
      "leverage": 10,
      "unrealized_pnl": 200.0,
      "unrealized_pnl_percentage": 4.0,
      "margin_used": 500.0,
      "liquidation_price": 45000.0,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ],
  "total_count": 1,
  "total_unrealized_pnl": 200.0,
  "total_margin_used": 500.0
}
```

##### 获取持仓详情
```http
GET /api/trading/positions/{symbol}
```

##### 平仓操作
```http
POST /api/trading/positions/close
```

**请求体**:
```json
{
  "symbol": "BTC/USDT:USDT",
  "amount": 0.05  // 可选，不填表示全部平仓
}
```

#### 4.2 订单管理

##### 获取订单列表
```http
GET /api/trading/orders?symbol=BTC/USDT:USDT&status=filled&limit=50
```

##### 手动下单
```http
POST /api/trading/orders/manual
```

**请求体**:
```json
{
  "symbol": "BTC/USDT:USDT",
  "side": "buy",
  "amount": 0.1,
  "order_type": "market",
  "price": 50000.0,  // 限价单需要
  "leverage": 10
}
```

##### 取消订单
```http
DELETE /api/trading/orders/{order_id}
```

#### 4.3 AI决策

##### 获取AI决策历史
```http
GET /api/trading/ai-decisions?limit=20&engine_type=opening&symbol=BTC/USDT:USDT
```

#### 4.4 交易统计

##### 获取交易统计
```http
GET /api/trading/trading-stats
```

#### 4.5 市场分析

##### 获取市场分析
```http
GET /api/trading/market-analysis/{symbol}
```

## 错误处理

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 常见错误码
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误示例
```json
{
  "detail": "不支持的交易所: invalid_exchange。支持的交易所: okx, binance, huobi, bybit"
}
```

## 数据模型

### 持仓对象
```json
{
  "symbol": "BTC/USDT:USDT",
  "side": "long",
  "amount": 0.1,
  "entry_price": 50000.0,
  "current_price": 52000.0,
  "leverage": 10,
  "unrealized_pnl": 200.0,
  "unrealized_pnl_percentage": 4.0,
  "margin_used": 500.0,
  "liquidation_price": 45000.0,
  "created_at": "2024-01-01T10:00:00Z",
  "profit_drawdown": {
    "max_profit_rate": 8.0,
    "current_profit_rate": 4.0,
    "drawdown_amount": 4.0,
    "drawdown_percentage": 50.0
  }
}
```

### AI决策对象
```json
{
  "id": "decision_123",
  "timestamp": "2024-01-01T10:00:00Z",
  "engine_type": "opening",
  "symbol": "BTC/USDT:USDT",
  "action": "open_long",
  "confidence": 85,
  "reasoning": "基于技术指标分析，RSI显示超卖，MACD金叉，建议开多头仓位",
  "suggested_leverage": 10,
  "risk_level": "medium",
  "executed": true,
  "execution_result": "success"
}
```

## 使用示例

### Python 示例
```python
import requests

# 获取系统状态
response = requests.get('http://localhost:8000/api/system/status')
status = response.json()
print(f"系统状态: {status['health']}")

# 获取持仓
response = requests.get('http://localhost:8000/api/trading/positions')
positions = response.json()
print(f"当前持仓数: {positions['total_count']}")

# 手动下单
order_data = {
    "symbol": "BTC/USDT:USDT",
    "side": "buy",
    "amount": 0.01,
    "order_type": "market",
    "leverage": 5
}
response = requests.post('http://localhost:8000/api/trading/orders/manual', json=order_data)
result = response.json()
print(f"下单结果: {result['message']}")
```

### JavaScript 示例
```javascript
// 获取仪表板概览
fetch('/api/dashboard/overview')
  .then(response => response.json())
  .then(data => {
    console.log('账户余额:', data.account_summary.total_balance);
    console.log('持仓数量:', data.positions_summary.total_positions);
  });

// 配置交易所
const exchangeConfig = {
  exchange_name: 'okx',
  api_key: 'your_api_key',
  secret_key: 'your_secret_key',
  passphrase: 'your_passphrase',
  sandbox_mode: true
};

fetch('/api/config/exchange', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(exchangeConfig)
})
.then(response => response.json())
.then(data => console.log('配置保存结果:', data.message));
```

## 注意事项

1. **安全性**: 当前版本未实现认证机制，仅适用于内部网络环境
2. **频率限制**: 部分接口可能有频率限制，请合理调用
3. **数据格式**: 所有时间戳使用Unix时间戳格式
4. **错误处理**: 请务必处理API调用可能出现的错误
5. **版本兼容**: API接口可能随版本更新而变化，请关注更新日志
