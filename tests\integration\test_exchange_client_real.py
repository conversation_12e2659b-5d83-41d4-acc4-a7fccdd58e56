#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易所客户端真实API集成测试

使用真实的OKX模拟盘API密钥进行测试。
"""

import pytest
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig
from src.utils.exceptions import ExchangeConnectionError, ExchangeAuthenticationError


@pytest.mark.integration
class TestExchangeClientReal:
    """交易所客户端真实API测试类"""
    
    @pytest.fixture
    def okx_config(self):
        """OKX模拟盘配置"""
        return ExchangeConfig(
            exchange_name="okx",
            api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
            secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
            passphrase="CAOwei00!",
            sandbox_mode=True  # 使用模拟盘
        )
    
    def test_okx_connection_real(self, okx_config):
        """测试OKX真实连接"""
        print("\n=== 测试OKX连接 ===")
        client = ExchangeClient(okx_config)
        
        try:
            # 测试连接
            result = client.connect()
            print(f"连接结果: {result}")
            assert result is True
            assert client.is_connected is True
            print("✅ OKX连接成功")
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            raise
        finally:
            client.disconnect()
    
    def test_okx_fetch_markets_real(self, okx_config):
        """测试OKX获取市场信息"""
        print("\n=== 测试获取市场信息 ===")
        client = ExchangeClient(okx_config)
        
        try:
            client.connect()
            
            # 测试获取市场信息
            markets = client.fetch_markets()
            print(f"获取到 {len(markets)} 个永续合约市场")
            
            assert isinstance(markets, list)
            assert len(markets) > 0
            
            # 显示前5个市场
            print("前5个永续合约:")
            for i, market in enumerate(markets[:5]):
                print(f"  {i+1}. {market.get('symbol', 'N/A')} - {market.get('type', 'N/A')}")
                # 验证返回的是永续合约
                assert market.get('type') == 'swap' or market.get('contract', False)
            
            print("✅ 市场信息获取成功")
            
        except Exception as e:
            print(f"❌ 获取市场信息失败: {e}")
            raise
        finally:
            client.disconnect()
    
    def test_okx_fetch_ohlcv_real(self, okx_config):
        """测试OKX获取K线数据"""
        print("\n=== 测试获取K线数据 ===")
        client = ExchangeClient(okx_config)
        
        try:
            client.connect()
            
            # 测试同步获取K线数据
            symbol = "BTC/USDT:USDT"
            timeframe = "1m"
            limit = 5
            
            print(f"获取 {symbol} {timeframe} K线数据，数量: {limit}")
            ohlcv_data = client.fetch_ohlcv_sync(symbol, timeframe, limit)
            
            assert isinstance(ohlcv_data, list)
            assert len(ohlcv_data) <= limit
            
            print(f"获取到 {len(ohlcv_data)} 条K线数据")
            
            if len(ohlcv_data) > 0:
                # 显示最新的K线数据
                latest = ohlcv_data[-1]
                print(f"最新K线: 时间={latest.datetime}, 开={latest.open}, 高={latest.high}, 低={latest.low}, 收={latest.close}, 量={latest.volume}")
                
                # 验证数据结构
                assert hasattr(latest, 'timestamp')
                assert hasattr(latest, 'open')
                assert hasattr(latest, 'high')
                assert hasattr(latest, 'low')
                assert hasattr(latest, 'close')
                assert hasattr(latest, 'volume')
                
                # 验证价格逻辑
                assert latest.high >= latest.low
                assert latest.high >= max(latest.open, latest.close)
                assert latest.low <= min(latest.open, latest.close)
                assert latest.volume >= 0
                
                print("✅ K线数据验证通过")
            else:
                print("⚠️ 未获取到K线数据")
        
        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            raise
        finally:
            client.disconnect()
    
    @pytest.mark.asyncio
    async def test_okx_async_fetch_ohlcv_real(self, okx_config):
        """测试OKX异步获取K线数据"""
        print("\n=== 测试异步获取K线数据 ===")
        client = ExchangeClient(okx_config)
        
        try:
            client.connect()
            
            # 测试异步获取K线数据
            symbol = "ETH/USDT:USDT"
            timeframe = "5m"
            limit = 3
            
            print(f"异步获取 {symbol} {timeframe} K线数据，数量: {limit}")
            ohlcv_data = await client.fetch_ohlcv(symbol, timeframe, limit)
            
            assert isinstance(ohlcv_data, list)
            assert len(ohlcv_data) <= limit
            
            print(f"异步获取到 {len(ohlcv_data)} 条K线数据")
            
            if len(ohlcv_data) > 0:
                # 验证时间戳是递增的
                timestamps = [candle.timestamp for candle in ohlcv_data]
                assert timestamps == sorted(timestamps)
                
                # 显示数据
                for i, candle in enumerate(ohlcv_data):
                    print(f"  {i+1}. {candle.datetime} - 收盘价: {candle.close}")
                
                print("✅ 异步K线数据获取成功")
            else:
                print("⚠️ 未获取到异步K线数据")
        
        except Exception as e:
            print(f"❌ 异步获取K线数据失败: {e}")
            raise
        finally:
            client.disconnect()
    
    def test_okx_fetch_balance_real(self, okx_config):
        """测试OKX获取账户余额"""
        print("\n=== 测试获取账户余额 ===")
        client = ExchangeClient(okx_config)
        
        try:
            client.connect()
            
            # 测试获取余额
            print("获取账户余额...")
            balances = client.fetch_balance()
            
            assert isinstance(balances, dict)
            print(f"获取到 {len(balances)} 个币种的余额信息")
            
            # 显示余额信息
            if balances:
                print("账户余额:")
                for currency, balance in balances.items():
                    if balance.total > 0:  # 只显示有余额的
                        print(f"  {currency}: 总计={balance.total}, 可用={balance.available}, 已用={balance.used}")
                    
                    # 验证余额结构
                    assert hasattr(balance, 'currency')
                    assert hasattr(balance, 'total')
                    assert hasattr(balance, 'available')
                    assert hasattr(balance, 'used')
                    assert balance.total >= 0
                    assert balance.available >= 0
                    assert balance.used >= 0
                
                print("✅ 余额信息获取成功")
            else:
                print("⚠️ 模拟盘账户暂无余额")
        
        except Exception as e:
            print(f"❌ 获取余额失败: {e}")
            # 如果是认证错误，说明API密钥有问题
            if "authentication" in str(e).lower() or "invalid" in str(e).lower():
                print("可能是API密钥配置问题")
            raise
        finally:
            client.disconnect()
    
    def test_okx_fetch_positions_real(self, okx_config):
        """测试OKX获取持仓信息"""
        print("\n=== 测试获取持仓信息 ===")
        client = ExchangeClient(okx_config)
        
        try:
            client.connect()
            
            # 测试获取持仓
            print("获取持仓信息...")
            positions = client.fetch_positions()
            
            assert isinstance(positions, list)
            print(f"获取到 {len(positions)} 个持仓")
            
            # 显示持仓信息
            if positions:
                print("当前持仓:")
                for position in positions:
                    print(f"  {position.symbol} {position.side}: 数量={position.amount}, 开仓价={position.entry_price}, 当前价={position.current_price}, 盈亏={position.unrealized_pnl}")
                    
                    # 验证持仓结构
                    assert hasattr(position, 'symbol')
                    assert hasattr(position, 'side')
                    assert hasattr(position, 'amount')
                    assert hasattr(position, 'entry_price')
                    assert hasattr(position, 'current_price')
                    assert position.amount > 0  # 只返回有持仓的
                
                print("✅ 持仓信息获取成功")
            else:
                print("⚠️ 当前无持仓")
        
        except Exception as e:
            print(f"❌ 获取持仓失败: {e}")
            raise
        finally:
            client.disconnect()
    
    def test_okx_fetch_orders_real(self, okx_config):
        """测试OKX获取订单信息"""
        print("\n=== 测试获取订单信息 ===")
        client = ExchangeClient(okx_config)
        
        try:
            client.connect()
            
            # 测试获取订单
            print("获取未完成订单...")
            orders = client.fetch_orders()
            
            assert isinstance(orders, list)
            print(f"获取到 {len(orders)} 个未完成订单")
            
            # 显示订单信息
            if orders:
                print("未完成订单:")
                for order in orders:
                    print(f"  {order.id}: {order.symbol} {order.side} {order.amount} @ {order.price} - {order.status}")
                    
                    # 验证订单结构
                    assert hasattr(order, 'id')
                    assert hasattr(order, 'symbol')
                    assert hasattr(order, 'side')
                    assert hasattr(order, 'amount')
                    assert hasattr(order, 'status')
                
                print("✅ 订单信息获取成功")
            else:
                print("⚠️ 当前无未完成订单")
        
        except Exception as e:
            print(f"❌ 获取订单失败: {e}")
            raise
        finally:
            client.disconnect()
    
    def test_okx_context_manager_real(self, okx_config):
        """测试OKX上下文管理器"""
        print("\n=== 测试上下文管理器 ===")
        
        with ExchangeClient(okx_config) as client:
            print("进入上下文管理器")
            assert client.is_connected is True
            
            # 测试基本功能
            markets = client.fetch_markets()
            print(f"在上下文中获取到 {len(markets)} 个市场")
            assert len(markets) > 0
        
        # 上下文退出后应该断开连接
        print("退出上下文管理器")
        assert client.is_connected is False
        print("✅ 上下文管理器测试通过")


if __name__ == "__main__":
    # 运行真实API测试
    pytest.main([__file__, "-v", "-s", "-m", "integration"])
