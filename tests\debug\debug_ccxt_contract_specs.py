#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CCXT合约规格调试工具

检查CCXT提供的合约规格信息，包括合约大小、精度等。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


def debug_ccxt_contract_specs():
    """调试CCXT合约规格"""
    print("=== CCXT合约规格调试工具 ===")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    
    try:
        # 连接交易所
        exchange_client.connect()
        
        # 获取市场信息
        print("\n1. 获取市场信息...")
        markets = exchange_client.fetch_markets()
        print(f"获取到 {len(markets)} 个永续合约")
        
        # 重点分析BTC相关合约
        btc_contracts = []
        for market in markets:
            if 'BTC' in market.get('symbol', ''):
                btc_contracts.append(market)
        
        print(f"\n2. BTC相关合约: {len(btc_contracts)} 个")
        
        # 详细分析每个BTC合约
        for i, market in enumerate(btc_contracts[:5]):  # 只看前5个
            print(f"\n=== BTC合约 {i+1}: {market.get('symbol')} ===")
            
            # 基本信息
            print(f"交易对ID: {market.get('id')}")
            print(f"交易对符号: {market.get('symbol')}")
            print(f"基础货币: {market.get('base')}")
            print(f"计价货币: {market.get('quote')}")
            print(f"结算货币: {market.get('settle')}")
            print(f"合约类型: {market.get('type')}")
            print(f"是否合约: {market.get('contract')}")
            print(f"是否线性: {market.get('linear')}")
            print(f"是否反向: {market.get('inverse')}")
            
            # 合约规格
            print(f"\n合约规格:")
            print(f"  合约大小: {market.get('contractSize')}")
            print(f"  合约价值: {market.get('contractValue')}")
            print(f"  最小交易量: {market.get('limits', {}).get('amount', {}).get('min')}")
            print(f"  最大交易量: {market.get('limits', {}).get('amount', {}).get('max')}")
            print(f"  最小价格: {market.get('limits', {}).get('price', {}).get('min')}")
            print(f"  最大价格: {market.get('limits', {}).get('price', {}).get('max')}")
            
            # 精度信息
            print(f"\n精度信息:")
            precision = market.get('precision', {})
            print(f"  数量精度: {precision.get('amount')}")
            print(f"  价格精度: {precision.get('price')}")
            print(f"  基础精度: {precision.get('base')}")
            print(f"  计价精度: {precision.get('quote')}")
            
            # 费用信息
            print(f"\n费用信息:")
            fees = market.get('fees', {})
            print(f"  Maker费率: {fees.get('maker')}")
            print(f"  Taker费率: {fees.get('taker')}")
            
            # 其他重要信息
            print(f"\n其他信息:")
            print(f"  活跃状态: {market.get('active')}")
            print(f"  到期时间: {market.get('expiry')}")
            print(f"  到期日期: {market.get('expiryDatetime')}")
            
            # 检查info字段（原始API数据）
            info = market.get('info', {})
            if info:
                print(f"\n原始API信息:")
                print(f"  instId: {info.get('instId')}")
                print(f"  instType: {info.get('instType')}")
                print(f"  ctVal: {info.get('ctVal')}")  # 合约面值
                print(f"  ctMult: {info.get('ctMult')}")  # 合约乘数
                print(f"  ctValCcy: {info.get('ctValCcy')}")  # 合约面值币种
                print(f"  lotSz: {info.get('lotSz')}")  # 下单数量精度
                print(f"  tickSz: {info.get('tickSz')}")  # 下单价格精度
                print(f"  minSz: {info.get('minSz')}")  # 最小下单数量
                print(f"  maxLmtSz: {info.get('maxLmtSz')}")  # 限价单最大下单数量
                print(f"  maxMktSz: {info.get('maxMktSz')}")  # 市价单最大下单数量
        
        # 3. 获取当前持仓并分析
        print(f"\n3. 分析当前持仓的合约规格...")
        positions = exchange_client.fetch_positions()
        
        for pos in positions:
            symbol = pos.symbol
            print(f"\n=== 持仓分析: {symbol} ===")
            
            # 查找对应的市场信息
            market_info = None
            for market in markets:
                if market.get('symbol') == symbol:
                    market_info = market
                    break
            
            if market_info:
                print(f"找到市场信息:")
                print(f"  合约大小: {market_info.get('contractSize')}")
                print(f"  合约价值: {market_info.get('contractValue')}")
                
                info = market_info.get('info', {})
                print(f"  合约面值: {info.get('ctVal')}")
                print(f"  合约乘数: {info.get('ctMult')}")
                print(f"  面值币种: {info.get('ctValCcy')}")
                
                # 计算正确的敞口
                contract_size = market_info.get('contractSize', 1)
                contract_value = market_info.get('contractValue', 1)
                
                print(f"\n持仓数据:")
                print(f"  持仓数量: {pos.amount}")
                print(f"  当前价格: {pos.current_price}")
                
                print(f"\n敞口计算方法:")
                method1 = abs(pos.amount) * pos.current_price
                method2 = abs(pos.amount) * contract_size * pos.current_price
                method3 = abs(pos.amount) * contract_value
                
                print(f"  方法1 (数量×价格): {method1:,.2f}")
                print(f"  方法2 (数量×合约大小×价格): {method2:,.2f}")
                print(f"  方法3 (数量×合约价值): {method3:,.2f}")
                
                # 检查哪种方法与保证金×杠杆最接近
                margin_method = pos.margin_used * pos.leverage
                print(f"  保证金×杠杆: {margin_method:,.2f}")
                
                print(f"\n差异分析:")
                print(f"  方法1 vs 保证金: {abs(method1 - margin_method):,.2f}")
                print(f"  方法2 vs 保证金: {abs(method2 - margin_method):,.2f}")
                print(f"  方法3 vs 保证金: {abs(method3 - margin_method):,.2f}")
                
            else:
                print(f"❌ 未找到 {symbol} 的市场信息")
        
        # 4. 建议正确的计算方法
        print(f"\n4. 建议的敞口计算方法:")
        print(f"根据CCXT和OKX的合约规格，正确的敞口计算应该考虑:")
        print(f"1. 合约大小 (contractSize)")
        print(f"2. 合约价值 (contractValue)")
        print(f"3. 合约面值 (ctVal)")
        print(f"4. 合约乘数 (ctMult)")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print(f"\n🧹 连接已断开")


if __name__ == "__main__":
    success = debug_ccxt_contract_specs()
    if success:
        print(f"\n✅ CCXT合约规格调试完成！")
    else:
        print(f"\n❌ CCXT合约规格调试失败！")
        sys.exit(1)
