# DeepSeek量化交易系统 用户指南

## 目录
1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [系统配置](#系统配置)
4. [交易管理](#交易管理)
5. [AI决策系统](#ai决策系统)
6. [风险管理](#风险管理)
7. [监控和日志](#监控和日志)
8. [常见问题](#常见问题)

## 系统概述

DeepSeek量化交易系统是一个基于人工智能的加密货币永续合约自动交易系统。系统采用DeepSeek AI模型进行市场分析和交易决策，支持多个主流交易所，提供完整的风险控制和监控功能。

### 主要特性
- **AI驱动**: 基于DeepSeek AI模型的智能交易决策
- **多交易所支持**: 支持OKX、Binance、Huobi等主流交易所
- **双引擎架构**: 独立的AI开仓引擎和AI持仓引擎
- **风险控制**: 完善的风险管理和参数验证
- **实时监控**: 全面的系统监控和性能指标
- **Web界面**: 专业的控制台风格管理界面

## 快速开始

### 1. 系统要求
- Python 3.8 或更高版本
- 8GB 以上内存
- 10GB 以上可用磁盘空间
- 稳定的网络连接

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动系统

#### 方式一：使用启动脚本（推荐）
```bash
# 启动完整系统
python scripts/start_system.py

# 仅启动Web界面
python scripts/start_system.py --web-only

# 指定端口和主机
python scripts/start_system.py --host 0.0.0.0 --port 8080
```

#### 方式二：手动启动
```bash
# 启动Web服务
uvicorn src.web.app:app --host 127.0.0.1 --port 8000 --reload
```

### 4. 访问Web界面
打开浏览器访问：`http://localhost:8000`

## 系统配置

### 1. 交易所配置

#### 1.1 配置步骤
1. 进入"系统设置"页面
2. 选择"交易所配置"选项卡
3. 选择交易所（OKX、Binance、Huobi等）
4. 填入API密钥信息：
   - API Key
   - Secret Key
   - Passphrase（OKX需要）
5. 选择环境（沙盒/实盘）
6. 点击"测试连接"验证配置
7. 保存配置

#### 1.2 API密钥获取
**OKX交易所**:
1. 登录OKX官网
2. 进入"API管理"页面
3. 创建新的API密钥
4. 设置权限：读取、交易
5. 记录API Key、Secret Key、Passphrase

**注意**: 建议先在沙盒环境测试，确认无误后再切换到实盘。

### 2. 交易参数配置

#### 2.1 基础参数
- **最大杠杆倍数**: 1-100倍，建议不超过20倍
- **最大仓位比例**: 10%-100%，建议不超过50%
- **开仓置信度阈值**: 0-100，建议70以上
- **持仓置信度阈值**: 0-100，建议60以上

#### 2.2 止盈止损设置
- **默认止损百分比**: 1%-50%，建议5%
- **默认止盈百分比**: 1%-100%，建议10%

### 3. 风险参数配置

#### 3.1 风险控制参数
- **最大杠杆**: 系统允许的最大杠杆倍数
- **最大仓位比例**: 单次开仓的最大资金比例
- **最小余额阈值**: 账户最小保留余额
- **敞口比率限制**: 总敞口与账户余额的比例限制

### 4. 交易对选择

#### 4.1 选择原则
- 选择流动性好的主流币种
- 避免选择过多交易对（建议5-10个）
- 关注交易对的波动性和交易量

#### 4.2 推荐交易对
- BTC/USDT:USDT（比特币）
- ETH/USDT:USDT（以太坊）
- BNB/USDT:USDT（币安币）
- ADA/USDT:USDT（艾达币）
- SOL/USDT:USDT（Solana）

## 交易管理

### 1. 持仓管理

#### 1.1 查看持仓
- 进入"持仓管理"页面
- 查看当前所有持仓信息
- 包括：交易对、方向、数量、价格、盈亏等

#### 1.2 手动操作
- **手动开仓**: 点击"+"按钮，填写交易信息
- **手动平仓**: 点击持仓行的"平仓"按钮
- **调整持仓**: 查看持仓详情，进行调整

#### 1.3 持仓监控
- 实时盈亏显示
- 强平价格提醒
- 利润回撤分析

### 2. 订单管理

#### 2.1 订单类型
- **市价单**: 立即按市场价格成交
- **限价单**: 指定价格等待成交

#### 2.2 订单状态
- **已提交**: 订单已发送到交易所
- **部分成交**: 订单部分成交
- **完全成交**: 订单完全成交
- **已取消**: 订单被取消

### 3. 交易统计

#### 3.1 统计指标
- **胜率**: 盈利交易占总交易的比例
- **盈亏比**: 平均盈利与平均亏损的比例
- **最大回撤**: 账户净值的最大回撤幅度
- **夏普比率**: 风险调整后的收益率

## AI决策系统

### 1. AI开仓引擎

#### 1.1 工作原理
- 分析多时间周期技术指标
- 结合市场情绪和趋势判断
- 给出开仓建议和置信度
- 只在无持仓时工作

#### 1.2 决策因素
- **技术指标**: RSI、MACD、布林带等
- **趋势分析**: 多时间周期趋势判断
- **支撑阻力**: 关键价位分析
- **成交量**: 量价关系分析

### 2. AI持仓引擎

#### 2.1 工作原理
- 监控现有持仓状态
- 分析利润回撤情况
- 给出持仓管理建议
- 只在有持仓时工作

#### 2.2 管理策略
- **持有**: 继续持有当前仓位
- **平仓**: 建议平仓离场
- **调整**: 调整止盈止损位置
- **锁定利润**: 部分平仓锁定利润

### 3. 决策查看

#### 3.1 决策历史
- 进入"AI决策"页面
- 查看所有AI决策记录
- 包括：时间、交易对、动作、置信度、理由

#### 3.2 决策筛选
- 按引擎类型筛选
- 按交易对筛选
- 按置信度范围筛选
- 按时间范围筛选

## 风险管理

### 1. 风险控制原则

#### 1.1 资金管理
- 不要投入超过承受能力的资金
- 单次交易风险不超过总资金的2-5%
- 保持足够的现金储备

#### 1.2 杠杆使用
- 新手建议使用低杠杆（1-5倍）
- 有经验者可适当提高（5-20倍）
- 避免使用过高杠杆（>50倍）

#### 1.3 仓位控制
- 避免满仓操作
- 分散投资多个交易对
- 控制单个交易对的仓位比例

### 2. 风险监控

#### 2.1 实时监控
- 账户余额变化
- 持仓盈亏状况
- 保证金使用率
- 强平风险提醒

#### 2.2 风险指标
- **敞口比率**: 总敞口/账户余额
- **保证金比率**: 已用保证金/可用余额
- **最大回撤**: 账户净值最大回撤幅度

### 3. 紧急处理

#### 3.1 紧急停止
- 点击"紧急停止"按钮
- 系统立即停止所有交易
- 可选择是否平仓所有持仓

#### 3.2 风险预警
- 系统自动监控风险指标
- 超过阈值时发出警告
- 严重时可自动停止交易

## 监控和日志

### 1. 系统监控

#### 1.1 系统状态
- 各组件运行状态
- 系统运行时间
- 连接状态检查

#### 1.2 性能监控
- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络连接状态

### 2. 日志管理

#### 2.1 日志类型
- **系统日志**: 系统运行状态
- **交易日志**: 交易操作记录
- **AI决策日志**: AI决策过程
- **错误日志**: 错误和异常信息

#### 2.2 日志查看
- 进入"系统日志"页面
- 按级别筛选日志
- 按模块筛选日志
- 支持关键词搜索

### 3. 数据备份

#### 3.1 自动备份
- 系统自动备份配置数据
- 定期备份交易记录
- 保留最近30天的备份

#### 3.2 手动备份
```bash
# 创建完整备份
python scripts/backup_system.py create --type full

# 创建配置备份
python scripts/backup_system.py create --type config

# 查看备份列表
python scripts/backup_system.py list
```

## 常见问题

### 1. 连接问题

**Q: 无法连接到交易所**
A: 检查以下项目：
- 网络连接是否正常
- API密钥是否正确
- 是否选择了正确的环境（沙盒/实盘）
- 交易所是否有IP白名单限制

**Q: API调用频率限制**
A: 
- 降低数据更新频率
- 检查是否有重复请求
- 联系交易所提高API限制

### 2. 交易问题

**Q: AI不进行开仓决策**
A: 检查以下项目：
- 是否已配置交易所信息
- 是否选择了交易对
- 置信度阈值是否过高
- 是否已有持仓（开仓引擎只在无持仓时工作）

**Q: 订单执行失败**
A: 可能原因：
- 账户余额不足
- 交易对暂停交易
- 价格偏离过大
- 网络连接问题

### 3. 系统问题

**Q: 系统启动失败**
A: 检查以下项目：
- Python版本是否符合要求
- 依赖包是否正确安装
- 端口是否被占用
- 权限是否足够

**Q: 数据显示异常**
A: 尝试以下解决方案：
- 刷新页面
- 重启系统
- 检查网络连接
- 查看错误日志

### 4. 性能问题

**Q: 系统运行缓慢**
A: 优化建议：
- 减少监控的交易对数量
- 降低数据更新频率
- 增加系统内存
- 关闭不必要的程序

**Q: 内存使用过高**
A: 解决方案：
- 重启系统释放内存
- 减少历史数据保留时间
- 优化系统配置
- 升级硬件配置

## 技术支持

如果遇到其他问题，请：
1. 查看系统日志获取详细错误信息
2. 运行健康检查脚本诊断问题
3. 备份重要数据后尝试重启系统
4. 联系技术支持团队

---

**免责声明**: 量化交易存在风险，请在充分了解风险的前提下使用本系统。过往表现不代表未来收益，请谨慎投资。
