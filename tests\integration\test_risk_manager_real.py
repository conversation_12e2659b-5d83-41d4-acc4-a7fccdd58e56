#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险管理系统真实数据测试

使用真实的账户和持仓数据测试风险管理功能。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.risk_manager import RiskManager
from src.data.models import ExchangeConfig, RiskParameters


def test_risk_manager_real():
    """测试风险管理系统"""
    print("=== 风险管理系统真实数据测试 ===")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建风险参数
    risk_params = RiskParameters(
        max_leverage=20,
        max_position_ratio=0.8,
        min_balance_threshold=100.0,
        opening_confidence_threshold=70,
        position_confidence_threshold=60
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    risk_manager = RiskManager(risk_params)
    
    try:
        # 1. 连接交易所并获取真实数据
        print("\n1. 连接交易所并获取真实数据...")
        exchange_client.connect()
        
        # 获取真实账户余额
        real_balance = exchange_client.fetch_balance()
        print(f"获取到 {len(real_balance)} 个币种的余额")
        
        for currency, balance in real_balance.items():
            if balance.total > 0:
                print(f"  {currency}: 总计={balance.total}, 可用={balance.available}, 已用={balance.used}")
        
        # 获取真实持仓
        real_positions = exchange_client.fetch_positions()
        print(f"获取到 {len(real_positions)} 个持仓")
        
        for position in real_positions:
            print(f"  {position.symbol}: {position.side.value} {position.amount} @ {position.current_price}")
        
        print("✅ 真实数据获取成功")
        
        # 2. 测试风险参数验证
        print("\n2. 测试风险参数验证...")
        
        # 测试有效参数
        valid_params = {
            "max_leverage": 10,
            "max_position_ratio": 0.5,
            "min_balance_threshold": 100.0,
            "opening_confidence_threshold": 70,
            "position_confidence_threshold": 60
        }
        
        is_valid, errors = risk_manager.validate_risk_parameters(valid_params)
        print(f"有效参数验证: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"  错误: {error}")
        
        # 测试无效参数
        invalid_params = {
            "max_leverage": 150,  # 超出范围
            "max_position_ratio": 1.5,  # 超出范围
            "opening_confidence_threshold": -10  # 负数
        }
        
        is_valid, errors = risk_manager.validate_risk_parameters(invalid_params)
        print(f"无效参数验证: {'通过' if not is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"  错误: {error}")
        
        print("✅ 风险参数验证完成")
        
        # 3. 测试开仓风险验证
        print("\n3. 测试开仓风险验证...")
        
        # 测试正常开仓
        test_symbol = "BTC/USDT:USDT"
        test_side = "buy"
        test_amount = 0.01  # 小额测试
        test_leverage = 5
        test_price = 50000.0
        
        risk_result = risk_manager.validate_opening_order(
            symbol=test_symbol,
            side=test_side,
            amount=test_amount,
            leverage=test_leverage,
            price=test_price,
            balance=real_balance
        )
        
        print(f"正常开仓风险检查:")
        print(f"  通过: {risk_result.passed}")
        print(f"  风险等级: {risk_result.risk_level}")
        print(f"  警告数量: {len(risk_result.warnings)}")
        print(f"  错误数量: {len(risk_result.errors)}")
        
        for warning in risk_result.warnings:
            print(f"  警告: {warning}")
        for error in risk_result.errors:
            print(f"  错误: {error}")
        
        if risk_result.suggested_adjustments:
            print(f"  建议调整: {risk_result.suggested_adjustments}")
        
        # 测试高风险开仓
        high_risk_amount = 10.0  # 大额测试
        high_risk_leverage = 50  # 超高杠杆
        
        high_risk_result = risk_manager.validate_opening_order(
            symbol=test_symbol,
            side=test_side,
            amount=high_risk_amount,
            leverage=high_risk_leverage,
            price=test_price,
            balance=real_balance
        )
        
        print(f"\n高风险开仓检查:")
        print(f"  通过: {high_risk_result.passed}")
        print(f"  风险等级: {high_risk_result.risk_level}")
        print(f"  错误数量: {len(high_risk_result.errors)}")
        
        for error in high_risk_result.errors:
            print(f"  错误: {error}")
        
        print("✅ 开仓风险验证完成")
        
        # 4. 测试持仓风险计算
        print("\n4. 测试持仓风险计算...")
        
        if real_positions:
            position_risks = risk_manager.calculate_position_risk(real_positions, real_balance)
            
            print(f"持仓风险分析 ({len(position_risks)} 个持仓):")
            for risk in position_risks:
                print(f"  {risk.symbol}:")
                print(f"    当前敞口: {risk.current_exposure:.2f}")
                print(f"    最大允许敞口: {risk.max_allowed_exposure:.2f}")
                print(f"    风险百分比: {risk.risk_percentage:.2%}")
                print(f"    保证金使用率: {risk.margin_usage:.2%}")
                print(f"    杠杆风险: {risk.leverage_risk}")
        else:
            print("当前无持仓，跳过持仓风险计算")
        
        print("✅ 持仓风险计算完成")
        
        # 5. 测试投资组合风险检查
        print("\n5. 测试投资组合风险检查...")
        
        portfolio_risk = risk_manager.check_portfolio_risk(real_positions, real_balance)
        
        print(f"投资组合风险检查:")
        print(f"  通过: {portfolio_risk.passed}")
        print(f"  风险等级: {portfolio_risk.risk_level}")
        print(f"  警告数量: {len(portfolio_risk.warnings)}")
        print(f"  错误数量: {len(portfolio_risk.errors)}")
        
        for warning in portfolio_risk.warnings:
            print(f"  警告: {warning}")
        for error in portfolio_risk.errors:
            print(f"  错误: {error}")
        
        print("✅ 投资组合风险检查完成")
        
        # 6. 测试最大仓位计算
        print("\n6. 测试最大仓位计算...")
        
        if "USDT" in real_balance:
            available_balance = real_balance["USDT"].available
            test_price = 50000.0
            test_leverage = 10
            
            max_position = risk_manager.calculate_max_position_size(
                price=test_price,
                leverage=test_leverage,
                available_balance=available_balance
            )
            
            print(f"最大仓位计算:")
            print(f"  可用余额: {available_balance:.2f} USDT")
            print(f"  测试价格: {test_price:.2f}")
            print(f"  测试杠杆: {test_leverage}x")
            print(f"  最大仓位: {max_position:.6f}")
            print(f"  仓位价值: {max_position * test_price:.2f} USDT")
        
        print("✅ 最大仓位计算完成")
        
        # 7. 测试风险摘要
        print("\n7. 测试风险摘要...")
        
        risk_summary = risk_manager.get_risk_summary(real_positions, real_balance)
        
        print(f"风险摘要:")
        print(f"  总余额: {risk_summary.get('total_balance', 0):.2f}")
        print(f"  总敞口: {risk_summary.get('total_exposure', 0):.2f}")
        print(f"  总保证金: {risk_summary.get('total_margin_used', 0):.2f}")
        print(f"  敞口比率: {risk_summary.get('exposure_ratio', 0):.2%}")
        print(f"  保证金使用率: {risk_summary.get('margin_usage_ratio', 0):.2%}")
        print(f"  投资组合风险等级: {risk_summary.get('portfolio_risk_level', 'unknown')}")
        print(f"  持仓数量: {risk_summary.get('position_count', 0)}")
        print(f"  高风险持仓: {risk_summary.get('high_risk_positions', 0)}")
        print(f"  紧急模式: {risk_summary.get('emergency_mode', False)}")
        
        print("✅ 风险摘要生成完成")
        
        # 8. 测试紧急风控模式
        print("\n8. 测试紧急风控模式...")
        
        # 启用紧急模式
        risk_manager.enable_emergency_mode("测试紧急风控")
        print(f"紧急模式状态: {risk_manager.emergency_mode}")
        
        # 在紧急模式下测试开仓
        emergency_result = risk_manager.validate_opening_order(
            symbol=test_symbol,
            side=test_side,
            amount=0.01,
            leverage=5,
            price=test_price,
            balance=real_balance
        )
        
        print(f"紧急模式下开仓检查:")
        print(f"  通过: {emergency_result.passed}")
        print(f"  错误: {emergency_result.errors}")
        
        # 禁用紧急模式
        risk_manager.disable_emergency_mode()
        print(f"紧急模式状态: {risk_manager.emergency_mode}")
        
        print("✅ 紧急风控模式测试完成")
        
        # 9. 测试风险参数更新
        print("\n9. 测试风险参数更新...")
        
        new_risk_params = RiskParameters(
            max_leverage=15,
            max_position_ratio=0.6,
            min_balance_threshold=200.0,
            opening_confidence_threshold=75,
            position_confidence_threshold=65
        )
        
        try:
            risk_manager.update_risk_parameters(new_risk_params)
            print("✅ 风险参数更新成功")
            print(f"  新的最大杠杆: {risk_manager.risk_params.max_leverage}")
            print(f"  新的最大仓位比例: {risk_manager.risk_params.max_position_ratio}")
        except Exception as e:
            print(f"❌ 风险参数更新失败: {e}")
        
        print("✅ 风险参数更新测试完成")
        
        print("\n🎉 所有风险管理系统测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 风险管理系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = test_risk_manager_real()
    if success:
        print("\n✅ 风险管理系统测试全部通过！")
    else:
        print("\n❌ 风险管理系统测试失败！")
        sys.exit(1)
