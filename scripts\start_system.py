#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统启动脚本

此脚本负责启动整个交易系统，包括：
1. 环境检查和依赖验证
2. 数据库初始化
3. 配置验证
4. 系统组件启动
5. Web服务启动
"""

import os
import sys
import time
import signal
import asyncio
import argparse
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger
from src.data.database_manager import DatabaseManager
from src.services.config_service import ConfigService
from src.services.monitoring_service import MonitoringService
from src.services.notification_service import NotificationService
from src.core.trading_coordinator import TradingCoordinator

logger = get_logger(__name__)


class SystemStarter:
    """系统启动器"""
    
    def __init__(self):
        self.db_manager = None
        self.config_service = None
        self.monitoring_service = None
        self.notification_service = None
        self.trading_coordinator = None
        self.web_process = None
        self.is_running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def start(self, web_only: bool = False, port: int = 8000, host: str = "127.0.0.1"):
        """启动系统
        
        Args:
            web_only: 是否只启动Web服务
            port: Web服务端口
            host: Web服务主机
        """
        try:
            logger.info("=" * 60)
            logger.info("DeepSeek量化交易系统启动中...")
            logger.info("=" * 60)
            
            # 1. 环境检查
            self._check_environment()
            
            # 2. 初始化数据库
            self._init_database()
            
            # 3. 初始化服务
            self._init_services()
            
            if not web_only:
                # 4. 验证配置
                self._validate_config()
                
                # 5. 启动交易系统
                self._start_trading_system()
            
            # 6. 启动Web服务
            self._start_web_service(host, port)
            
            # 7. 启动监控
            if not web_only:
                self._start_monitoring()
            
            self.is_running = True
            logger.info("系统启动完成！")
            logger.info(f"Web界面地址: http://{host}:{port}")
            
            # 保持运行
            self._keep_running()
            
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭系统...")
            self.stop()
        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            self.stop()
            sys.exit(1)
    
    def stop(self):
        """停止系统"""
        if not self.is_running:
            return
        
        logger.info("正在停止系统...")
        
        try:
            # 停止监控服务
            if self.monitoring_service:
                self.monitoring_service.stop_monitoring()
                logger.info("监控服务已停止")
            
            # 停止交易协调器
            if self.trading_coordinator:
                asyncio.run(self.trading_coordinator.stop())
                logger.info("交易协调器已停止")
            
            # 停止Web服务
            if self.web_process:
                self.web_process.terminate()
                self.web_process.wait(timeout=10)
                logger.info("Web服务已停止")
            
            self.is_running = False
            logger.info("系统已完全停止")
            
        except Exception as e:
            logger.error(f"停止系统时发生错误: {e}")
    
    def _check_environment(self):
        """检查环境"""
        logger.info("检查运行环境...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            raise RuntimeError("需要Python 3.8或更高版本")
        
        # 检查必要的目录
        required_dirs = [
            "config",
            "data",
            "logs",
            "src",
            "scripts"
        ]
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
        
        # 检查环境变量文件
        env_file = project_root / ".env"
        if not env_file.exists():
            logger.warning(".env文件不存在，将使用默认配置")
        
        logger.info("环境检查完成")
    
    def _init_database(self):
        """初始化数据库"""
        logger.info("初始化数据库...")
        
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.init_database()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _init_services(self):
        """初始化服务"""
        logger.info("初始化系统服务...")
        
        try:
            # 配置服务
            self.config_service = ConfigService()
            logger.info("配置服务初始化完成")
            
            # 通知服务
            self.notification_service = NotificationService()
            logger.info("通知服务初始化完成")
            
            # 监控服务
            self.monitoring_service = MonitoringService()
            logger.info("监控服务初始化完成")
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            raise
    
    def _validate_config(self):
        """验证配置"""
        logger.info("验证系统配置...")
        
        try:
            # 检查交易所配置
            exchange_config = self.config_service.get_exchange_config()
            if not exchange_config:
                logger.warning("未配置交易所信息，请在Web界面中配置")
            else:
                logger.info(f"交易所配置: {exchange_config.exchange_name}")
            
            # 检查交易参数
            trading_params = self.config_service.get_trading_parameters()
            logger.info(f"交易参数: 最大杠杆={trading_params.max_leverage}x")
            
            # 检查风险参数
            risk_params = self.config_service.get_risk_parameters()
            logger.info(f"风险参数: 最大仓位比例={risk_params.max_position_ratio*100}%")
            
            # 检查选择的交易对
            symbols = self.config_service.get_selected_symbols()
            logger.info(f"选择的交易对: {len(symbols)} 个")
            
            logger.info("配置验证完成")
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            raise
    
    def _start_trading_system(self):
        """启动交易系统"""
        logger.info("启动交易系统...")
        
        try:
            # 检查是否有必要的配置
            exchange_config = self.config_service.get_exchange_config()
            if not exchange_config:
                logger.warning("未配置交易所信息，跳过交易系统启动")
                return
            
            # 创建交易协调器
            self.trading_coordinator = TradingCoordinator()
            
            # 异步启动交易协调器
            asyncio.run(self.trading_coordinator.start())
            
            logger.info("交易系统启动完成")
            
        except Exception as e:
            logger.error(f"交易系统启动失败: {e}")
            raise
    
    def _start_web_service(self, host: str, port: int):
        """启动Web服务"""
        logger.info(f"启动Web服务 {host}:{port}...")
        
        try:
            import subprocess
            
            # 启动FastAPI服务
            cmd = [
                sys.executable, "-m", "uvicorn",
                "src.web.app:app",
                "--host", host,
                "--port", str(port),
                "--reload"
            ]
            
            self.web_process = subprocess.Popen(
                cmd,
                cwd=str(project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务启动
            time.sleep(3)
            
            if self.web_process.poll() is None:
                logger.info("Web服务启动完成")
            else:
                raise RuntimeError("Web服务启动失败")
            
        except Exception as e:
            logger.error(f"Web服务启动失败: {e}")
            raise
    
    def _start_monitoring(self):
        """启动监控"""
        logger.info("启动系统监控...")
        
        try:
            self.monitoring_service.start_monitoring()
            logger.info("系统监控启动完成")
            
        except Exception as e:
            logger.error(f"监控启动失败: {e}")
            raise
    
    def _keep_running(self):
        """保持系统运行"""
        try:
            while self.is_running:
                time.sleep(1)
                
                # 检查Web进程状态
                if self.web_process and self.web_process.poll() is not None:
                    logger.error("Web服务意外停止")
                    break
                
        except KeyboardInterrupt:
            pass
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在停止系统...")
        self.is_running = False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DeepSeek量化交易系统启动脚本")
    parser.add_argument("--web-only", action="store_true", help="只启动Web服务")
    parser.add_argument("--port", type=int, default=8000, help="Web服务端口")
    parser.add_argument("--host", default="127.0.0.1", help="Web服务主机")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.debug:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建并启动系统
    starter = SystemStarter()
    starter.start(
        web_only=args.web_only,
        port=args.port,
        host=args.host
    )


if __name__ == "__main__":
    main()
