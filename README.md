# DeepSeek加密货币永续合约全自动量化系统

基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统。系统采用模块化架构，通过统一的交易所客户端封装CCXT功能，支持多个主流交易所，结合独立的AI开仓引擎和AI持仓引擎实现智能化交易决策。

## 🚀 核心特性

- **AI驱动决策**：基于DeepSeek AI模型的智能开仓和持仓管理
- **多交易所支持**：统一的CCXT接口，支持OKX、Binance、Huobi等主流交易所
- **技术分析引擎**：集成TA-Lib，支持多种技术指标计算
- **风险管理**：严格的风险控制和参数验证机制
- **Web控制台**：现代化的控制台风格Web界面
- **实时监控**：完整的系统监控和日志记录

## 📋 系统要求

- Python 3.8+
- 支持的操作系统：Windows、Linux、macOS
- 内存：至少2GB RAM
- 存储：至少1GB可用空间

## 🛠️ 安装指南

### 1. 克隆项目

```bash
git clone <repository-url>
cd Superbot
```

### 2. 创建虚拟环境

```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入你的API密钥和配置
```

### 5. 初始化数据库

```bash
python scripts/setup_database.py
```

## ⚙️ 配置说明

### 🔒 安全配置

**重要**：为了保护您的API密钥和其他敏感信息，请遵循以下安全配置步骤：

```bash
# 1. 复制配置模板
cp .env.example .env

# 2. 编辑配置文件，填入真实的API密钥
nano .env  # 请勿将真实密钥提交到版本控制
```

### DeepSeek API配置

在 `.env` 文件中配置DeepSeek API：

```env
# 请访问 https://platform.deepseek.com/ 获取API密钥
DEEPSEEK_API_KEY=sk-your-actual-deepseek-api-key-here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# 数据库加密密钥（必须是32个字符）
DATABASE_ENCRYPTION_KEY=your-32-character-encryption-key
```

### 交易所API配置

**注意**：交易所API密钥通过Web界面配置，加密存储在数据库中，无需在环境文件中配置。

支持的交易所：
- OKX（欧易）
- Binance（币安）
- Huobi（火币）
- Bybit
- 其他CCXT支持的交易所

### 📖 详细安全指南

请参阅 [安全配置指南](docs/security_config_guide.md) 了解：
- 密钥生成和管理
- 环境变量配置
- Docker部署配置
- 安全最佳实践

## 🚀 快速开始

### 启动系统

```bash
python main.py
```

### 访问Web界面

打开浏览器访问：`http://localhost:8000`

### 基本使用流程

1. **配置交易所**：在Web界面中配置交易所API密钥
2. **设置交易参数**：配置杠杆、仓位比例、置信度阈值等
3. **选择交易对**：选择要交易的永续合约交易对
4. **启动交易**：开启自动交易模式
5. **监控系统**：通过Web界面实时监控交易状态

## 📊 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Web前端界面 (控制台风格)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  仪表板     │ │  持仓管理   │ │  AI决策     │ │  系统设置   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/AJAX
┌─────────────────────────────────────────────────────────────┐
│                      FastAPI 后端服务                        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      核心交易引擎                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 交易所客户端 │ │市场数据引擎 │ │技术分析引擎 │ │数据存储管理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐                               │
│  │ AI开仓引擎  │ │ AI持仓引擎  │                               │
│  └─────────────┘ └─────────────┘                               │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 开发指南

### 项目结构

```
├── config/                   # 配置文件
├── src/                      # 源代码
│   ├── core/                 # 核心交易引擎
│   ├── data/                 # 数据管理
│   ├── ai/                   # AI相关模块
│   ├── web/                  # Web前端和API
│   ├── utils/                # 工具模块
│   └── services/             # 服务层
├── tests/                    # 测试代码
├── docs/                     # 文档
├── scripts/                  # 脚本
├── logs/                     # 日志文件
└── data/                     # 数据文件
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成测试覆盖率报告
pytest --cov=src tests/
```

## 📝 API文档

启动系统后，访问以下地址查看API文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## ⚠️ 风险提示

1. **投资风险**：加密货币交易存在高风险，可能导致资金损失
2. **技术风险**：系统可能存在bug或故障，建议先在模拟盘测试
3. **API风险**：妥善保管API密钥，避免泄露
4. **网络风险**：确保网络连接稳定，避免因网络问题导致的交易异常

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如果您遇到问题或有疑问，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 🙏 致谢

- [CCXT](https://github.com/ccxt/ccxt) - 统一交易所API库
- [TA-Lib](https://github.com/mrjbq7/ta-lib) - 技术分析库
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [DeepSeek](https://www.deepseek.com/) - AI模型服务
