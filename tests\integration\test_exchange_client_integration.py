#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易所客户端集成测试

使用真实的交易所沙盒环境进行测试。
注意：需要配置真实的API密钥才能运行这些测试。
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig
from src.utils.exceptions import ExchangeConnectionError, ExchangeAuthenticationError


@pytest.mark.integration
class TestExchangeClientIntegration:
    """交易所客户端集成测试类"""
    
    @pytest.fixture
    def okx_sandbox_config(self):
        """OKX沙盒配置"""
        # 从环境变量获取API密钥，如果没有则跳过测试
        api_key = os.getenv('OKX_API_KEY')
        secret_key = os.getenv('OKX_SECRET_KEY')
        passphrase = os.getenv('OKX_PASSPHRASE')
        
        if not all([api_key, secret_key, passphrase]):
            pytest.skip("需要设置OKX API密钥环境变量: OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE")
        
        return ExchangeConfig(
            exchange_name="okx",
            api_key=api_key,
            secret_key=secret_key,
            passphrase=passphrase,
            sandbox_mode=True  # 使用沙盒模式
        )
    
    @pytest.fixture
    def binance_testnet_config(self):
        """Binance测试网配置"""
        api_key = os.getenv('BINANCE_TESTNET_API_KEY')
        secret_key = os.getenv('BINANCE_TESTNET_SECRET_KEY')
        
        if not all([api_key, secret_key]):
            pytest.skip("需要设置Binance测试网API密钥环境变量: BINANCE_TESTNET_API_KEY, BINANCE_TESTNET_SECRET_KEY")
        
        return ExchangeConfig(
            exchange_name="binance",
            api_key=api_key,
            secret_key=secret_key,
            sandbox_mode=True
        )
    
    def test_okx_connection(self, okx_sandbox_config):
        """测试OKX连接"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            # 测试连接
            result = client.connect()
            assert result is True
            assert client.is_connected is True
            
            # 测试获取市场信息
            markets = client.fetch_markets()
            assert isinstance(markets, list)
            assert len(markets) > 0
            
            # 验证返回的是永续合约
            for market in markets[:5]:  # 只检查前5个
                assert market.get('type') == 'swap' or market.get('contract', False)
            
        finally:
            client.disconnect()
    
    def test_okx_fetch_ohlcv(self, okx_sandbox_config):
        """测试OKX获取K线数据"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            client.connect()
            
            # 测试同步获取K线数据
            ohlcv_data = client.fetch_ohlcv_sync("BTC-USDT-SWAP", "1m", 10)
            
            assert isinstance(ohlcv_data, list)
            assert len(ohlcv_data) <= 10
            
            if len(ohlcv_data) > 0:
                # 验证数据结构
                first_candle = ohlcv_data[0]
                assert hasattr(first_candle, 'timestamp')
                assert hasattr(first_candle, 'open')
                assert hasattr(first_candle, 'high')
                assert hasattr(first_candle, 'low')
                assert hasattr(first_candle, 'close')
                assert hasattr(first_candle, 'volume')
                
                # 验证价格逻辑
                assert first_candle.high >= first_candle.low
                assert first_candle.high >= max(first_candle.open, first_candle.close)
                assert first_candle.low <= min(first_candle.open, first_candle.close)
                assert first_candle.volume >= 0
        
        finally:
            client.disconnect()
    
    @pytest.mark.asyncio
    async def test_okx_async_fetch_ohlcv(self, okx_sandbox_config):
        """测试OKX异步获取K线数据"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            client.connect()
            
            # 测试异步获取K线数据
            ohlcv_data = await client.fetch_ohlcv("BTC-USDT-SWAP", "5m", 5)
            
            assert isinstance(ohlcv_data, list)
            assert len(ohlcv_data) <= 5
            
            if len(ohlcv_data) > 0:
                # 验证时间戳是递增的
                timestamps = [candle.timestamp for candle in ohlcv_data]
                assert timestamps == sorted(timestamps)
        
        finally:
            client.disconnect()
    
    def test_okx_fetch_balance(self, okx_sandbox_config):
        """测试OKX获取账户余额"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            client.connect()
            
            # 测试获取余额
            balances = client.fetch_balance()
            
            assert isinstance(balances, dict)
            
            # 沙盒环境可能没有余额，但应该返回空字典而不是错误
            for currency, balance in balances.items():
                assert hasattr(balance, 'currency')
                assert hasattr(balance, 'total')
                assert hasattr(balance, 'available')
                assert hasattr(balance, 'used')
                assert balance.total >= 0
                assert balance.available >= 0
                assert balance.used >= 0
        
        finally:
            client.disconnect()
    
    def test_okx_fetch_positions(self, okx_sandbox_config):
        """测试OKX获取持仓信息"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            client.connect()
            
            # 测试获取持仓
            positions = client.fetch_positions()
            
            assert isinstance(positions, list)
            
            # 沙盒环境可能没有持仓
            for position in positions:
                assert hasattr(position, 'symbol')
                assert hasattr(position, 'side')
                assert hasattr(position, 'amount')
                assert hasattr(position, 'entry_price')
                assert hasattr(position, 'current_price')
                assert position.amount > 0  # 只返回有持仓的
        
        finally:
            client.disconnect()
    
    def test_okx_fetch_orders(self, okx_sandbox_config):
        """测试OKX获取订单信息"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            client.connect()
            
            # 测试获取订单
            orders = client.fetch_orders()
            
            assert isinstance(orders, list)
            
            # 验证订单结构
            for order in orders:
                assert hasattr(order, 'id')
                assert hasattr(order, 'symbol')
                assert hasattr(order, 'side')
                assert hasattr(order, 'amount')
                assert hasattr(order, 'status')
        
        finally:
            client.disconnect()
    
    def test_okx_market_info(self, okx_sandbox_config):
        """测试OKX获取特定交易对信息"""
        client = ExchangeClient(okx_sandbox_config)
        
        try:
            client.connect()
            
            # 测试获取BTC永续合约信息
            market_info = client.fetch_market_info("BTC-USDT-SWAP")
            
            assert isinstance(market_info, dict)
            assert 'symbol' in market_info
            assert market_info['symbol'] == "BTC-USDT-SWAP"
        
        finally:
            client.disconnect()
    
    def test_invalid_credentials(self):
        """测试无效凭据"""
        invalid_config = ExchangeConfig(
            exchange_name="okx",
            api_key="invalid_key",
            secret_key="invalid_secret",
            passphrase="invalid_passphrase",
            sandbox_mode=True
        )
        
        client = ExchangeClient(invalid_config)
        
        # 连接可能成功（因为load_markets不需要认证）
        # 但是获取余额等需要认证的操作会失败
        try:
            client.connect()
            
            # 这个操作需要认证，应该失败
            with pytest.raises(ExchangeAuthenticationError):
                client.fetch_balance()
        
        finally:
            client.disconnect()
    
    def test_context_manager(self, okx_sandbox_config):
        """测试上下文管理器"""
        with ExchangeClient(okx_sandbox_config) as client:
            assert client.is_connected is True
            
            # 测试基本功能
            markets = client.fetch_markets()
            assert len(markets) > 0
        
        # 上下文退出后应该断开连接
        assert client.is_connected is False


if __name__ == "__main__":
    # 运行集成测试
    pytest.main([__file__, "-v", "-m", "integration"])
