{% extends "base.html" %}

{% block title %}系统日志 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<li style="display: flex; align-items: center; gap: 4px;">
    <span>/</span>
    <span>📝</span>
    <span>系统日志</span>
</li>
{% endblock %}

{% block extra_css %}
<style>
    /* 系统日志页面特定样式 */
    .logs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        flex-wrap: wrap;
        gap: 16px;
    }
    
    .logs-filters {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .filter-label {
        font-size: 14px;
        color: var(--ant-color-text-secondary);
        white-space: nowrap;
    }
    
    .filter-select,
    .filter-input {
        padding: 6px 12px;
        border: 1px solid var(--ant-color-border);
        border-radius: var(--ant-border-radius);
        background: var(--ant-color-bg-container);
        color: var(--ant-color-text);
        font-size: 14px;
        min-width: 120px;
    }
    
    .filter-input {
        min-width: 200px;
    }
    
    .logs-container {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        box-shadow: var(--ant-box-shadow);
        display: flex;
        flex-direction: column;
        height: 600px;
    }
    
    .logs-toolbar {
        padding: 16px 24px;
        border-bottom: 1px solid var(--ant-color-border-secondary);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--ant-color-fill-secondary);
    }
    
    .toolbar-left {
        display: flex;
        align-items: center;
        gap: 16px;
    }
    
    .logs-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .logs-count {
        font-size: 12px;
        color: var(--ant-color-text-secondary);
        background: var(--ant-color-fill);
        padding: 2px 8px;
        border-radius: 12px;
    }
    
    .toolbar-right {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .auto-scroll-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: var(--ant-color-text-secondary);
    }
    
    .auto-scroll-checkbox {
        margin: 0;
    }
    
    .logs-content {
        flex: 1;
        overflow-y: auto;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.4;
        background: #1e1e1e;
        color: #d4d4d4;
    }
    
    [data-theme="light"] .logs-content {
        background: #f8f8f8;
        color: #333333;
    }
    
    .log-entry {
        padding: 8px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: flex-start;
        gap: 12px;
        transition: background-color 0.2s;
    }
    
    [data-theme="light"] .log-entry {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .log-entry:hover {
        background: rgba(255, 255, 255, 0.05);
    }
    
    [data-theme="light"] .log-entry:hover {
        background: rgba(0, 0, 0, 0.05);
    }
    
    .log-timestamp {
        color: #569cd6;
        white-space: nowrap;
        min-width: 140px;
        font-size: 12px;
    }
    
    [data-theme="light"] .log-timestamp {
        color: #0066cc;
    }
    
    .log-level {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        white-space: nowrap;
        min-width: 60px;
        text-align: center;
    }
    
    .log-level.debug {
        background: #4a4a4a;
        color: #cccccc;
    }
    
    .log-level.info {
        background: #1e3a8a;
        color: #93c5fd;
    }
    
    .log-level.warning {
        background: #92400e;
        color: #fbbf24;
    }
    
    .log-level.error {
        background: #991b1b;
        color: #fca5a5;
    }
    
    .log-level.success {
        background: #166534;
        color: #86efac;
    }
    
    .log-module {
        color: #ce9178;
        font-weight: 500;
        min-width: 100px;
        white-space: nowrap;
    }
    
    [data-theme="light"] .log-module {
        color: #a31515;
    }
    
    .log-message {
        flex: 1;
        word-break: break-word;
        color: #d4d4d4;
    }
    
    [data-theme="light"] .log-message {
        color: #333333;
    }
    
    .log-details {
        color: #608b4e;
        font-style: italic;
        margin-top: 4px;
        font-size: 12px;
    }
    
    [data-theme="light"] .log-details {
        color: #008000;
    }
    
    .empty-logs {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: var(--ant-color-text-secondary);
        text-align: center;
    }
    
    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }
    
    .loading-logs {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: var(--ant-color-text-secondary);
    }
    
    .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid var(--ant-color-border);
        border-top-color: var(--ant-color-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 12px;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .ant-btn {
        padding: 6px 12px;
        border-radius: var(--ant-border-radius);
        border: 1px solid var(--ant-color-border);
        background: var(--ant-color-bg-container);
        color: var(--ant-color-text);
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 6px;
    }
    
    .ant-btn:hover {
        border-color: var(--ant-color-primary);
        color: var(--ant-color-primary);
    }
    
    .ant-btn-primary {
        background: var(--ant-color-primary);
        border-color: var(--ant-color-primary);
        color: white;
    }
    
    .ant-btn-primary:hover {
        background: #4096ff;
        border-color: #4096ff;
    }
    
    @media (max-width: 768px) {
        .logs-header {
            flex-direction: column;
            align-items: stretch;
        }
        
        .logs-filters {
            flex-direction: column;
            align-items: stretch;
        }
        
        .filter-group {
            justify-content: space-between;
        }
        
        .logs-container {
            height: 500px;
        }
        
        .logs-toolbar {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
        }
        
        .toolbar-left,
        .toolbar-right {
            justify-content: space-between;
        }
        
        .log-entry {
            flex-direction: column;
            gap: 4px;
        }
        
        .log-timestamp,
        .log-module {
            min-width: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="logs-header">
    <div>
        <h1 style="margin: 0 0 8px 0; font-size: 24px; font-weight: 600; color: var(--ant-color-text);">
            📝 系统日志
        </h1>
        <p style="margin: 0; color: var(--ant-color-text-secondary);">
            查看和分析系统运行日志，监控交易活动和错误信息
        </p>
    </div>
    
    <div class="logs-filters">
        <div class="filter-group">
            <span class="filter-label">日志级别:</span>
            <select class="filter-select" id="levelFilter">
                <option value="">全部级别</option>
                <option value="debug">调试</option>
                <option value="info">信息</option>
                <option value="warning">警告</option>
                <option value="error">错误</option>
                <option value="success">成功</option>
            </select>
        </div>
        
        <div class="filter-group">
            <span class="filter-label">模块:</span>
            <select class="filter-select" id="moduleFilter">
                <option value="">全部模块</option>
                <option value="trading">交易引擎</option>
                <option value="ai">AI引擎</option>
                <option value="risk">风控系统</option>
                <option value="exchange">交易所</option>
                <option value="system">系统</option>
            </select>
        </div>
        
        <div class="filter-group">
            <span class="filter-label">搜索:</span>
            <input type="text" class="filter-input" id="searchFilter" placeholder="搜索日志内容...">
        </div>
        
        <button class="ant-btn ant-btn-primary" id="applyFilters">
            <span>🔍</span>
            <span>应用筛选</span>
        </button>
    </div>
</div>

<div class="logs-container">
    <div class="logs-toolbar">
        <div class="toolbar-left">
            <div class="logs-title">
                <span>📋</span>
                <span>实时日志</span>
            </div>
            <div class="logs-count" id="logsCount">0 条记录</div>
        </div>
        
        <div class="toolbar-right">
            <div class="auto-scroll-toggle">
                <input type="checkbox" class="auto-scroll-checkbox" id="autoScroll" checked>
                <label for="autoScroll">自动滚动</label>
            </div>
            
            <button class="ant-btn" id="clearLogs">
                <span>🗑️</span>
                <span>清空</span>
            </button>
            
            <button class="ant-btn" id="exportLogs">
                <span>📥</span>
                <span>导出</span>
            </button>
            
            <button class="ant-btn" id="refreshLogs">
                <span>🔄</span>
                <span>刷新</span>
            </button>
        </div>
    </div>
    
    <div class="logs-content" id="logsContent">
        <div class="loading-logs">
            <div class="loading-spinner"></div>
            <span>加载系统日志...</span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 系统日志管理器
    class LogsManager {
        constructor() {
            this.logs = [];
            this.filteredLogs = [];
            this.isLoading = false;
            this.autoScroll = true;
            this.refreshInterval = null;
            this.filters = {
                level: '',
                module: '',
                search: ''
            };
            this.init();
        }

        init() {
            this.loadLogs();
            this.startAutoRefresh();
            this.bindEvents();
        }

        bindEvents() {
            // 筛选器事件
            document.getElementById('applyFilters').addEventListener('click', () => {
                this.applyFilters();
            });

            // 搜索框回车事件
            document.getElementById('searchFilter').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });

            // 自动滚动切换
            document.getElementById('autoScroll').addEventListener('change', (e) => {
                this.autoScroll = e.target.checked;
            });

            // 工具栏按钮
            document.getElementById('clearLogs').addEventListener('click', () => {
                this.clearLogs();
            });

            document.getElementById('exportLogs').addEventListener('click', () => {
                this.exportLogs();
            });

            document.getElementById('refreshLogs').addEventListener('click', () => {
                this.loadLogs();
            });
        }

        async loadLogs() {
            if (this.isLoading) return;

            this.isLoading = true;
            this.showLoading();

            try {
                const response = await axios.get('/api/system/logs', {
                    params: {
                        limit: 1000,
                        ...this.filters
                    }
                });

                this.logs = response.data.logs || [];
                this.applyFilters();

            } catch (error) {
                console.error('加载日志失败:', error);
                this.showError('加载日志失败，请稍后重试');
            } finally {
                this.isLoading = false;
            }
        }

        applyFilters() {
            // 获取当前筛选条件
            this.filters.level = document.getElementById('levelFilter').value;
            this.filters.module = document.getElementById('moduleFilter').value;
            this.filters.search = document.getElementById('searchFilter').value.toLowerCase();

            // 应用筛选
            this.filteredLogs = this.logs.filter(log => {
                // 级别筛选
                if (this.filters.level && log.level !== this.filters.level) {
                    return false;
                }

                // 模块筛选
                if (this.filters.module && log.module !== this.filters.module) {
                    return false;
                }

                // 搜索筛选
                if (this.filters.search) {
                    const searchText = `${log.message} ${log.details || ''}`.toLowerCase();
                    if (!searchText.includes(this.filters.search)) {
                        return false;
                    }
                }

                return true;
            });

            this.renderLogs();
        }

        renderLogs() {
            const container = document.getElementById('logsContent');
            const countElement = document.getElementById('logsCount');

            // 更新计数
            countElement.textContent = `${this.filteredLogs.length} 条记录`;

            if (this.filteredLogs.length === 0) {
                container.innerHTML = `
                    <div class="empty-logs">
                        <div class="empty-icon">📝</div>
                        <div>没有找到匹配的日志记录</div>
                        <div style="font-size: 14px; margin-top: 8px; color: var(--ant-color-text-tertiary);">
                            尝试调整筛选条件或刷新日志
                        </div>
                    </div>
                `;
                return;
            }

            // 渲染日志条目
            const logsHtml = this.filteredLogs.map(log => this.renderLogEntry(log)).join('');
            container.innerHTML = logsHtml;

            // 自动滚动到底部
            if (this.autoScroll) {
                container.scrollTop = container.scrollHeight;
            }
        }

        renderLogEntry(log) {
            const timestamp = this.formatTimestamp(log.timestamp);
            const levelClass = log.level || 'info';
            const levelText = this.getLevelText(log.level);

            return `
                <div class="log-entry">
                    <div class="log-timestamp">${timestamp}</div>
                    <div class="log-level ${levelClass}">${levelText}</div>
                    <div class="log-module">${log.module || 'system'}</div>
                    <div class="log-message">
                        ${this.escapeHtml(log.message)}
                        ${log.details ? `<div class="log-details">${this.escapeHtml(log.details)}</div>` : ''}
                    </div>
                </div>
            `;
        }

        formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                fractionalSecondDigits: 3
            });
        }

        getLevelText(level) {
            const levelMap = {
                'debug': 'DEBUG',
                'info': 'INFO',
                'warning': 'WARN',
                'error': 'ERROR',
                'success': 'OK'
            };
            return levelMap[level] || 'INFO';
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        async clearLogs() {
            if (!confirm('确定要清空所有日志吗？此操作不可撤销。')) {
                return;
            }

            try {
                await axios.delete('/api/system/logs');
                this.logs = [];
                this.filteredLogs = [];
                this.renderLogs();
                alert('✅ 日志已清空');
            } catch (error) {
                console.error('清空日志失败:', error);
                alert('❌ 清空日志失败: ' + (error.response?.data?.message || error.message));
            }
        }

        exportLogs() {
            if (this.filteredLogs.length === 0) {
                alert('❌ 没有日志可以导出');
                return;
            }

            // 生成CSV内容
            const headers = ['时间', '级别', '模块', '消息', '详情'];
            const csvContent = [
                headers.join(','),
                ...this.filteredLogs.map(log => [
                    `"${this.formatTimestamp(log.timestamp)}"`,
                    `"${log.level || 'info'}"`,
                    `"${log.module || 'system'}"`,
                    `"${(log.message || '').replace(/"/g, '""')}"`,
                    `"${(log.details || '').replace(/"/g, '""')}"`
                ].join(','))
            ].join('\n');

            // 下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `system_logs_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ 日志已导出');
        }

        showLoading() {
            const container = document.getElementById('logsContent');
            container.innerHTML = `
                <div class="loading-logs">
                    <div class="loading-spinner"></div>
                    <span>加载系统日志...</span>
                </div>
            `;
        }

        showError(message) {
            const container = document.getElementById('logsContent');
            container.innerHTML = `
                <div class="empty-logs">
                    <div class="empty-icon">❌</div>
                    <div>${message}</div>
                </div>
            `;
        }

        startAutoRefresh() {
            // 每10秒自动刷新日志
            this.refreshInterval = setInterval(() => {
                if (!this.isLoading) {
                    this.loadLogs();
                }
            }, 10000);
        }

        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        }
    }

    // 页面加载完成后初始化日志管理器
    document.addEventListener('DOMContentLoaded', () => {
        window.logsManager = new LogsManager();
    });

    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', () => {
        if (window.logsManager) {
            window.logsManager.stopAutoRefresh();
        }
    });
</script>
{% endblock %}
