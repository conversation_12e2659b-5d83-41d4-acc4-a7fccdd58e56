#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统市场数据获取引擎

此模块负责定期获取多时间周期的市场数据，包括：
1. 多时间周期K线数据获取
2. 市场数据缓存和管理
3. 数据完整性检查
4. 实时数据更新
5. 数据轮询调度
"""

import asyncio
import time
from typing import Dict, List, Optional, Set, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import threading

from src.core.exchange_client import ExchangeClient
from src.data.models import MarketData, OHLCVData
from src.utils.exceptions import DataInsufficientError, ExchangeConnectionError
from src.utils.logger import get_logger, log_execution_time
from src.utils.formatters import TimeFormatter

logger = get_logger(__name__)


@dataclass
class DataRequirement:
    """数据需求配置"""
    symbol: str                        # 交易对
    timeframes: List[str]              # 时间周期列表
    min_periods: int = 200             # 最小数据量
    max_periods: int = 1000            # 最大数据量
    update_interval: int = 60          # 更新间隔（秒）
    last_update: int = 0               # 上次更新时间


class MarketDataEngine:
    """市场数据获取引擎类
    
    负责管理多个交易对的多时间周期市场数据获取。
    """
    
    def __init__(self, exchange_client: ExchangeClient, symbols: List[str]):
        """初始化市场数据引擎
        
        Args:
            exchange_client: 交易所客户端实例
            symbols: 要监控的交易对列表
        """
        self.exchange_client = exchange_client
        self.symbols = symbols
        self.is_running = False
        self.polling_task = None
        self._lock = threading.RLock()  # 使用重入锁避免死锁
        
        # 数据存储
        self.market_data: Dict[str, MarketData] = {}
        
        # 支持的时间周期
        self.supported_timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
        self.default_timeframes = ["1m", "5m", "15m", "1h"]
        
        # 数据需求配置
        self.data_requirements: Dict[str, DataRequirement] = {}
        
        # 初始化数据需求
        self._initialize_data_requirements()
    
    def _initialize_data_requirements(self):
        """初始化数据需求配置"""
        for symbol in self.symbols:
            self.data_requirements[symbol] = DataRequirement(
                symbol=symbol,
                timeframes=self.default_timeframes.copy(),
                min_periods=200,
                max_periods=1000,
                update_interval=60
            )
            
            # 初始化市场数据存储
            self.market_data[symbol] = MarketData(symbol=symbol)
    
    def add_symbol(self, symbol: str, timeframes: Optional[List[str]] = None):
        """添加新的交易对监控
        
        Args:
            symbol: 交易对符号
            timeframes: 时间周期列表，如果为None则使用默认值
        """
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            
            # 添加数据需求配置
            self.data_requirements[symbol] = DataRequirement(
                symbol=symbol,
                timeframes=timeframes or self.default_timeframes.copy()
            )
            
            # 初始化市场数据存储
            self.market_data[symbol] = MarketData(symbol=symbol)
            
            logger.info(f"添加新的交易对监控: {symbol}")
    
    def remove_symbol(self, symbol: str):
        """移除交易对监控
        
        Args:
            symbol: 交易对符号
        """
        if symbol in self.symbols:
            self.symbols.remove(symbol)
            self.data_requirements.pop(symbol, None)
            self.market_data.pop(symbol, None)
            
            logger.info(f"移除交易对监控: {symbol}")
    
    def set_timeframes(self, symbol: str, timeframes: List[str]):
        """设置交易对的时间周期
        
        Args:
            symbol: 交易对符号
            timeframes: 时间周期列表
        """
        if symbol in self.data_requirements:
            # 验证时间周期
            valid_timeframes = [tf for tf in timeframes if tf in self.supported_timeframes]
            if not valid_timeframes:
                raise ValueError(f"无效的时间周期: {timeframes}")
            
            self.data_requirements[symbol].timeframes = valid_timeframes
            logger.info(f"设置 {symbol} 时间周期: {valid_timeframes}")
    
    @log_execution_time("获取多时间周期数据")
    async def fetch_multi_timeframe_data(self, symbol: str) -> Dict[str, List[OHLCVData]]:
        """获取指定交易对的多时间周期数据
        
        Args:
            symbol: 交易对符号
        
        Returns:
            Dict[str, List[OHLCVData]]: 多时间周期数据
        """
        if symbol not in self.data_requirements:
            raise ValueError(f"未配置交易对: {symbol}")
        
        requirement = self.data_requirements[symbol]
        result = {}
        
        for timeframe in requirement.timeframes:
            try:
                # 获取K线数据
                ohlcv_data = await self.exchange_client.fetch_ohlcv(
                    symbol, 
                    timeframe, 
                    requirement.max_periods
                )
                
                result[timeframe] = ohlcv_data
                logger.debug(f"获取 {symbol} {timeframe} 数据: {len(ohlcv_data)} 条")
                
            except Exception as e:
                logger.error(f"获取 {symbol} {timeframe} 数据失败: {e}")
                result[timeframe] = []
        
        return result
    
    def fetch_multi_timeframe_data_sync(self, symbol: str) -> Dict[str, List[OHLCVData]]:
        """同步获取指定交易对的多时间周期数据
        
        Args:
            symbol: 交易对符号
        
        Returns:
            Dict[str, List[OHLCVData]]: 多时间周期数据
        """
        if symbol not in self.data_requirements:
            raise ValueError(f"未配置交易对: {symbol}")
        
        requirement = self.data_requirements[symbol]
        result = {}
        
        for timeframe in requirement.timeframes:
            try:
                # 获取K线数据
                ohlcv_data = self.exchange_client.fetch_ohlcv_sync(
                    symbol, 
                    timeframe, 
                    requirement.max_periods
                )
                
                result[timeframe] = ohlcv_data
                logger.debug(f"同步获取 {symbol} {timeframe} 数据: {len(ohlcv_data)} 条")
                
            except Exception as e:
                logger.error(f"同步获取 {symbol} {timeframe} 数据失败: {e}")
                result[timeframe] = []
        
        return result
    
    def update_market_data(self, symbol: str, timeframe_data: Dict[str, List[OHLCVData]]):
        """更新市场数据
        
        Args:
            symbol: 交易对符号
            timeframe_data: 时间周期数据
        """
        with self._lock:
            if symbol not in self.market_data:
                self.market_data[symbol] = MarketData(symbol=symbol)
            
            market_data = self.market_data[symbol]
            
            for timeframe, ohlcv_list in timeframe_data.items():
                if ohlcv_list:
                    market_data.add_ohlcv_data(timeframe, ohlcv_list)
            
            # 更新最后更新时间
            if symbol in self.data_requirements:
                self.data_requirements[symbol].last_update = int(time.time())
    
    def get_latest_data(self, symbol: str, timeframe: str) -> List[OHLCVData]:
        """获取最新的市场数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
        
        Returns:
            List[OHLCVData]: K线数据列表
        """
        with self._lock:
            if symbol in self.market_data:
                market_data = self.market_data[symbol]
                return market_data.timeframes.get(timeframe, [])
            return []
    
    def get_latest_price(self, symbol: str, timeframe: str = "1m") -> Optional[float]:
        """获取最新价格
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
        
        Returns:
            Optional[float]: 最新价格
        """
        with self._lock:
            if symbol in self.market_data:
                return self.market_data[symbol].get_latest_price(timeframe)
            return None
    
    def is_data_sufficient(self, symbol: str, timeframe: str, min_periods: Optional[int] = None) -> bool:
        """检查数据是否充足
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            min_periods: 最小周期数，如果为None则使用配置值
        
        Returns:
            bool: 数据是否充足
        """
        if symbol not in self.data_requirements:
            return False
        
        requirement = self.data_requirements[symbol]
        min_required = min_periods or requirement.min_periods
        
        data_count = self.get_data_count(symbol, timeframe)
        return data_count >= min_required
    
    def get_data_count(self, symbol: str, timeframe: str) -> int:
        """获取数据数量
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
        
        Returns:
            int: 数据数量
        """
        with self._lock:
            if symbol in self.market_data:
                return self.market_data[symbol].get_data_count(timeframe)
            return 0
    
    def get_data_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有数据状态
        
        Returns:
            Dict[str, Dict[str, Any]]: 数据状态信息
        """
        status = {}
        
        with self._lock:
            for symbol in self.symbols:
                symbol_status = {
                    "timeframes": {},
                    "last_update": 0,
                    "is_sufficient": True
                }
                
                if symbol in self.data_requirements:
                    requirement = self.data_requirements[symbol]
                    symbol_status["last_update"] = requirement.last_update
                    
                    for timeframe in requirement.timeframes:
                        data_count = self.get_data_count(symbol, timeframe)
                        is_sufficient = data_count >= requirement.min_periods
                        
                        symbol_status["timeframes"][timeframe] = {
                            "count": data_count,
                            "required": requirement.min_periods,
                            "sufficient": is_sufficient
                        }
                        
                        if not is_sufficient:
                            symbol_status["is_sufficient"] = False
                
                status[symbol] = symbol_status
        
        return status
    
    async def _update_single_symbol(self, symbol: str):
        """更新单个交易对的数据
        
        Args:
            symbol: 交易对符号
        """
        try:
            # 获取多时间周期数据
            timeframe_data = await self.fetch_multi_timeframe_data(symbol)
            
            # 更新市场数据
            self.update_market_data(symbol, timeframe_data)
            
            logger.debug(f"更新 {symbol} 数据完成")
            
        except Exception as e:
            logger.error(f"更新 {symbol} 数据失败: {e}")
    
    async def _polling_loop(self, interval: int = 60):
        """数据轮询循环

        Args:
            interval: 轮询间隔（秒）
        """
        logger.info(f"开始市场数据轮询，间隔: {interval}秒")

        while self.is_running:
            try:
                # 检查交易所连接
                if not self.exchange_client.is_connected:
                    logger.warning("交易所未连接，跳过本次轮询")
                    await asyncio.sleep(10)
                    continue

                # 更新所有交易对数据
                tasks = []
                for symbol in self.symbols:
                    task = asyncio.create_task(self._update_single_symbol(symbol))
                    tasks.append(task)

                # 等待所有任务完成
                await asyncio.gather(*tasks, return_exceptions=True)

                logger.info(f"完成一轮数据更新，涉及 {len(self.symbols)} 个交易对")

                # 等待下次轮询
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"数据轮询出错: {e}")
                await asyncio.sleep(min(30, interval))  # 出错后等待较短时间再重试
    
    async def start_data_polling(self, interval: int = 60):
        """启动数据轮询

        Args:
            interval: 轮询间隔（秒）
        """
        if self.is_running:
            logger.warning("数据轮询已在运行")
            return

        self.is_running = True

        # 首次获取所有数据
        logger.info("首次获取市场数据...")
        for symbol in self.symbols:
            await self._update_single_symbol(symbol)

        # 启动轮询任务
        self.polling_task = asyncio.create_task(self._polling_loop(interval))
        logger.info(f"市场数据轮询已启动，间隔: {interval}秒")
    
    async def stop_data_polling(self):
        """停止数据轮询"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.polling_task:
            self.polling_task.cancel()
            try:
                await self.polling_task
            except asyncio.CancelledError:
                pass
        
        logger.info("市场数据轮询已停止")

    async def start(self):
        """启动市场数据引擎（兼容方法）"""
        await self.start_data_polling()

    async def stop(self):
        """停止市场数据引擎（兼容方法）"""
        await self.stop_data_polling()

    async def get_symbol_data(self, symbol: str) -> Optional[Dict[str, List[OHLCVData]]]:
        """获取交易对的所有时间周期数据（兼容方法）

        Args:
            symbol: 交易对符号

        Returns:
            Optional[Dict[str, List[OHLCVData]]]: 时间周期数据字典
        """
        with self._lock:
            if symbol not in self.market_data:
                logger.warning(f"没有 {symbol} 的市场数据")
                return None

            # 转换为兼容格式
            result = {}
            market_data = self.market_data[symbol]

            for timeframe in self.default_timeframes:
                if timeframe in market_data.timeframes:
                    data = market_data.timeframes[timeframe]
                    if data:
                        result[timeframe] = data

            return result if result else None
    
    def get_market_summary(self) -> Dict[str, Any]:
        """获取市场数据摘要
        
        Returns:
            Dict[str, Any]: 市场数据摘要
        """
        summary = {
            "total_symbols": len(self.symbols),
            "is_running": self.is_running,
            "symbols": self.symbols.copy(),
            "supported_timeframes": self.supported_timeframes.copy(),
            "data_status": self.get_data_status()
        }
        
        return summary
