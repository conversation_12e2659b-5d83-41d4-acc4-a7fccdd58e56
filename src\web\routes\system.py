#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统控制API路由

此模块提供系统控制相关的API接口，包括：
1. 系统启动/停止控制
2. 系统状态监控
3. 日志管理
4. 系统健康检查
5. 紧急控制功能
"""

import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 系统状态存储（在实际应用中应该使用更持久的存储）
system_state = {
    "is_running": False,
    "start_time": None,
    "last_update": time.time(),
    "components": {
        "trading_engine": "stopped",
        "market_data_engine": "stopped",
        "ai_opening_engine": "stopped",
        "ai_position_engine": "stopped",
        "risk_manager": "stopped"
    },
    "emergency_mode": False
}


@router.get("/status")
async def get_system_status():
    """获取系统状态"""
    try:
        uptime = 0
        if system_state["start_time"]:
            uptime = time.time() - system_state["start_time"]
        
        status = {
            "is_running": system_state["is_running"],
            "uptime": uptime,
            "uptime_formatted": format_uptime(uptime),
            "start_time": system_state["start_time"],
            "last_update": system_state["last_update"],
            "components": system_state["components"].copy(),
            "emergency_mode": system_state["emergency_mode"],
            "health": "healthy" if system_state["is_running"] else "stopped"
        }
        
        return JSONResponse(content=status)
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_system():
    """启动交易系统"""
    try:
        if system_state["is_running"]:
            return JSONResponse(content={
                "success": False,
                "message": "系统已在运行中"
            })
        
        # 模拟启动过程
        logger.info("开始启动交易系统...")
        
        # 更新系统状态
        system_state["is_running"] = True
        system_state["start_time"] = time.time()
        system_state["last_update"] = time.time()
        system_state["emergency_mode"] = False
        
        # 模拟组件启动
        components = ["trading_engine", "market_data_engine", "ai_opening_engine", "ai_position_engine", "risk_manager"]
        for component in components:
            system_state["components"][component] = "running"
            logger.info(f"{component} 已启动")
        
        logger.info("交易系统启动完成")
        
        return JSONResponse(content={
            "success": True,
            "message": "交易系统启动成功",
            "start_time": system_state["start_time"],
            "components": system_state["components"]
        })
        
    except Exception as e:
        logger.error(f"启动系统失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_system():
    """停止交易系统"""
    try:
        if not system_state["is_running"]:
            return JSONResponse(content={
                "success": False,
                "message": "系统未在运行"
            })
        
        # 模拟停止过程
        logger.info("开始停止交易系统...")
        
        # 模拟组件停止
        components = ["ai_position_engine", "ai_opening_engine", "trading_engine", "market_data_engine", "risk_manager"]
        for component in components:
            system_state["components"][component] = "stopped"
            logger.info(f"{component} 已停止")
        
        # 更新系统状态
        system_state["is_running"] = False
        system_state["start_time"] = None
        system_state["last_update"] = time.time()
        system_state["emergency_mode"] = False
        
        logger.info("交易系统停止完成")
        
        return JSONResponse(content={
            "success": True,
            "message": "交易系统停止成功",
            "components": system_state["components"]
        })
        
    except Exception as e:
        logger.error(f"停止系统失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/restart")
async def restart_system():
    """重启交易系统"""
    try:
        logger.info("开始重启交易系统...")
        
        # 先停止
        if system_state["is_running"]:
            await stop_system()
            time.sleep(2)  # 等待停止完成
        
        # 再启动
        result = await start_system()
        
        logger.info("交易系统重启完成")
        
        return JSONResponse(content={
            "success": True,
            "message": "交易系统重启成功",
            "restart_time": time.time()
        })
        
    except Exception as e:
        logger.error(f"重启系统失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/emergency-stop")
async def emergency_stop():
    """紧急停止"""
    try:
        logger.warning("执行紧急停止...")
        
        # 立即停止所有交易活动
        system_state["emergency_mode"] = True
        system_state["is_running"] = False
        system_state["last_update"] = time.time()
        
        # 停止所有组件
        for component in system_state["components"]:
            system_state["components"][component] = "emergency_stopped"
        
        logger.warning("紧急停止完成")
        
        return JSONResponse(content={
            "success": True,
            "message": "紧急停止执行成功",
            "emergency_mode": True,
            "stop_time": time.time()
        })
        
    except Exception as e:
        logger.error(f"紧急停止失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset-emergency")
async def reset_emergency_mode():
    """重置紧急模式"""
    try:
        if not system_state["emergency_mode"]:
            return JSONResponse(content={
                "success": False,
                "message": "系统未处于紧急模式"
            })
        
        logger.info("重置紧急模式...")
        
        system_state["emergency_mode"] = False
        system_state["last_update"] = time.time()
        
        # 重置组件状态为停止
        for component in system_state["components"]:
            system_state["components"][component] = "stopped"
        
        logger.info("紧急模式已重置")
        
        return JSONResponse(content={
            "success": True,
            "message": "紧急模式已重置",
            "emergency_mode": False
        })
        
    except Exception as e:
        logger.error(f"重置紧急模式失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """系统健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "checks": {
                "system_running": system_state["is_running"],
                "emergency_mode": not system_state["emergency_mode"],
                "components_healthy": all(
                    status in ["running", "stopped"] 
                    for status in system_state["components"].values()
                ),
                "database_connection": True,  # 模拟数据库检查
                "api_responsive": True,       # 模拟API检查
                "memory_usage_ok": True,      # 模拟内存检查
                "disk_space_ok": True         # 模拟磁盘检查
            }
        }
        
        # 计算总体健康状态
        all_healthy = all(health_status["checks"].values())
        health_status["status"] = "healthy" if all_healthy else "unhealthy"
        
        return JSONResponse(content=health_status)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs")
async def get_system_logs(
    level: Optional[str] = Query(None, description="日志级别筛选: DEBUG/INFO/WARNING/ERROR"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    module: Optional[str] = Query(None, description="模块筛选")
):
    """获取系统日志"""
    try:
        # 这里应该从实际的日志文件或日志系统获取数据
        # 目前返回模拟数据
        mock_logs = [
            {
                "timestamp": time.time() - 300,
                "level": "INFO",
                "module": "trading_engine",
                "message": "交易引擎启动完成",
                "details": None
            },
            {
                "timestamp": time.time() - 240,
                "level": "INFO",
                "module": "market_data_engine",
                "message": "市场数据更新完成",
                "details": {"symbols": ["BTC/USDT:USDT", "ETH/USDT:USDT"]}
            },
            {
                "timestamp": time.time() - 180,
                "level": "WARNING",
                "module": "risk_manager",
                "message": "检测到较高的敞口比率",
                "details": {"exposure_ratio": 0.85}
            },
            {
                "timestamp": time.time() - 120,
                "level": "INFO",
                "module": "ai_opening_engine",
                "message": "AI开仓分析完成",
                "details": {"action": "no_action", "confidence": 65}
            },
            {
                "timestamp": time.time() - 60,
                "level": "ERROR",
                "module": "exchange_client",
                "message": "API请求失败",
                "details": {"error": "Rate limit exceeded", "retry_after": 60}
            }
        ]
        
        # 应用筛选
        filtered_logs = mock_logs
        if level:
            filtered_logs = [log for log in filtered_logs if log["level"] == level.upper()]
        if module:
            filtered_logs = [log for log in filtered_logs if log["module"] == module]
        
        # 应用限制
        filtered_logs = filtered_logs[:limit]
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "logs": filtered_logs,
                "statistics": {
                    "total": len(mock_logs),
                    "error": len([log for log in mock_logs if log["level"] == "ERROR"]),
                    "warning": len([log for log in mock_logs if log["level"] == "WARNING"])
                }
            },
            "filters": {
                "level": level,
                "module": module,
                "limit": limit
            }
        })

    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.delete("/logs")
async def clear_system_logs():
    """清空系统日志"""
    try:
        # 这里应该实际清空日志文件或数据库中的日志记录
        # 目前只记录操作日志
        logger.warning("系统日志已被清空")

        return JSONResponse(content={
            "success": True,
            "message": "系统日志已清空",
            "cleared_count": 0  # 实际实现时应该返回清空的日志数量
        })

    except Exception as e:
        logger.error(f"清空系统日志失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.get("/performance")
async def get_system_performance():
    """获取系统性能指标"""
    try:
        import psutil
        
        # 获取系统性能指标
        performance = {
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "used": psutil.virtual_memory().used,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "used": psutil.disk_usage('/').used,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "network": psutil.net_io_counters()._asdict(),
            "processes": len(psutil.pids()),
            "timestamp": time.time()
        }
        
        return JSONResponse(content=performance)
        
    except Exception as e:
        logger.error(f"获取系统性能失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/version")
async def get_system_version():
    """获取系统版本信息"""
    try:
        version_info = {
            "version": "1.0.0",
            "build_date": "2024-01-01",
            "git_commit": "abc123def456",
            "python_version": sys.version,
            "dependencies": {
                "fastapi": "0.104.1",
                "ccxt": "4.1.0",
                "pandas": "2.1.0",
                "numpy": "1.24.0",
                "ta-lib": "0.4.28"
            }
        }
        
        return JSONResponse(content=version_info)
        
    except Exception as e:
        logger.error(f"获取版本信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def format_uptime(seconds: float) -> str:
    """格式化运行时间"""
    if seconds < 60:
        return f"{int(seconds)}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}分{secs}秒"
    elif seconds < 86400:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}小时{minutes}分"
    else:
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        return f"{days}天{hours}小时"
