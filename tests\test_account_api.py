#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户API真实测试脚本

此脚本用于测试账户数据获取的完整流程：
1. 测试数据库中的交易所配置
2. 测试交易所客户端连接
3. 测试账户数据获取
4. 测试前端API响应
"""

import sys
import asyncio
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.database_manager import DatabaseManager
from src.core.exchange_client import ExchangeClient


async def test_database_config():
    """测试数据库中的交易所配置"""
    print("=== 1. 测试数据库交易所配置 ===")
    
    try:
        db_manager = DatabaseManager()
        exchange_config = db_manager.load_exchange_config()
        
        if exchange_config:
            print(f"✅ 找到交易所配置:")
            print(f"   交易所: {exchange_config.exchange_name}")
            print(f"   API Key: {exchange_config.api_key[:8]}...")
            print(f"   Secret Key: {exchange_config.secret_key[:8]}...")
            print(f"   Passphrase: {exchange_config.passphrase[:4] if exchange_config.passphrase else 'None'}...")
            print(f"   沙盒模式: {exchange_config.sandbox_mode}")
            return exchange_config
        else:
            print("❌ 未找到交易所配置")
            return None
            
    except Exception as e:
        print(f"❌ 数据库配置测试失败: {e}")
        return None


async def test_exchange_connection(exchange_config):
    """测试交易所连接"""
    print("\n=== 2. 测试交易所连接 ===")
    
    if not exchange_config:
        print("❌ 跳过连接测试：无交易所配置")
        return None
    
    try:
        exchange_client = ExchangeClient(exchange_config)
        print(f"✅ 创建交易所客户端: {exchange_config.exchange_name}")
        
        # 测试连接
        connected = exchange_client.connect()
        if connected:
            print("✅ 交易所连接成功")
            return exchange_client
        else:
            print("❌ 交易所连接失败")
            return None
            
    except Exception as e:
        print(f"❌ 交易所连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_account_data(exchange_client):
    """测试账户数据获取"""
    print("\n=== 3. 测试账户数据获取 ===")
    
    if not exchange_client:
        print("❌ 跳过账户数据测试：无交易所连接")
        return None
    
    try:
        # 获取账户余额
        print("正在获取账户余额...")
        balance_data = exchange_client.fetch_balance()
        
        if balance_data:
            print("✅ 成功获取账户余额:")
            print(f"   原始数据: {balance_data}")
            
            # 解析余额数据
            total_balance = 0.0
            available_balance = 0.0
            
            if 'total' in balance_data:
                for currency, amount in balance_data['total'].items():
                    if amount > 0:
                        print(f"   {currency}: 总量={amount}, 可用={balance_data.get('free', {}).get(currency, 0)}")
                        if currency == 'USDT':
                            total_balance += amount
                            available_balance += balance_data.get('free', {}).get(currency, 0)
            
            print(f"✅ 计算结果: 总资产={total_balance} USDT, 可用余额={available_balance} USDT")
            return {
                "total_balance_usdt": total_balance,
                "available_balance_usdt": available_balance,
                "raw_data": balance_data
            }
        else:
            print("❌ 获取账户余额失败：返回空数据")
            return None
            
    except Exception as e:
        print(f"❌ 账户数据获取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 断开连接
        if exchange_client:
            exchange_client.disconnect()
            print("✅ 交易所连接已断开")


def test_frontend_api():
    """测试前端API"""
    print("\n=== 4. 测试前端API ===")
    
    try:
        # 测试账户API
        print("正在调用前端账户API...")
        response = requests.get("http://127.0.0.1:8000/api/dashboard/account", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 前端API调用成功:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应数据: {data}")
            
            # 检查关键字段
            total_balance = data.get('total_balance_usdt', 0)
            available_balance = data.get('available_balance_usdt', 0)
            exchange_connected = data.get('exchange_connected', False)
            
            print(f"✅ 解析结果:")
            print(f"   总资产: {total_balance} USDT")
            print(f"   可用余额: {available_balance} USDT")
            print(f"   交易所连接: {exchange_connected}")
            
            if total_balance == 0 and available_balance == 0:
                print("⚠️  警告: 账户余额为0，可能是配置问题或沙盒环境")
            
            return data
        else:
            print(f"❌ 前端API调用失败: 状态码={response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 前端API测试失败: {e}")
        return None


async def main():
    """主测试函数"""
    print("开始账户API真实测试...")
    print("=" * 50)
    
    # 1. 测试数据库配置
    exchange_config = await test_database_config()
    
    # 2. 测试交易所连接
    exchange_client = await test_exchange_connection(exchange_config)
    
    # 3. 测试账户数据获取
    account_data = await test_account_data(exchange_client)
    
    # 4. 测试前端API
    api_data = test_frontend_api()
    
    # 5. 总结测试结果
    print("\n" + "=" * 50)
    print("=== 测试结果总结 ===")
    
    if exchange_config:
        print("✅ 数据库配置: 正常")
    else:
        print("❌ 数据库配置: 失败")
    
    if exchange_client:
        print("✅ 交易所连接: 正常")
    else:
        print("❌ 交易所连接: 失败")
    
    if account_data:
        print("✅ 账户数据获取: 正常")
    else:
        print("❌ 账户数据获取: 失败")
    
    if api_data:
        print("✅ 前端API: 正常")
    else:
        print("❌ 前端API: 失败")
    
    # 6. 问题诊断
    print("\n=== 问题诊断 ===")
    if not exchange_config:
        print("🔧 建议: 请先配置交易所API密钥")
    elif not exchange_client:
        print("🔧 建议: 检查API密钥是否正确，网络是否正常")
    elif not account_data:
        print("🔧 建议: 检查交易所API权限，确保有查询余额权限")
    elif api_data and api_data.get('total_balance_usdt', 0) == 0:
        print("🔧 建议: 检查是否在沙盒环境，或账户确实无余额")
    else:
        print("🎉 所有测试通过！")


if __name__ == "__main__":
    asyncio.run(main())
