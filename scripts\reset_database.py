#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库重置脚本

此脚本用于清理数据库中的所有加密数据，解决加密密钥不一致的问题。
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def reset_database():
    """重置数据库，清理所有加密数据"""
    db_path = project_root / "data" / "trading_system.db"
    
    print("=== 数据库重置脚本 ===")
    print(f"数据库路径: {db_path}")
    
    if not db_path.exists():
        print("✅ 数据库文件不存在，无需清理")
        return True
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 清理交易所配置表
        cursor.execute("DELETE FROM exchange_config")
        deleted_exchange = cursor.rowcount
        print(f"✅ 已清理交易所配置: {deleted_exchange} 条记录")
        
        # 清理AI配置表
        cursor.execute("DELETE FROM ai_config")
        deleted_ai = cursor.rowcount
        print(f"✅ 已清理AI配置: {deleted_ai} 条记录")
        
        # 清理交易参数表（可选，这些通常不加密）
        cursor.execute("DELETE FROM trading_parameters")
        deleted_trading = cursor.rowcount
        print(f"✅ 已清理交易参数: {deleted_trading} 条记录")
        
        # 清理风险参数表（可选，这些通常不加密）
        try:
            cursor.execute("DELETE FROM risk_parameters")
            deleted_risk = cursor.rowcount
            print(f"✅ 已清理风险参数: {deleted_risk} 条记录")
        except sqlite3.OperationalError as e:
            if "no such table" in str(e):
                print("ℹ️  风险参数表不存在，跳过清理")
            else:
                raise
        
        # 清理选择的交易对表
        cursor.execute("DELETE FROM selected_symbols")
        deleted_symbols = cursor.rowcount
        print(f"✅ 已清理选择的交易对: {deleted_symbols} 条记录")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("\n🎉 数据库重置完成！")
        print("现在可以重新配置系统了。")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库重置失败: {e}")
        return False


def cleanup_lock_files():
    """清理数据库锁定文件"""
    db_path = project_root / "data" / "trading_system.db"
    lock_files = [
        db_path.with_suffix('.db-wal'),
        db_path.with_suffix('.db-shm'),
        db_path.parent / f"{db_path.name}-journal"
    ]
    
    cleaned_any = False
    for lock_file in lock_files:
        if lock_file.exists():
            try:
                lock_file.unlink()
                print(f"✅ 已删除锁定文件: {lock_file.name}")
                cleaned_any = True
            except Exception as e:
                print(f"⚠️  无法删除锁定文件 {lock_file.name}: {e}")
    
    if not cleaned_any:
        print("ℹ️  未发现数据库锁定文件")


def main():
    """主函数"""
    print("开始清理数据库锁定文件...")
    cleanup_lock_files()
    
    print("\n开始重置数据库...")
    if reset_database():
        print("\n✅ 数据库重置成功！")
        print("\n下一步操作：")
        print("1. 重新启动系统: python main.py")
        print("2. 通过Web界面重新配置交易所和AI设置")
        print("3. 访问 http://127.0.0.1:8000/settings 进行配置")
    else:
        print("\n❌ 数据库重置失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
