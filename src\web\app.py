#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统FastAPI应用主文件

此模块是Web应用的入口点，包括：
1. FastAPI应用初始化
2. 路由注册
3. 中间件配置
4. 静态文件服务
5. 模板引擎配置
"""

import os
import sys
import time
from pathlib import Path
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入路由模块
from src.web.routes import dashboard, trading, system, config

from src.utils.logger import get_logger
from src.web.routes import dashboard, trading, config, system
# from config.settings import Settings  # 暂时注释掉，避免导入错误

logger = get_logger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="DeepSeek量化交易系统",
    description="基于DeepSeek AI的加密货币永续合约全自动量化交易系统",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加Gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 配置静态文件服务
static_dir = Path(__file__).parent / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 配置模板引擎
templates_dir = Path(__file__).parent / "templates"
templates = Jinja2Templates(directory=str(templates_dir))

# 注册路由
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["仪表板"])
app.include_router(trading.router, prefix="/api/trading", tags=["交易"])
app.include_router(system.router, prefix="/api/system", tags=["系统"])
app.include_router(config.router, prefix="/api/config", tags=["配置"])

# 全局变量存储系统状态
system_state = {
    "trading_system": None,
    "is_running": False,
    "last_update": None
}


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    
    # 记录请求开始
    logger.info(f"请求开始: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录请求完成
        logger.info(f"请求完成: {request.method} {request.url} - "
                   f"状态码: {response.status_code} - 耗时: {process_time:.3f}s")
        
        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        # 记录请求错误
        process_time = time.time() - start_time
        logger.error(f"请求错误: {request.method} {request.url} - "
                    f"错误: {str(e)} - 耗时: {process_time:.3f}s")
        raise


@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """404错误处理器"""
    if request.url.path.startswith("/api/"):
        return JSONResponse(
            status_code=404,
            content={"error": "API端点未找到", "path": request.url.path}
        )
    else:
        return templates.TemplateResponse(
            "error.html",
            {"request": request, "error_code": 404, "error_message": "页面未找到"}
        )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    """500错误处理器"""
    logger.error(f"内部服务器错误: {str(exc)}")
    
    if request.url.path.startswith("/api/"):
        return JSONResponse(
            status_code=500,
            content={"error": "内部服务器错误", "message": str(exc)}
        )
    else:
        return templates.TemplateResponse(
            "error.html",
            {"request": request, "error_code": 500, "error_message": "内部服务器错误"}
        )


# 注册路由
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["仪表板"])
app.include_router(trading.router, prefix="/api/trading", tags=["交易"])
app.include_router(config.router, prefix="/api/config", tags=["配置"])
app.include_router(system.router, prefix="/api/system", tags=["系统"])


@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """主页路由"""
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "title": "DeepSeek量化交易系统"}
    )


@app.get("/positions", response_class=HTMLResponse)
async def positions_page(request: Request):
    """持仓管理页面"""
    return templates.TemplateResponse(
        "positions.html",
        {"request": request, "title": "持仓管理"}
    )


@app.get("/ai-decisions", response_class=HTMLResponse)
async def ai_decisions_page(request: Request):
    """AI决策页面"""
    return templates.TemplateResponse(
        "ai_decisions.html",
        {"request": request, "title": "AI决策"}
    )


@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    """系统设置页面"""
    return templates.TemplateResponse(
        "settings.html",
        {"request": request, "title": "系统设置"}
    )


@app.get("/test", response_class=HTMLResponse)
async def test_page(request: Request):
    """模块加载测试页面"""
    return templates.TemplateResponse(
        "test.html",
        {"request": request, "title": "模块加载测试"}
    )


@app.get("/logs", response_class=HTMLResponse)
async def logs_page(request: Request):
    """日志查看页面"""
    return templates.TemplateResponse(
        "logs.html",
        {"request": request, "title": "系统日志"}
    )


# 保留健康检查API，因为这是基础功能
@app.get("/api/health")
async def health_check():
    """健康检查API"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "system_running": system_state["is_running"]
    }








def get_system_state() -> Dict[str, Any]:
    """获取系统状态"""
    return system_state.copy()


def update_system_state(key: str, value: Any):
    """更新系统状态"""
    system_state[key] = value
    system_state["last_update"] = time.time()


def get_templates():
    """获取模板引擎实例"""
    return templates


if __name__ == "__main__":
    import time

    # 启动应用
    logger.info("启动DeepSeek量化交易系统Web应用")

    uvicorn.run(
        "src.web.app:app",
        host="127.0.0.1",
        port=8080,
        reload=True,
        log_level="info"
    )