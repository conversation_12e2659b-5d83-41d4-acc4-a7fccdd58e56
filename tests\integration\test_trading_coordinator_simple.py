#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易协调器简化测试

测试核心功能而不运行完整的交易循环。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.trading_coordinator import TradingCoordinator, TradingConfig, SystemState
from src.ai.deepseek_client import DeepSeekConfig
from src.data.models import ExchangeConfig, RiskParameters


async def test_trading_coordinator_simple():
    """简化的交易协调器测试"""
    print("=== 交易协调器简化测试 ===")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建DeepSeek配置
    deepseek_config = DeepSeekConfig(
        api_key="***********************************",
        model="deepseek-chat",
        temperature=0.3,
        max_tokens=1000
    )
    
    # 创建风险参数
    risk_params = RiskParameters(
        max_leverage=5,
        max_position_ratio=0.2,
        min_balance_threshold=100.0,
        opening_confidence_threshold=80,
        position_confidence_threshold=70
    )
    
    # 创建交易配置
    trading_config = TradingConfig(
        symbols=["BTC/USDT:USDT"],
        timeframes=["15m", "1h"],
        analysis_interval=300,  # 5分钟
        max_positions=1,
        enable_opening=False,  # 禁用开仓
        enable_position_management=False  # 禁用持仓管理
    )
    
    # 创建客户端和协调器
    exchange_client = ExchangeClient(exchange_config)
    coordinator = None
    
    try:
        # 1. 创建交易协调器
        print("\n1. 创建交易协调器...")
        coordinator = TradingCoordinator(
            exchange_client=exchange_client,
            deepseek_config=deepseek_config,
            risk_params=risk_params,
            trading_config=trading_config
        )
        print("✅ 交易协调器创建成功")
        
        # 2. 测试各个组件的连接
        print("\n2. 测试组件连接...")
        
        # 测试交易所连接
        exchange_client.connect()
        print(f"✅ 交易所连接成功: {exchange_client.is_connected}")
        
        # 测试AI引擎连接
        ai_opening_ok = coordinator.ai_opening_engine.test_connection()
        ai_position_ok = coordinator.ai_position_engine.test_connection()
        print(f"✅ AI开仓引擎连接: {ai_opening_ok}")
        print(f"✅ AI持仓引擎连接: {ai_position_ok}")
        
        # 3. 测试数据获取
        print("\n3. 测试数据获取...")
        
        # 获取账户信息
        balance = exchange_client.fetch_balance()
        positions = exchange_client.fetch_positions()
        print(f"✅ 账户余额: {len(balance)} 个币种")
        print(f"✅ 持仓信息: {len(positions)} 个持仓")
        
        # 获取市场数据
        test_symbol = "BTC/USDT:USDT"
        market_data = {}
        for timeframe in ["15m", "1h"]:
            ohlcv_data = exchange_client.fetch_ohlcv_sync(test_symbol, timeframe, 300)
            market_data[timeframe] = ohlcv_data
            print(f"✅ {test_symbol} {timeframe}: {len(ohlcv_data)} 条K线")
        
        # 4. 测试技术分析
        print("\n4. 测试技术分析...")
        
        technical_indicators = coordinator.technical_analysis_engine.analyze_multi_timeframe(market_data)
        print(f"✅ 技术分析完成: {len(technical_indicators)} 个时间周期")
        
        for timeframe, indicators in technical_indicators.items():
            print(f"  {timeframe}: 趋势指标 {len(indicators.trend_indicators)} 个")
        
        # 5. 测试AI分析（如果有持仓）
        if positions:
            print("\n5. 测试AI持仓分析...")
            
            position = positions[0]
            current_price = market_data["15m"][-1].close
            
            # 计算利润回撤
            price_history = [candle.close for candle in market_data["15m"][-50:]]
            profit_drawdown = coordinator.ai_position_engine.calculate_profit_drawdown(
                position, current_price, price_history
            )
            
            print(f"✅ 利润回撤计算完成:")
            print(f"  当前收益率: {profit_drawdown.current_profit_rate:.2%}")
            print(f"  最高收益率: {profit_drawdown.max_profit_rate:.2%}")
            print(f"  回撤比例: {profit_drawdown.drawdown_percentage:.2%}")
            
            # AI持仓分析
            decision = await coordinator.ai_position_engine.analyze_position_management(
                position=position,
                technical_data=technical_indicators,
                profit_drawdown=profit_drawdown,
                current_price=current_price
            )
            
            print(f"✅ AI持仓分析完成:")
            print(f"  建议操作: {decision.action}")
            print(f"  置信度: {decision.confidence}%")
            print(f"  紧急程度: {decision.urgency}")
        
        else:
            print("\n5. 测试AI开仓分析...")
            
            current_price = market_data["15m"][-1].close
            
            # AI开仓分析
            decision = await coordinator.ai_opening_engine.analyze_market_for_opening(
                symbol=test_symbol,
                technical_data=technical_indicators,
                current_price=current_price
            )
            
            print(f"✅ AI开仓分析完成:")
            print(f"  建议操作: {decision.action}")
            print(f"  置信度: {decision.confidence}%")
            print(f"  风险等级: {decision.risk_level}")
        
        # 6. 测试风险管理
        print("\n6. 测试风险管理...")
        
        # 测试开仓风险验证
        risk_result = coordinator.risk_manager.validate_opening_order(
            symbol=test_symbol,
            side="buy",
            amount=0.01,
            leverage=5,
            price=current_price,
            balance=balance
        )
        
        print(f"✅ 开仓风险检查:")
        print(f"  通过: {risk_result.passed}")
        print(f"  风险等级: {risk_result.risk_level}")
        print(f"  警告数量: {len(risk_result.warnings)}")
        print(f"  错误数量: {len(risk_result.errors)}")
        
        # 投资组合风险检查
        portfolio_risk = coordinator.risk_manager.check_portfolio_risk(positions, balance)
        print(f"✅ 投资组合风险:")
        print(f"  风险等级: {portfolio_risk.risk_level}")
        print(f"  通过: {portfolio_risk.passed}")
        
        # 7. 测试系统状态
        print("\n7. 测试系统状态...")
        
        status = coordinator.get_system_status()
        print(f"✅ 系统状态:")
        print(f"  状态: {status.state.value}")
        print(f"  运行时间: {status.uptime:.2f}秒")
        print(f"  总分析次数: {status.total_analyses}")
        print(f"  活跃持仓: {status.active_positions}")
        
        # 8. 测试配置更新
        print("\n8. 测试配置更新...")
        
        new_risk_params = RiskParameters(
            max_leverage=3,
            max_position_ratio=0.1,
            min_balance_threshold=200.0,
            opening_confidence_threshold=85,
            position_confidence_threshold=75
        )
        
        coordinator.update_risk_parameters(new_risk_params)
        print(f"✅ 风险参数更新成功")
        print(f"  新的最大杠杆: {coordinator.risk_manager.risk_params.max_leverage}")
        
        new_trading_config = TradingConfig(
            symbols=["ETH/USDT:USDT"],
            timeframes=["1h"],
            analysis_interval=600,
            max_positions=1,
            enable_opening=False,
            enable_position_management=False
        )
        
        coordinator.update_trading_config(new_trading_config)
        print(f"✅ 交易配置更新成功")
        print(f"  新的交易对: {coordinator.trading_config.symbols}")
        
        print("\n🎉 所有交易协调器核心功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 交易协调器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if coordinator:
            try:
                exchange_client.disconnect()
                print("\n🧹 交易所连接已断开")
            except Exception as e:
                print(f"断开连接时出错: {e}")
        
        print("🧹 资源清理完成")


if __name__ == "__main__":
    success = asyncio.run(test_trading_coordinator_simple())
    if success:
        print("\n✅ 交易协调器核心功能测试全部通过！")
    else:
        print("\n❌ 交易协调器核心功能测试失败！")
        sys.exit(1)
