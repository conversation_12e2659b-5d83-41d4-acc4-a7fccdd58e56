#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统AI开仓引擎

此模块负责在无持仓时分析市场数据，给出开仓建议，包括：
1. 技术指标分析
2. 市场趋势判断
3. 开仓方向决策
4. 风险评估
5. 置信度计算
"""

import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from src.ai.deepseek_client import DeepSeekClient, DeepSeekConfig, ChatMessage
from src.data.models import TechnicalIndicators, AIDecision
from src.utils.logger import get_logger, log_execution_time
from src.utils.exceptions import AIServiceError

logger = get_logger(__name__)


@dataclass
class OpeningDecision:
    """开仓决策"""
    action: str  # "open_long", "open_short", "no_action"
    confidence: int  # 0-100
    reasoning: str  # 决策理由
    suggested_leverage: int  # 建议杠杆倍数
    risk_level: str  # "low", "medium", "high"
    entry_price_range: Optional[Dict[str, float]] = None  # 建议入场价格区间
    stop_loss_price: Optional[float] = None  # 建议止损价格
    take_profit_price: Optional[float] = None  # 建议止盈价格
    position_size_ratio: Optional[float] = None  # 建议仓位比例


class AIOpeningEngine:
    """AI开仓引擎类
    
    负责在无持仓时分析市场数据并给出开仓建议。
    """
    
    def __init__(self, deepseek_config: DeepSeekConfig):
        """初始化AI开仓引擎
        
        Args:
            deepseek_config: DeepSeek配置
        """
        self.deepseek_config = deepseek_config
        self.client = DeepSeekClient(deepseek_config)
        
        # 开仓决策阈值
        self.min_confidence_threshold = 70  # 最小置信度阈值
        self.max_leverage = 20  # 最大杠杆倍数
        
        # 系统提示词
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词
        
        Returns:
            str: 系统提示词
        """
        return """你是一个专业的加密货币永续合约量化交易AI助手，专门负责开仓决策分析。

你的任务是：
1. 分析多时间周期的技术指标数据
2. 判断市场趋势和交易机会
3. 给出开仓建议（做多、做空或不开仓）
4. 评估风险等级和置信度
5. 提供具体的交易参数建议

分析原则：
- 综合考虑趋势指标、震荡指标、波动率指标、成交量指标和支撑阻力位
- 多时间周期共振分析，优先考虑高时间周期的趋势方向
- 严格控制风险，只在高概率机会时建议开仓
- 考虑市场波动性和流动性
- 避免在震荡市场中频繁开仓

响应格式要求：
请严格按照以下JSON格式返回分析结果，不要包含任何其他文本：

{
    "action": "open_long" | "open_short" | "no_action",
    "confidence": 整数(0-100),
    "reasoning": "详细的分析理由，包括关键技术指标的解读",
    "suggested_leverage": 整数(1-20),
    "risk_level": "low" | "medium" | "high",
    "entry_price_range": {
        "min": 浮点数,
        "max": 浮点数
    },
    "stop_loss_price": 浮点数或null,
    "take_profit_price": 浮点数或null,
    "position_size_ratio": 浮点数(0.1-1.0)
}

注意事项：
- confidence低于70时，action应该是"no_action"
- 高风险市场条件下，建议较低的杠杆倍数
- 止损价格应该基于技术支撑阻力位设置
- 仓位比例应该根据风险等级调整"""
    
    def _build_analysis_prompt(self, symbol: str, technical_data: Dict[str, TechnicalIndicators], 
                              current_price: float) -> str:
        """构建分析提示词
        
        Args:
            symbol: 交易对符号
            technical_data: 技术指标数据
            current_price: 当前价格
        
        Returns:
            str: 分析提示词
        """
        prompt_parts = [
            f"请分析 {symbol} 的开仓机会。",
            f"当前价格: {current_price}",
            "",
            "=== 技术指标分析 ==="
        ]
        
        # 添加各时间周期的技术指标
        for timeframe, indicators in technical_data.items():
            prompt_parts.append(f"\n【{timeframe} 时间周期】")
            
            # 趋势指标
            if indicators.trend_indicators:
                prompt_parts.append("趋势指标:")
                for key, value in indicators.trend_indicators.items():
                    if value is not None:
                        prompt_parts.append(f"  {key}: {value:.4f}")
            
            # 震荡指标
            if indicators.oscillator_indicators:
                prompt_parts.append("震荡指标:")
                for key, value in indicators.oscillator_indicators.items():
                    if value is not None:
                        prompt_parts.append(f"  {key}: {value:.2f}")
            
            # 波动率指标
            if indicators.volatility_indicators:
                prompt_parts.append("波动率指标:")
                for key, value in indicators.volatility_indicators.items():
                    if value is not None:
                        prompt_parts.append(f"  {key}: {value:.4f}")
            
            # 成交量指标
            if indicators.volume_indicators:
                prompt_parts.append("成交量指标:")
                for key, value in indicators.volume_indicators.items():
                    if value is not None:
                        prompt_parts.append(f"  {key}: {value:.2f}")
            
            # 支撑阻力
            if indicators.support_resistance:
                prompt_parts.append("支撑阻力位:")
                for key, value in indicators.support_resistance.items():
                    if value is not None:
                        prompt_parts.append(f"  {key}: {value:.4f}")
        
        prompt_parts.extend([
            "",
            "请基于以上技术指标数据，进行综合分析并给出开仓建议。",
            "特别关注多时间周期的趋势一致性、关键支撑阻力位、以及成交量确认。"
        ])
        
        return "\n".join(prompt_parts)
    
    @log_execution_time("AI开仓分析")
    async def analyze_market_for_opening(self, symbol: str, technical_data: Dict[str, TechnicalIndicators], 
                                       current_price: float) -> OpeningDecision:
        """分析市场开仓机会
        
        Args:
            symbol: 交易对符号
            technical_data: 技术指标数据
            current_price: 当前价格
        
        Returns:
            OpeningDecision: 开仓决策
        
        Raises:
            AIServiceError: AI服务异常
        """
        try:
            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(symbol, technical_data, current_price)
            
            # 准备消息
            messages = [
                ChatMessage(role="system", content=self.system_prompt),
                ChatMessage(role="user", content=analysis_prompt)
            ]
            
            # 发送请求到DeepSeek
            async with self.client as client:
                response = await client.chat_completion_async(
                    messages=messages,
                    temperature=0.3,  # 较低的温度以获得更一致的结果
                    max_tokens=1000
                )
            
            # 解析响应
            decision = self._parse_ai_response(response.content, symbol)
            
            logger.info(f"AI开仓分析完成: {symbol} - {decision.action} (置信度: {decision.confidence}%)")
            return decision
        
        except Exception as e:
            logger.error(f"AI开仓分析失败: {e}")
            raise AIServiceError(f"AI开仓分析失败: {e}")
    
    def analyze_market_for_opening_sync(self, symbol: str, technical_data: Dict[str, TechnicalIndicators],
                                      current_price: float) -> OpeningDecision:
        """同步分析市场开仓机会（已弃用，建议使用异步版本）

        ⚠️ 警告：此方法会阻塞线程，建议使用 analyze_market_for_opening() 异步版本

        Args:
            symbol: 交易对符号
            technical_data: 技术指标数据
            current_price: 当前价格

        Returns:
            OpeningDecision: 开仓决策

        Raises:
            AIServiceError: AI服务异常
        """
        logger.warning("使用了同步AI分析方法，建议改用异步版本以避免阻塞线程")

        # 使用asyncio.run来运行异步版本
        import asyncio
        try:
            return asyncio.run(self.analyze_market_for_opening(symbol, technical_data, current_price))
        except Exception as e:
            logger.error(f"同步AI开仓分析失败: {e}")
            raise AIServiceError(f"同步AI开仓分析失败: {e}")
    
    def _parse_ai_response(self, response_content: str, symbol: str) -> OpeningDecision:
        """解析AI响应
        
        Args:
            response_content: AI响应内容
            symbol: 交易对符号
        
        Returns:
            OpeningDecision: 解析后的开仓决策
        
        Raises:
            AIServiceError: 解析失败时抛出异常
        """
        try:
            # 尝试提取JSON部分
            content = response_content.strip()
            
            # 如果响应包含其他文本，尝试提取JSON部分
            if not content.startswith('{'):
                # 查找JSON开始和结束位置
                start_idx = content.find('{')
                end_idx = content.rfind('}')
                
                if start_idx != -1 and end_idx != -1:
                    content = content[start_idx:end_idx + 1]
                else:
                    raise ValueError("响应中未找到有效的JSON格式")
            
            # 解析JSON
            data = json.loads(content)
            
            # 验证必需字段
            required_fields = ["action", "confidence", "reasoning"]
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"响应中缺少必需字段: {field}")
            
            # 验证action值
            valid_actions = ["open_long", "open_short", "no_action"]
            if data["action"] not in valid_actions:
                raise ValueError(f"无效的action值: {data['action']}")
            
            # 验证confidence范围
            confidence = int(data["confidence"])
            if not 0 <= confidence <= 100:
                raise ValueError(f"confidence值超出范围: {confidence}")
            
            # 创建开仓决策对象
            decision = OpeningDecision(
                action=data["action"],
                confidence=confidence,
                reasoning=data["reasoning"],
                suggested_leverage=min(int(data.get("suggested_leverage", 5)), self.max_leverage),
                risk_level=data.get("risk_level", "medium"),
                entry_price_range=data.get("entry_price_range"),
                stop_loss_price=data.get("stop_loss_price"),
                take_profit_price=data.get("take_profit_price"),
                position_size_ratio=data.get("position_size_ratio", 0.3)
            )
            
            # 记录决策到AI决策表
            ai_decision = AIDecision(
                action=decision.action,
                confidence=decision.confidence,
                reasoning=decision.reasoning,
                timestamp=int(time.time()),
                symbol=symbol,
                engine_type="opening"
            )
            
            return decision
        
        except json.JSONDecodeError as e:
            logger.error(f"AI响应JSON解析失败: {e}")
            logger.error(f"响应内容: {response_content}")
            raise AIServiceError(f"AI响应格式错误: {e}")
        
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            logger.error(f"响应内容: {response_content}")
            raise AIServiceError(f"解析AI响应失败: {e}")
    
    def should_open_position(self, decision: OpeningDecision) -> bool:
        """判断是否应该开仓
        
        Args:
            decision: 开仓决策
        
        Returns:
            bool: 是否应该开仓
        """
        # 检查基本条件
        if decision.action == "no_action":
            return False
        
        if decision.confidence < self.min_confidence_threshold:
            logger.info(f"置信度不足，不开仓: {decision.confidence}% < {self.min_confidence_threshold}%")
            return False
        
        if decision.action not in ["open_long", "open_short"]:
            logger.warning(f"无效的开仓动作: {decision.action}")
            return False
        
        logger.info(f"满足开仓条件: {decision.action} (置信度: {decision.confidence}%)")
        return True
    
    def get_trading_parameters(self, decision: OpeningDecision, current_price: float, 
                             available_balance: float) -> Dict[str, Any]:
        """获取交易参数
        
        Args:
            decision: 开仓决策
            current_price: 当前价格
            available_balance: 可用余额
        
        Returns:
            Dict[str, Any]: 交易参数
        """
        # 计算仓位大小
        position_ratio = min(decision.position_size_ratio or 0.3, 0.8)  # 最大80%仓位
        position_value = available_balance * position_ratio
        
        # 计算交易数量（考虑杠杆）
        leverage = min(decision.suggested_leverage, self.max_leverage)
        trade_amount = position_value * leverage / current_price
        
        # 构建交易参数
        trading_params = {
            "side": "buy" if decision.action == "open_long" else "sell",
            "amount": trade_amount,
            "leverage": leverage,
            "position_ratio": position_ratio,
            "stop_loss_price": decision.stop_loss_price,
            "take_profit_price": decision.take_profit_price,
            "risk_level": decision.risk_level,
            "confidence": decision.confidence,
            "reasoning": decision.reasoning
        }
        
        return trading_params
    
    def test_connection(self) -> bool:
        """测试DeepSeek连接
        
        Returns:
            bool: 连接是否成功
        """
        return self.client.test_connection()
