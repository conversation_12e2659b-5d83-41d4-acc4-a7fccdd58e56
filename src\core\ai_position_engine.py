#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统AI持仓引擎

此模块负责在有持仓时分析持仓状态，给出持仓管理建议，包括：
1. 持仓盈亏分析
2. 利润回撤计算
3. 持仓管理决策
4. 止盈止损调整
5. 风险控制建议
"""

import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from src.ai.deepseek_client import DeepSeekClient, DeepSeekConfig, ChatMessage
from src.data.models import TechnicalIndicators, Position, AIDecision
from src.utils.logger import get_logger, log_execution_time
from src.utils.exceptions import AIServiceError

logger = get_logger(__name__)


@dataclass
class ProfitDrawdown:
    """利润回撤数据"""
    max_profit_rate: float      # 历史最高收益率
    current_profit_rate: float  # 当前收益率
    drawdown_amount: float      # 回撤幅度
    drawdown_percentage: float  # 回撤比例


@dataclass
class PositionDecision:
    """持仓决策"""
    action: str  # "hold", "close_partial", "close_all", "adjust_stop_loss", "take_profit"
    confidence: int  # 0-100
    reasoning: str  # 决策理由
    close_ratio: Optional[float] = None  # 平仓比例 (0.0-1.0)
    new_stop_loss: Optional[float] = None  # 新的止损价格
    new_take_profit: Optional[float] = None  # 新的止盈价格
    urgency: str = "normal"  # "low", "normal", "high", "urgent"
    risk_level: str = "medium"  # "low", "medium", "high"


class AIPositionEngine:
    """AI持仓引擎类
    
    负责在有持仓时分析持仓状态并给出持仓管理建议。
    """
    
    def __init__(self, deepseek_config: DeepSeekConfig):
        """初始化AI持仓引擎
        
        Args:
            deepseek_config: DeepSeek配置
        """
        self.deepseek_config = deepseek_config
        self.client = DeepSeekClient(deepseek_config)
        
        # 持仓管理阈值
        self.min_confidence_threshold = 60  # 最小置信度阈值
        self.max_drawdown_threshold = 0.5   # 最大回撤阈值 50%
        self.profit_lock_threshold = 0.3    # 利润锁定阈值 30%
        
        # 系统提示词
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词
        
        Returns:
            str: 系统提示词
        """
        return """你是一个专业的加密货币永续合约量化交易AI助手，专门负责持仓管理决策分析。

你的任务是：
1. 分析当前持仓的盈亏状况和利润回撤情况
2. 结合技术指标判断持仓的持续性
3. 给出持仓管理建议（持有、部分平仓、全部平仓、调整止损止盈）
4. 评估操作的紧急程度和风险等级
5. 提供具体的操作参数建议

分析原则：
- 优先保护已实现的利润，避免利润大幅回撤
- 结合技术指标判断趋势是否发生反转
- 考虑持仓时间和市场波动性
- 动态调整止损止盈位置
- 在趋势延续时保持持仓，在趋势反转时及时平仓

响应格式要求：
请严格按照以下JSON格式返回分析结果，不要包含任何其他文本：

{
    "action": "hold" | "close_partial" | "close_all" | "adjust_stop_loss" | "take_profit",
    "confidence": 整数(0-100),
    "reasoning": "详细的分析理由，包括持仓状况和技术指标分析",
    "close_ratio": 浮点数(0.0-1.0)或null,
    "new_stop_loss": 浮点数或null,
    "new_take_profit": 浮点数或null,
    "urgency": "low" | "normal" | "high" | "urgent",
    "risk_level": "low" | "medium" | "high"
}

注意事项：
- 当利润回撤超过30%时，考虑部分锁定利润
- 当利润回撤超过50%时，建议全部平仓
- 技术指标显示趋势反转时，提高操作紧急程度
- 止损位应该根据技术支撑阻力位动态调整
- 考虑持仓的风险收益比"""
    
    def calculate_profit_drawdown(self, position: Position, current_price: float, 
                                 price_history: List[float]) -> ProfitDrawdown:
        """计算利润回撤
        
        Args:
            position: 持仓信息
            current_price: 当前价格
            price_history: 价格历史数据
        
        Returns:
            ProfitDrawdown: 利润回撤数据
        """
        try:
            # 计算当前收益率
            if position.side.value.lower() in ['long', 'buy']:
                current_profit_rate = (current_price - position.entry_price) / position.entry_price
            else:  # short
                current_profit_rate = (position.entry_price - current_price) / position.entry_price
            
            # 计算历史最高收益率
            max_profit_rate = current_profit_rate
            
            for price in price_history:
                if position.side.value.lower() in ['long', 'buy']:
                    profit_rate = (price - position.entry_price) / position.entry_price
                else:  # short
                    profit_rate = (position.entry_price - price) / position.entry_price
                
                max_profit_rate = max(max_profit_rate, profit_rate)
            
            # 计算回撤
            if max_profit_rate > 0:
                drawdown_amount = max_profit_rate - current_profit_rate
                drawdown_percentage = drawdown_amount / max_profit_rate
            else:
                drawdown_amount = 0
                drawdown_percentage = 0
            
            return ProfitDrawdown(
                max_profit_rate=max_profit_rate,
                current_profit_rate=current_profit_rate,
                drawdown_amount=drawdown_amount,
                drawdown_percentage=drawdown_percentage
            )
        
        except Exception as e:
            logger.error(f"计算利润回撤失败: {e}")
            return ProfitDrawdown(0, 0, 0, 0)
    
    def _build_position_analysis_prompt(self, position: Position, technical_data: Dict[str, TechnicalIndicators],
                                      profit_drawdown: ProfitDrawdown, current_price: float) -> str:
        """构建持仓分析提示词
        
        Args:
            position: 持仓信息
            technical_data: 技术指标数据
            profit_drawdown: 利润回撤数据
            current_price: 当前价格
        
        Returns:
            str: 分析提示词
        """
        prompt_parts = [
            f"请分析 {position.symbol} 的持仓管理策略。",
            "",
            "=== 持仓信息 ===",
            f"交易对: {position.symbol}",
            f"持仓方向: {position.side.value}",
            f"持仓数量: {position.amount}",
            f"开仓价格: {position.entry_price}",
            f"当前价格: {current_price}",
            f"杠杆倍数: {position.leverage}x",
            f"未实现盈亏: {position.unrealized_pnl} ({position.unrealized_pnl_percentage:.2f}%)",
            f"已用保证金: {position.margin_used}",
            "",
            "=== 利润回撤分析 ===",
            f"历史最高收益率: {profit_drawdown.max_profit_rate:.4f} ({profit_drawdown.max_profit_rate*100:.2f}%)",
            f"当前收益率: {profit_drawdown.current_profit_rate:.4f} ({profit_drawdown.current_profit_rate*100:.2f}%)",
            f"利润回撤幅度: {profit_drawdown.drawdown_amount:.4f} ({profit_drawdown.drawdown_amount*100:.2f}%)",
            f"利润回撤比例: {profit_drawdown.drawdown_percentage:.4f} ({profit_drawdown.drawdown_percentage*100:.2f}%)",
            "",
            "=== 技术指标分析 ==="
        ]
        
        # 添加各时间周期的关键技术指标
        for timeframe, indicators in technical_data.items():
            prompt_parts.append(f"\n【{timeframe} 时间周期】")
            
            # 关键趋势指标
            if indicators.trend_indicators:
                trend_data = []
                for key in ['MACD', 'MACD_SIGNAL', 'MACD_HIST', 'ADX', 'PLUS_DI', 'MINUS_DI']:
                    if key in indicators.trend_indicators and indicators.trend_indicators[key] is not None:
                        trend_data.append(f"{key}: {indicators.trend_indicators[key]:.4f}")
                if trend_data:
                    prompt_parts.append("关键趋势指标: " + ", ".join(trend_data))
            
            # 关键震荡指标
            if indicators.oscillator_indicators:
                osc_data = []
                for key in ['RSI', 'STOCH_K', 'STOCH_D']:
                    if key in indicators.oscillator_indicators and indicators.oscillator_indicators[key] is not None:
                        osc_data.append(f"{key}: {indicators.oscillator_indicators[key]:.2f}")
                if osc_data:
                    prompt_parts.append("关键震荡指标: " + ", ".join(osc_data))
            
            # 关键支撑阻力位
            if indicators.support_resistance:
                sr_data = []
                for key in ['SUPPORT_1', 'RESISTANCE_1', 'PIVOT_POINT']:
                    if key in indicators.support_resistance and indicators.support_resistance[key] is not None:
                        sr_data.append(f"{key}: {indicators.support_resistance[key]:.4f}")
                if sr_data:
                    prompt_parts.append("关键支撑阻力: " + ", ".join(sr_data))
        
        prompt_parts.extend([
            "",
            "请基于以上持仓信息、利润回撤数据和技术指标，给出持仓管理建议。",
            "特别关注利润保护、趋势延续性、以及风险控制。"
        ])
        
        return "\n".join(prompt_parts)
    
    @log_execution_time("AI持仓分析")
    async def analyze_position_management(self, position: Position, technical_data: Dict[str, TechnicalIndicators],
                                        profit_drawdown: ProfitDrawdown, current_price: float) -> PositionDecision:
        """分析持仓管理策略
        
        Args:
            position: 持仓信息
            technical_data: 技术指标数据
            profit_drawdown: 利润回撤数据
            current_price: 当前价格
        
        Returns:
            PositionDecision: 持仓决策
        
        Raises:
            AIServiceError: AI服务异常
        """
        try:
            # 构建分析提示词
            analysis_prompt = self._build_position_analysis_prompt(
                position, technical_data, profit_drawdown, current_price
            )
            
            # 准备消息
            messages = [
                ChatMessage(role="system", content=self.system_prompt),
                ChatMessage(role="user", content=analysis_prompt)
            ]
            
            # 发送请求到DeepSeek
            async with self.client as client:
                response = await client.chat_completion_async(
                    messages=messages,
                    temperature=0.2,  # 更低的温度以获得更保守的决策
                    max_tokens=800
                )
            
            # 解析响应
            decision = self._parse_ai_response(response.content, position.symbol)
            
            logger.info(f"AI持仓分析完成: {position.symbol} - {decision.action} (置信度: {decision.confidence}%)")
            return decision
        
        except Exception as e:
            logger.error(f"AI持仓分析失败: {e}")
            raise AIServiceError(f"AI持仓分析失败: {e}")
    
    def _parse_ai_response(self, response_content: str, symbol: str) -> PositionDecision:
        """解析AI响应
        
        Args:
            response_content: AI响应内容
            symbol: 交易对符号
        
        Returns:
            PositionDecision: 解析后的持仓决策
        
        Raises:
            AIServiceError: 解析失败时抛出异常
        """
        try:
            # 尝试提取JSON部分
            content = response_content.strip()
            
            # 如果响应包含其他文本，尝试提取JSON部分
            if not content.startswith('{'):
                start_idx = content.find('{')
                end_idx = content.rfind('}')
                
                if start_idx != -1 and end_idx != -1:
                    content = content[start_idx:end_idx + 1]
                else:
                    raise ValueError("响应中未找到有效的JSON格式")
            
            # 解析JSON
            data = json.loads(content)
            
            # 验证必需字段
            required_fields = ["action", "confidence", "reasoning"]
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"响应中缺少必需字段: {field}")
            
            # 验证action值
            valid_actions = ["hold", "close_partial", "close_all", "adjust_stop_loss", "take_profit"]
            if data["action"] not in valid_actions:
                raise ValueError(f"无效的action值: {data['action']}")
            
            # 验证confidence范围
            confidence = int(data["confidence"])
            if not 0 <= confidence <= 100:
                raise ValueError(f"confidence值超出范围: {confidence}")
            
            # 创建持仓决策对象
            decision = PositionDecision(
                action=data["action"],
                confidence=confidence,
                reasoning=data["reasoning"],
                close_ratio=data.get("close_ratio"),
                new_stop_loss=data.get("new_stop_loss"),
                new_take_profit=data.get("new_take_profit"),
                urgency=data.get("urgency", "normal"),
                risk_level=data.get("risk_level", "medium")
            )
            
            # 记录决策到AI决策表
            ai_decision = AIDecision(
                action=decision.action,
                confidence=decision.confidence,
                reasoning=decision.reasoning,
                timestamp=int(time.time()),
                symbol=symbol,
                engine_type="position"
            )
            
            return decision
        
        except json.JSONDecodeError as e:
            logger.error(f"AI响应JSON解析失败: {e}")
            logger.error(f"响应内容: {response_content}")
            raise AIServiceError(f"AI响应格式错误: {e}")
        
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            logger.error(f"响应内容: {response_content}")
            raise AIServiceError(f"解析AI响应失败: {e}")
    
    def should_manage_position(self, decision: PositionDecision) -> bool:
        """判断是否应该执行持仓管理操作
        
        Args:
            decision: 持仓决策
        
        Returns:
            bool: 是否应该执行操作
        """
        # 检查基本条件
        if decision.action == "hold":
            return False
        
        if decision.confidence < self.min_confidence_threshold:
            logger.info(f"置信度不足，不执行持仓操作: {decision.confidence}% < {self.min_confidence_threshold}%")
            return False
        
        logger.info(f"满足持仓管理条件: {decision.action} (置信度: {decision.confidence}%)")
        return True
    
    def get_management_parameters(self, decision: PositionDecision, position: Position) -> Dict[str, Any]:
        """获取持仓管理参数
        
        Args:
            decision: 持仓决策
            position: 当前持仓
        
        Returns:
            Dict[str, Any]: 管理参数
        """
        params = {
            "action": decision.action,
            "confidence": decision.confidence,
            "urgency": decision.urgency,
            "risk_level": decision.risk_level,
            "reasoning": decision.reasoning
        }
        
        if decision.action in ["close_partial", "close_all"]:
            close_ratio = decision.close_ratio or (1.0 if decision.action == "close_all" else 0.5)
            params["close_amount"] = position.amount * close_ratio
            params["close_ratio"] = close_ratio
        
        if decision.new_stop_loss:
            params["new_stop_loss"] = decision.new_stop_loss
        
        if decision.new_take_profit:
            params["new_take_profit"] = decision.new_take_profit
        
        return params
    
    def test_connection(self) -> bool:
        """测试DeepSeek连接
        
        Returns:
            bool: 连接是否成功
        """
        return self.client.test_connection()
