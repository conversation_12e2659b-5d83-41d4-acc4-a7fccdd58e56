#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场数据引擎简单测试

使用同步方法测试市场数据引擎的基本功能。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.market_data_engine import MarketDataEngine
from src.data.models import ExchangeConfig


def test_market_data_engine_basic():
    """测试市场数据引擎基本功能"""
    print("=== 市场数据引擎基本功能测试 ===")
    
    # 创建配置
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建交易所客户端
    exchange_client = ExchangeClient(config)
    
    try:
        # 连接交易所
        print("1. 连接交易所...")
        exchange_client.connect()
        print("✅ 交易所连接成功")
        
        # 创建市场数据引擎
        print("\n2. 创建市场数据引擎...")
        symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT"]
        engine = MarketDataEngine(exchange_client, symbols)
        print(f"✅ 市场数据引擎创建成功，监控 {len(symbols)} 个交易对")
        
        # 测试获取多时间周期数据（同步）
        print("\n3. 测试同步获取多时间周期数据...")
        symbol = "BTC/USDT:USDT"
        timeframe_data = engine.fetch_multi_timeframe_data_sync(symbol)
        
        print(f"获取到 {len(timeframe_data)} 个时间周期的数据:")
        for timeframe, ohlcv_list in timeframe_data.items():
            print(f"  {timeframe}: {len(ohlcv_list)} 条K线数据")
            if ohlcv_list:
                latest = ohlcv_list[-1]
                print(f"    最新: {latest.datetime} - 收盘价: {latest.close}")
        
        print("✅ 多时间周期数据获取成功")
        
        # 测试数据更新
        print("\n4. 测试数据更新...")
        engine.update_market_data(symbol, timeframe_data)
        
        # 验证数据已存储
        for timeframe in timeframe_data.keys():
            stored_count = engine.get_data_count(symbol, timeframe)
            print(f"  {timeframe}: 存储了 {stored_count} 条数据")
        
        # 获取最新价格
        latest_price = engine.get_latest_price(symbol)
        print(f"  最新价格: {latest_price}")
        
        print("✅ 数据更新成功")
        
        # 测试数据充足性检查
        print("\n5. 测试数据充足性检查...")
        for timeframe in ["1m", "5m", "15m", "1h"]:
            data_count = engine.get_data_count(symbol, timeframe)
            is_sufficient = engine.is_data_sufficient(symbol, timeframe)
            print(f"  {timeframe}: {data_count} 条数据，{'充足' if is_sufficient else '不足'}")
        
        print("✅ 数据充足性检查完成")
        
        # 测试交易对管理
        print("\n6. 测试交易对管理...")
        initial_count = len(engine.symbols)
        print(f"  初始交易对数量: {initial_count}")
        
        # 添加新交易对
        new_symbol = "DOGE/USDT:USDT"
        engine.add_symbol(new_symbol, ["1m", "5m"])
        print(f"  添加 {new_symbol}，当前数量: {len(engine.symbols)}")
        
        # 移除交易对
        engine.remove_symbol(new_symbol)
        print(f"  移除 {new_symbol}，当前数量: {len(engine.symbols)}")
        
        print("✅ 交易对管理测试完成")
        
        # 测试数据状态
        print("\n7. 测试数据状态...")
        status = engine.get_data_status()
        
        for symbol_name, symbol_status in status.items():
            print(f"  {symbol_name}:")
            print(f"    数据充足: {symbol_status['is_sufficient']}")
            for tf, tf_status in symbol_status['timeframes'].items():
                print(f"    {tf}: {tf_status['count']}/{tf_status['required']}")
        
        print("✅ 数据状态获取成功")
        
        # 测试市场摘要
        print("\n8. 测试市场摘要...")
        summary = engine.get_market_summary()
        
        print(f"  总交易对数: {summary['total_symbols']}")
        print(f"  运行状态: {summary['is_running']}")
        print(f"  支持时间周期: {summary['supported_timeframes']}")
        
        print("✅ 市场摘要获取成功")
        
        print("\n🎉 所有测试通过！市场数据引擎功能正常！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")
    
    return True


if __name__ == "__main__":
    success = test_market_data_engine_basic()
    if success:
        print("\n✅ 市场数据引擎测试全部通过！")
    else:
        print("\n❌ 市场数据引擎测试失败！")
        sys.exit(1)
