#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek AI客户端

此模块负责与DeepSeek API进行交互，包括：
1. API连接和认证
2. 聊天完成请求
3. 响应解析和错误处理
4. 请求限制和重试机制
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
import aiohttp
import requests
from dataclasses import dataclass

from src.utils.logger import get_logger, log_execution_time
from src.utils.exceptions import AIServiceError

logger = get_logger(__name__)


@dataclass
class DeepSeekConfig:
    """DeepSeek配置"""
    api_key: str
    api_base: str = "https://api.deepseek.com"
    model: str = "deepseek-chat"
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class ChatMessage:
    """聊天消息"""
    role: str  # system, user, assistant
    content: str


@dataclass
class ChatResponse:
    """聊天响应"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    created: int


class DeepSeekClient:
    """DeepSeek AI客户端类
    
    负责与DeepSeek API进行交互。
    """
    
    def __init__(self, config: DeepSeekConfig):
        """初始化DeepSeek客户端
        
        Args:
            config: DeepSeek配置
        """
        self.config = config
        self.session = None
        
        # API端点
        self.chat_endpoint = f"{config.api_base}/chat/completions"
        
        # 请求头
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "DeepSeek-Trading-Bot/1.0"
        }
        
        # 请求限制
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 最小请求间隔100ms
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            headers=self.headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _rate_limit(self):
        """请求限制"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _prepare_request_data(self, messages: List[ChatMessage], **kwargs) -> Dict[str, Any]:
        """准备请求数据
        
        Args:
            messages: 聊天消息列表
            **kwargs: 其他参数
        
        Returns:
            Dict[str, Any]: 请求数据
        """
        # 转换消息格式
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        # 构建请求数据
        request_data = {
            "model": kwargs.get("model", self.config.model),
            "messages": formatted_messages,
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "temperature": kwargs.get("temperature", self.config.temperature),
            "top_p": kwargs.get("top_p", self.config.top_p),
            "stream": False
        }
        
        return request_data
    
    @log_execution_time("DeepSeek API请求")
    async def chat_completion_async(self, messages: List[ChatMessage], **kwargs) -> ChatResponse:
        """异步聊天完成请求
        
        Args:
            messages: 聊天消息列表
            **kwargs: 其他参数
        
        Returns:
            ChatResponse: 聊天响应
        
        Raises:
            AIServiceError: AI服务异常
        """
        if not self.session:
            raise AIServiceError("DeepSeek客户端未初始化，请使用async with语句")
        
        request_data = self._prepare_request_data(messages, **kwargs)
        
        for attempt in range(self.config.max_retries):
            try:
                self._rate_limit()
                
                logger.debug(f"发送DeepSeek API请求 (尝试 {attempt + 1}/{self.config.max_retries})")
                
                async with self.session.post(self.chat_endpoint, json=request_data) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        response_data = json.loads(response_text)
                        return self._parse_response(response_data)
                    
                    elif response.status == 429:
                        # 请求限制，等待更长时间
                        wait_time = self.config.retry_delay * (2 ** attempt)
                        logger.warning(f"DeepSeek API请求限制，等待 {wait_time}秒")
                        await asyncio.sleep(wait_time)
                        continue
                    
                    elif response.status >= 500:
                        # 服务器错误，重试
                        wait_time = self.config.retry_delay * (2 ** attempt)
                        logger.warning(f"DeepSeek API服务器错误 {response.status}，等待 {wait_time}秒后重试")
                        await asyncio.sleep(wait_time)
                        continue
                    
                    else:
                        # 其他错误，不重试
                        error_msg = f"DeepSeek API请求失败: {response.status} - {response_text}"
                        logger.error(error_msg)
                        raise AIServiceError(error_msg)
            
            except asyncio.TimeoutError:
                logger.warning(f"DeepSeek API请求超时 (尝试 {attempt + 1}/{self.config.max_retries})")
                if attempt == self.config.max_retries - 1:
                    raise AIServiceError("DeepSeek API请求超时")
                await asyncio.sleep(self.config.retry_delay)
            
            except json.JSONDecodeError as e:
                logger.error(f"DeepSeek API响应JSON解析失败: {e}")
                raise AIServiceError(f"DeepSeek API响应格式错误: {e}")
            
            except Exception as e:
                logger.error(f"DeepSeek API请求异常: {e}")
                if attempt == self.config.max_retries - 1:
                    raise AIServiceError(f"DeepSeek API请求失败: {e}")
                await asyncio.sleep(self.config.retry_delay)
        
        raise AIServiceError("DeepSeek API请求重试次数已用完")
    
    def chat_completion_sync(self, messages: List[ChatMessage], **kwargs) -> ChatResponse:
        """同步聊天完成请求
        
        Args:
            messages: 聊天消息列表
            **kwargs: 其他参数
        
        Returns:
            ChatResponse: 聊天响应
        
        Raises:
            AIServiceError: AI服务异常
        """
        request_data = self._prepare_request_data(messages, **kwargs)
        
        for attempt in range(self.config.max_retries):
            try:
                self._rate_limit()
                
                logger.debug(f"发送DeepSeek API同步请求 (尝试 {attempt + 1}/{self.config.max_retries})")
                
                response = requests.post(
                    self.chat_endpoint,
                    json=request_data,
                    headers=self.headers,
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    return self._parse_response(response_data)
                
                elif response.status_code == 429:
                    # 请求限制，等待更长时间
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    logger.warning(f"DeepSeek API请求限制，等待 {wait_time}秒")
                    time.sleep(wait_time)
                    continue
                
                elif response.status_code >= 500:
                    # 服务器错误，重试
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    logger.warning(f"DeepSeek API服务器错误 {response.status_code}，等待 {wait_time}秒后重试")
                    time.sleep(wait_time)
                    continue
                
                else:
                    # 其他错误，不重试
                    error_msg = f"DeepSeek API请求失败: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    raise AIServiceError(error_msg)
            
            except requests.exceptions.Timeout:
                logger.warning(f"DeepSeek API请求超时 (尝试 {attempt + 1}/{self.config.max_retries})")
                if attempt == self.config.max_retries - 1:
                    raise AIServiceError("DeepSeek API请求超时")
                time.sleep(self.config.retry_delay)
            
            except requests.exceptions.RequestException as e:
                logger.error(f"DeepSeek API请求异常: {e}")
                if attempt == self.config.max_retries - 1:
                    raise AIServiceError(f"DeepSeek API请求失败: {e}")
                time.sleep(self.config.retry_delay)
            
            except json.JSONDecodeError as e:
                logger.error(f"DeepSeek API响应JSON解析失败: {e}")
                raise AIServiceError(f"DeepSeek API响应格式错误: {e}")
            
            except Exception as e:
                logger.error(f"DeepSeek API请求异常: {e}")
                if attempt == self.config.max_retries - 1:
                    raise AIServiceError(f"DeepSeek API请求失败: {e}")
                time.sleep(self.config.retry_delay)
        
        raise AIServiceError("DeepSeek API请求重试次数已用完")
    
    def _parse_response(self, response_data: Dict[str, Any]) -> ChatResponse:
        """解析API响应
        
        Args:
            response_data: API响应数据
        
        Returns:
            ChatResponse: 解析后的响应
        
        Raises:
            AIServiceError: 解析失败时抛出异常
        """
        try:
            choices = response_data.get("choices", [])
            if not choices:
                raise AIServiceError("DeepSeek API响应中没有choices")
            
            choice = choices[0]
            message = choice.get("message", {})
            content = message.get("content", "")
            
            if not content:
                raise AIServiceError("DeepSeek API响应内容为空")
            
            return ChatResponse(
                content=content,
                model=response_data.get("model", "unknown"),
                usage=response_data.get("usage", {}),
                finish_reason=choice.get("finish_reason", "unknown"),
                created=response_data.get("created", int(time.time()))
            )
        
        except Exception as e:
            logger.error(f"解析DeepSeek API响应失败: {e}")
            logger.error(f"响应数据: {response_data}")
            raise AIServiceError(f"解析DeepSeek API响应失败: {e}")
    
    def test_connection(self) -> bool:
        """测试连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            test_messages = [
                ChatMessage(role="user", content="Hello, this is a connection test.")
            ]
            
            response = self.chat_completion_sync(test_messages, max_tokens=10)
            logger.info("DeepSeek API连接测试成功")
            return True
        
        except Exception as e:
            logger.error(f"DeepSeek API连接测试失败: {e}")
            return False
