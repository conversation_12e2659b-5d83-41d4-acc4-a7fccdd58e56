#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统配置管理模块

此模块负责管理系统的所有配置参数，包括：
1. 环境变量加载
2. 配置验证
3. 默认值设置
4. 配置类型定义
"""

import os
from pathlib import Path
from typing import List, Optional, Union
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from pydantic_settings import SettingsConfigDict


class Settings(BaseSettings):
    """系统配置类
    
    使用Pydantic进行配置管理和验证，支持从环境变量、.env文件等多种来源加载配置。
    """
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # =============================================================================
    # 环境配置
    # =============================================================================
    environment: str = Field(
        default="development",
        description="运行环境：development（开发）、production（生产）"
    )
    debug_mode: bool = Field(
        default=True,
        description="调试模式，开发环境建议启用"
    )
    hot_reload: bool = Field(
        default=True,
        description="热重载功能，开发环境建议启用"
    )

    # =============================================================================
    # DeepSeek AI API配置
    # =============================================================================
    deepseek_api_key: str = Field(
        default="",
        description="DeepSeek API密钥（可选，建议通过Web界面配置）"
    )
    deepseek_api_base: str = Field(
        default="https://api.deepseek.com/v1",
        description="DeepSeek API基础URL"
    )
    deepseek_model: str = Field(
        default="deepseek-chat",
        description="DeepSeek模型名称"
    )
    
    # =============================================================================
    # 数据库配置
    # =============================================================================
    database_path: str = Field(
        default="data/trading_system.db",
        description="SQLite数据库文件路径"
    )
    # 数据库加密功能已移除，数据以明文存储
    # 敏感信息通过Web界面配置并安全存储
    
    # =============================================================================
    # Web应用配置
    # =============================================================================
    web_host: str = Field(
        default="127.0.0.1",
        description="Web服务器主机地址"
    )
    web_port: int = Field(
        default=8000,
        ge=1024,
        le=65535,
        description="Web服务器端口"
    )
    # web_debug已废弃，使用debug_mode统一控制
    # 为了向后兼容，保留此字段但使用debug_mode的值
    @property
    def web_debug(self) -> bool:
        """Web调试模式（向后兼容，实际使用debug_mode）"""
        return self.debug_mode
    web_secret_key: str = Field(
        default="your-web-secret-key-for-sessions",
        description="Web会话密钥"
    )
    
    # =============================================================================
    # 交易系统默认配置
    # =============================================================================
    default_max_leverage: int = Field(
        default=10,
        ge=1,
        le=100,
        description="默认最大杠杆倍数"
    )
    default_max_position_ratio: float = Field(
        default=0.5,
        ge=0.01,
        le=1.0,
        description="默认最大仓位比例"
    )
    default_opening_confidence_threshold: int = Field(
        default=70,
        ge=0,
        le=100,
        description="默认开仓置信度阈值"
    )
    default_position_confidence_threshold: int = Field(
        default=60,
        ge=0,
        le=100,
        description="默认持仓置信度阈值"
    )
    default_stop_loss_percentage: float = Field(
        default=0.05,
        ge=0.001,
        le=0.5,
        description="默认止损百分比"
    )
    default_take_profit_percentage: float = Field(
        default=0.1,
        ge=0.001,
        le=1.0,
        description="默认止盈百分比"
    )
    
    # =============================================================================
    # 风险控制配置
    # =============================================================================
    min_balance_threshold: float = Field(
        default=100.0,
        ge=0.01,
        description="最小余额阈值"
    )
    max_daily_trades: int = Field(
        default=50,
        ge=1,
        le=1000,
        description="每日最大交易次数"
    )
    max_concurrent_positions: int = Field(
        default=5,
        ge=1,
        le=50,
        description="最大并发持仓数量"
    )
    
    # =============================================================================
    # 系统运行配置
    # =============================================================================
    market_data_polling_interval: int = Field(
        default=60,
        ge=10,
        le=3600,
        description="市场数据轮询间隔（秒）"
    )
    ai_analysis_interval: int = Field(
        default=300,
        ge=60,
        le=3600,
        description="AI分析间隔（秒）"
    )
    
    # =============================================================================
    # 日志配置
    # =============================================================================
    log_level: str = Field(
        default="INFO",
        description="日志级别"
    )
    log_file_max_size: str = Field(
        default="10MB",
        description="日志文件最大大小"
    )
    log_file_backup_count: int = Field(
        default=5,
        ge=1,
        le=100,
        description="日志文件备份数量"
    )
    
    # =============================================================================
    # 开发和测试配置
    # =============================================================================
    environment: str = Field(
        default="development",
        description="运行环境"
    )
    testing: bool = Field(
        default=False,
        description="是否为测试模式"
    )
    mock_trading: bool = Field(
        default=True,
        description="是否启用模拟交易"
    )
    
    # =============================================================================
    # API请求配置
    # =============================================================================
    request_timeout: int = Field(
        default=30,
        ge=5,
        le=300,
        description="API请求超时时间（秒）"
    )
    max_retries: int = Field(
        default=3,
        ge=0,
        le=10,
        description="最大重试次数"
    )
    retry_delay: float = Field(
        default=1.0,
        ge=0.1,
        le=60.0,
        description="重试延迟（秒）"
    )
    
    # =============================================================================
    # CORS配置
    # =============================================================================
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:8000"],
        description="CORS允许的源"
    )
    cors_allow_credentials: bool = Field(
        default=True,
        description="CORS是否允许凭据"
    )
    
    # =============================================================================
    # 会话配置
    # =============================================================================
    session_expire_minutes: int = Field(
        default=1440,
        ge=60,
        le=10080,
        description="会话过期时间（分钟）"
    )
    
    # =============================================================================
    # 验证器
    # =============================================================================
    @validator("log_level")
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是以下之一: {valid_levels}")
        return v.upper()
    
    @validator("environment")
    def validate_environment(cls, v):
        """验证运行环境"""
        valid_envs = ["development", "testing", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f"运行环境必须是以下之一: {valid_envs}")
        return v.lower()
    
    # 数据库加密验证器已移除
    
    # =============================================================================
    # 属性方法
    # =============================================================================
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == "production"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == "testing" or self.testing
    
    @property
    def database_url(self) -> str:
        """获取数据库URL"""
        return f"sqlite:///{self.database_path}"
    
    @property
    def log_file_path(self) -> Path:
        """获取日志文件路径"""
        return Path("logs") / "system.log"
    
    def get_cors_config(self) -> dict:
        """获取CORS配置"""
        return {
            "allow_origins": self.cors_origins,
            "allow_credentials": self.cors_allow_credentials,
            "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["*"],
        }
    
    def validate_all(self) -> List[str]:
        """验证所有配置并返回错误列表"""
        errors = []
        
        # 检查必需的配置
        # 注意：DeepSeek API密钥现在通过Web界面配置，不再在此验证
        
        # 数据库加密检查已移除
        
        # 检查文件路径
        database_dir = Path(self.database_path).parent
        if not database_dir.exists():
            try:
                database_dir.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建数据库目录: {e}")
        
        return errors


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例
    
    Returns:
        Settings: 配置实例
    """
    return settings


def reload_settings() -> Settings:
    """重新加载配置
    
    Returns:
        Settings: 新的配置实例
    """
    global settings
    settings = Settings()
    return settings
