#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易执行功能真实API测试

测试开仓、平仓、止盈止损等交易执行功能。
注意：这是真实的交易测试，会在模拟盘中执行真实的交易操作。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.data.models import ExchangeConfig


async def test_trading_execution():
    """测试交易执行功能"""
    print("=== 交易执行功能测试 ===")
    print("⚠️  注意：这是真实的交易测试，会在模拟盘中执行真实的交易操作")
    
    # 创建配置
    config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建交易所客户端
    exchange_client = ExchangeClient(config)
    
    try:
        # 连接交易所
        print("1. 连接交易所...")
        exchange_client.connect()
        print("✅ 交易所连接成功")
        
        # 获取初始状态
        print("\n2. 获取初始状态...")
        initial_balance = exchange_client.fetch_balance()
        initial_positions = exchange_client.fetch_positions()
        
        print(f"初始USDT余额: {initial_balance.get('USDT', {}).total if 'USDT' in initial_balance else 0}")
        print(f"初始持仓数量: {len(initial_positions)}")
        
        # 选择测试交易对
        test_symbol = "DOGE/USDT:USDT"  # 选择DOGE，价格较低，适合测试
        print(f"测试交易对: {test_symbol}")
        
        # 获取当前价格
        ohlcv_data = exchange_client.fetch_ohlcv_sync(test_symbol, "1m", 1)
        if not ohlcv_data:
            print("❌ 无法获取价格数据，跳过交易测试")
            return False
        
        current_price = ohlcv_data[0].close
        print(f"当前价格: {current_price}")
        
        # 测试仓位大小计算
        print("\n3. 测试仓位大小计算...")
        risk_amount = 10.0  # 风险10 USDT
        entry_price = current_price
        stop_loss_price = current_price * 0.95  # 5%止损
        leverage = 5
        
        position_size = exchange_client.calculate_position_size(
            test_symbol, risk_amount, entry_price, stop_loss_price, leverage
        )
        print(f"建议仓位大小: {position_size}")
        
        # 调整为合理的测试仓位（最小10个DOGE）
        test_amount = max(10.0, min(position_size, 50.0))
        print(f"实际测试仓位: {test_amount}")
        
        # 测试市场价开仓
        print("\n4. 测试市场价开仓...")
        try:
            # 检查是否有相反方向持仓
            has_opposite = exchange_client.ensure_single_direction(test_symbol, 'buy')
            if has_opposite:
                print("检测到相反方向持仓，等待平仓完成...")
                await asyncio.sleep(3)
            
            # 创建市场价买单
            market_order = await exchange_client.create_market_order(
                symbol=test_symbol,
                side='buy',
                amount=test_amount,
                leverage=leverage
            )
            
            print(f"✅ 市场价开仓成功:")
            print(f"  订单ID: {market_order.get('id', 'N/A')}")
            print(f"  交易对: {market_order.get('symbol', 'N/A')}")
            print(f"  方向: {market_order.get('side', 'N/A')}")
            print(f"  数量: {market_order.get('amount', 'N/A')}")
            print(f"  状态: {market_order.get('status', 'N/A')}")
            
            # 等待订单执行
            await asyncio.sleep(2)
            
        except Exception as e:
            print(f"❌ 市场价开仓失败: {e}")
            return False
        
        # 检查持仓
        print("\n5. 检查开仓后持仓...")
        new_positions = exchange_client.fetch_positions()
        target_position = None
        
        for position in new_positions:
            if position.symbol == test_symbol:
                target_position = position
                break
        
        if target_position:
            print(f"✅ 找到新持仓:")
            print(f"  交易对: {target_position.symbol}")
            print(f"  方向: {target_position.side.value}")
            print(f"  数量: {target_position.amount}")
            print(f"  开仓价: {target_position.entry_price}")
            print(f"  当前价: {target_position.current_price}")
            print(f"  未实现盈亏: {target_position.unrealized_pnl}")
        else:
            print("⚠️ 未找到新持仓，可能订单未完全执行")
        
        # 测试限价订单（止盈）
        print("\n6. 测试限价订单（止盈）...")
        if target_position:
            try:
                take_profit_price = target_position.entry_price * 1.05  # 5%止盈
                
                limit_order = await exchange_client.create_limit_order(
                    symbol=test_symbol,
                    side='sell',
                    amount=target_position.amount,
                    price=take_profit_price,
                    leverage=leverage
                )
                
                print(f"✅ 限价止盈订单创建成功:")
                print(f"  订单ID: {limit_order.get('id', 'N/A')}")
                print(f"  止盈价格: {take_profit_price}")
                
                # 等待一段时间
                await asyncio.sleep(2)
                
                # 取消止盈订单（避免意外成交）
                if limit_order.get('id'):
                    try:
                        cancel_result = await exchange_client.cancel_order(
                            limit_order['id'], test_symbol
                        )
                        print(f"✅ 止盈订单已取消: {cancel_result.get('status', 'N/A')}")
                    except Exception as e:
                        print(f"⚠️ 取消止盈订单失败: {e}")
                
            except Exception as e:
                print(f"❌ 创建限价订单失败: {e}")
        
        # 测试止盈止损设置
        print("\n7. 测试止盈止损设置...")
        if target_position:
            try:
                stop_loss_price = target_position.entry_price * 0.95  # 5%止损
                take_profit_price = target_position.entry_price * 1.05  # 5%止盈
                
                sl_tp_result = await exchange_client.set_stop_loss_take_profit(
                    symbol=test_symbol,
                    position_side=target_position.side.value,
                    stop_loss_price=stop_loss_price,
                    take_profit_price=take_profit_price
                )
                
                print(f"✅ 止盈止损设置结果:")
                if 'stop_loss' in sl_tp_result:
                    print(f"  止损订单: {sl_tp_result['stop_loss'].get('id', 'N/A')}")
                if 'take_profit' in sl_tp_result:
                    print(f"  止盈订单: {sl_tp_result['take_profit'].get('id', 'N/A')}")
                if 'stop_loss_error' in sl_tp_result:
                    print(f"  止损错误: {sl_tp_result['stop_loss_error']}")
                if 'take_profit_error' in sl_tp_result:
                    print(f"  止盈错误: {sl_tp_result['take_profit_error']}")
                
            except Exception as e:
                print(f"❌ 设置止盈止损失败: {e}")
        
        # 测试平仓
        print("\n8. 测试平仓...")
        if target_position:
            try:
                # 先取消所有未完成订单
                orders = exchange_client.fetch_orders(test_symbol)
                for order in orders:
                    try:
                        await exchange_client.cancel_order(order.id, test_symbol)
                        print(f"取消订单: {order.id}")
                    except:
                        pass
                
                await asyncio.sleep(1)
                
                # 执行平仓
                close_result = await exchange_client.close_position(
                    symbol=test_symbol,
                    side=target_position.side.value
                )
                
                print(f"✅ 平仓成功:")
                print(f"  订单ID: {close_result.get('id', 'N/A')}")
                print(f"  状态: {close_result.get('status', 'N/A')}")
                
                # 等待平仓完成
                await asyncio.sleep(3)
                
            except Exception as e:
                print(f"❌ 平仓失败: {e}")
        
        # 检查最终状态
        print("\n9. 检查最终状态...")
        final_balance = exchange_client.fetch_balance()
        final_positions = exchange_client.fetch_positions()
        
        print(f"最终USDT余额: {final_balance.get('USDT', {}).total if 'USDT' in final_balance else 0}")
        print(f"最终持仓数量: {len(final_positions)}")
        
        # 检查是否还有测试交易对的持仓
        test_position_exists = any(pos.symbol == test_symbol for pos in final_positions)
        if not test_position_exists:
            print(f"✅ {test_symbol} 持仓已清空")
        else:
            print(f"⚠️ {test_symbol} 仍有持仓")
        
        print("\n🎉 交易执行功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 交易执行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = asyncio.run(test_trading_execution())
    if success:
        print("\n✅ 交易执行功能测试全部通过！")
    else:
        print("\n❌ 交易执行功能测试失败！")
        sys.exit(1)
