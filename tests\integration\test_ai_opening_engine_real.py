#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI开仓引擎真实API测试

使用真实的DeepSeek API和市场数据测试AI开仓决策功能。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.exchange_client import ExchangeClient
from src.core.technical_analysis import TechnicalAnalysisEngine
from src.core.ai_opening_engine import AIOpeningEngine
from src.ai.deepseek_client import DeepSeekConfig
from src.data.models import ExchangeConfig


async def test_ai_opening_engine_real():
    """测试AI开仓引擎"""
    print("=== AI开仓引擎真实API测试 ===")
    print("⚠️  注意：这将使用真实的DeepSeek API进行AI分析")
    
    # 创建交易所配置
    exchange_config = ExchangeConfig(
        exchange_name="okx",
        api_key="15196a7d-0c92-4e6e-9f45-4729e46f8db8",
        secret_key="F5FCE206D2B5D5253A0A28E0EB0528DE",
        passphrase="CAOwei00!",
        sandbox_mode=True
    )
    
    # 创建DeepSeek配置
    deepseek_config = DeepSeekConfig(
        api_key="***********************************",
        model="deepseek-chat",
        temperature=0.3,
        max_tokens=1000
    )
    
    # 创建客户端
    exchange_client = ExchangeClient(exchange_config)
    ta_engine = TechnicalAnalysisEngine()
    ai_engine = AIOpeningEngine(deepseek_config)
    
    try:
        # 1. 测试DeepSeek连接
        print("\n1. 测试DeepSeek API连接...")
        connection_ok = ai_engine.test_connection()
        if connection_ok:
            print("✅ DeepSeek API连接成功")
        else:
            print("❌ DeepSeek API连接失败")
            return False
        
        # 2. 连接交易所并获取数据
        print("\n2. 连接交易所并获取市场数据...")
        exchange_client.connect()
        
        test_symbol = "BTC/USDT:USDT"
        timeframes = ["1m", "5m", "15m", "1h"]
        
        # 获取市场数据
        market_data = {}
        for timeframe in timeframes:
            ohlcv_data = exchange_client.fetch_ohlcv_sync(test_symbol, timeframe, 300)
            market_data[timeframe] = ohlcv_data
            print(f"  {timeframe}: {len(ohlcv_data)} 条K线数据")
        
        # 获取当前价格
        current_price = market_data["1m"][-1].close if market_data["1m"] else 0
        print(f"当前价格: {current_price}")
        
        # 3. 计算技术指标
        print("\n3. 计算技术指标...")
        technical_indicators = ta_engine.analyze_multi_timeframe(market_data)
        
        print(f"计算完成 {len(technical_indicators)} 个时间周期的技术指标")
        for timeframe in technical_indicators:
            print(f"  {timeframe}: 趋势指标 {len(technical_indicators[timeframe].trend_indicators)} 个")
        
        # 4. 测试同步AI分析
        print("\n4. 测试同步AI开仓分析...")
        
        sync_decision = ai_engine.analyze_market_for_opening_sync(
            symbol=test_symbol,
            technical_data=technical_indicators,
            current_price=current_price
        )
        
        print("同步AI分析结果:")
        print(f"  动作: {sync_decision.action}")
        print(f"  置信度: {sync_decision.confidence}%")
        print(f"  风险等级: {sync_decision.risk_level}")
        print(f"  建议杠杆: {sync_decision.suggested_leverage}x")
        print(f"  建议仓位比例: {sync_decision.position_size_ratio}")
        print(f"  分析理由: {sync_decision.reasoning[:200]}...")
        
        if sync_decision.stop_loss_price:
            print(f"  建议止损价: {sync_decision.stop_loss_price}")
        if sync_decision.take_profit_price:
            print(f"  建议止盈价: {sync_decision.take_profit_price}")
        
        # 验证决策结构
        assert sync_decision.action in ["open_long", "open_short", "no_action"]
        assert 0 <= sync_decision.confidence <= 100
        assert sync_decision.risk_level in ["low", "medium", "high"]
        assert 1 <= sync_decision.suggested_leverage <= 20
        assert len(sync_decision.reasoning) > 10
        
        print("✅ 同步AI分析成功")
        
        # 5. 测试异步AI分析
        print("\n5. 测试异步AI开仓分析...")
        
        async_decision = await ai_engine.analyze_market_for_opening(
            symbol=test_symbol,
            technical_data=technical_indicators,
            current_price=current_price
        )
        
        print("异步AI分析结果:")
        print(f"  动作: {async_decision.action}")
        print(f"  置信度: {async_decision.confidence}%")
        print(f"  风险等级: {async_decision.risk_level}")
        print(f"  建议杠杆: {async_decision.suggested_leverage}x")
        print(f"  分析理由: {async_decision.reasoning[:200]}...")
        
        # 验证异步决策结构
        assert async_decision.action in ["open_long", "open_short", "no_action"]
        assert 0 <= async_decision.confidence <= 100
        assert async_decision.risk_level in ["low", "medium", "high"]
        
        print("✅ 异步AI分析成功")
        
        # 6. 测试开仓决策判断
        print("\n6. 测试开仓决策判断...")
        
        should_open = ai_engine.should_open_position(sync_decision)
        print(f"是否应该开仓: {should_open}")
        
        if should_open:
            print(f"满足开仓条件: {sync_decision.action} (置信度: {sync_decision.confidence}%)")
        else:
            print(f"不满足开仓条件: 置信度 {sync_decision.confidence}% 或动作为 {sync_decision.action}")
        
        print("✅ 开仓决策判断完成")
        
        # 7. 测试交易参数计算
        print("\n7. 测试交易参数计算...")
        
        # 模拟可用余额
        available_balance = 1000.0  # 1000 USDT
        
        trading_params = ai_engine.get_trading_parameters(
            decision=sync_decision,
            current_price=current_price,
            available_balance=available_balance
        )
        
        print("交易参数:")
        print(f"  交易方向: {trading_params['side']}")
        print(f"  交易数量: {trading_params['amount']:.6f}")
        print(f"  杠杆倍数: {trading_params['leverage']}x")
        print(f"  仓位比例: {trading_params['position_ratio']:.2%}")
        print(f"  风险等级: {trading_params['risk_level']}")
        print(f"  置信度: {trading_params['confidence']}%")
        
        if trading_params.get('stop_loss_price'):
            print(f"  止损价格: {trading_params['stop_loss_price']}")
        if trading_params.get('take_profit_price'):
            print(f"  止盈价格: {trading_params['take_profit_price']}")
        
        # 验证交易参数
        assert trading_params['side'] in ['buy', 'sell']
        assert trading_params['amount'] > 0
        assert 1 <= trading_params['leverage'] <= 20
        assert 0 < trading_params['position_ratio'] <= 0.8
        
        print("✅ 交易参数计算成功")
        
        # 8. 测试多个交易对分析
        print("\n8. 测试多个交易对分析...")
        
        test_symbols = ["ETH/USDT:USDT", "DOGE/USDT:USDT"]
        
        for symbol in test_symbols:
            try:
                print(f"\n分析 {symbol}...")
                
                # 获取数据
                symbol_market_data = {}
                for timeframe in ["5m", "1h"]:  # 使用较少的时间周期以节省时间
                    ohlcv_data = exchange_client.fetch_ohlcv_sync(symbol, timeframe, 200)
                    symbol_market_data[timeframe] = ohlcv_data
                
                # 计算技术指标
                symbol_indicators = ta_engine.analyze_multi_timeframe(symbol_market_data)
                
                # AI分析
                symbol_price = symbol_market_data["5m"][-1].close
                decision = ai_engine.analyze_market_for_opening_sync(
                    symbol=symbol,
                    technical_data=symbol_indicators,
                    current_price=symbol_price
                )
                
                print(f"  {symbol}: {decision.action} (置信度: {decision.confidence}%)")
                
            except Exception as e:
                print(f"  {symbol} 分析失败: {e}")
        
        print("✅ 多交易对分析完成")
        
        # 9. 性能测试
        print("\n9. 性能测试...")
        
        import time
        start_time = time.time()
        
        # 连续进行3次分析
        for i in range(3):
            decision = ai_engine.analyze_market_for_opening_sync(
                symbol=test_symbol,
                technical_data=technical_indicators,
                current_price=current_price
            )
            print(f"  第{i+1}次分析: {decision.action} (置信度: {decision.confidence}%)")
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 3
        
        print(f"平均分析时间: {avg_time:.2f}秒")
        print("✅ 性能测试完成")
        
        print("\n🎉 所有AI开仓引擎测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ AI开仓引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        exchange_client.disconnect()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    success = asyncio.run(test_ai_opening_engine_real())
    if success:
        print("\n✅ AI开仓引擎测试全部通过！")
    else:
        print("\n❌ AI开仓引擎测试失败！")
        sys.exit(1)
